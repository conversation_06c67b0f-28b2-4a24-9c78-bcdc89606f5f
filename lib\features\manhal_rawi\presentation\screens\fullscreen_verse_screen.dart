import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'dart:async';

import '../../data/models/verse.dart';
import '../../data/models/poem.dart';
import '../providers/manhal_rawi_provider.dart';
import '../widgets/professional_verse_display.dart';
import '../widgets/book_style_verse_display.dart';
import '../widgets/classic_verse_display.dart';
import '../../../../utils/theme/manhal_colors.dart';
import '../../../../utils/helpers/responsive_helper.dart';

/// شاشة عرض القصيدة بملء الشاشة
///
/// تعرض القصيدة بملء الشاشة مع إمكانية التحكم في طريقة العرض
class FullscreenVerseScreen extends StatefulWidget {
  final Poem poem;
  final int initialPage;
  final VerseDisplayType displayType;

  const FullscreenVerseScreen({
    super.key,
    required this.poem,
    this.initialPage = 0,
    required this.displayType,
  });

  @override
  State<FullscreenVerseScreen> createState() => _FullscreenVerseScreenState();
}

class _FullscreenVerseScreenState extends State<FullscreenVerseScreen>
    with SingleTickerProviderStateMixin {
  int _currentPage = 0;
  bool _showControls = true;
  Timer? _controlsTimer;
  late AnimationController _animationController;
  late Animation<double> _controlsAnimation;
  VerseDisplayType _currentDisplayType = VerseDisplayType.professional;

  @override
  void initState() {
    super.initState();
    _currentPage = widget.initialPage;
    _currentDisplayType = widget.displayType;

    // إنشاء متحكم الرسوم المتحركة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _controlsAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    _animationController.value = 1.0;

    // إخفاء شريط الحالة وشريط التنقل
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);

    // بدء مؤقت إخفاء أزرار التحكم
    _startControlsTimer();
  }

  @override
  void dispose() {
    // إعادة إظهار شريط الحالة وشريط التنقل
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

    // إلغاء المؤقت
    _controlsTimer?.cancel();
    _animationController.dispose();

    super.dispose();
  }

  void _startControlsTimer() {
    _controlsTimer?.cancel();
    _controlsTimer = Timer(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _showControls = false;
        });
        _animationController.reverse();
      }
    });
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });

    if (_showControls) {
      _animationController.forward();
      _startControlsTimer();
    } else {
      _animationController.reverse();
    }
  }

  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page;
      _showControls = true;
    });

    _animationController.forward();
    _startControlsTimer();
  }

  void _exitFullscreen() {
    Navigator.of(context).pop(_currentPage);
  }

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<ManhalRawiProvider>();
    final isDarkMode = provider.isDarkMode;

    return Directionality(
      textDirection: TextDirection.rtl, // تأكيد اتجاه RTL للشاشة بأكملها
      child: Scaffold(
        backgroundColor: isDarkMode ? Colors.black : Colors.white,
        body: GestureDetector(
          onTap: _toggleControls,
          behavior: HitTestBehavior.opaque,
          child: Stack(
            children: [
              // عارض الأبيات
              Positioned.fill(
                child: _buildVerseDisplay(
                  _currentDisplayType,
                  widget.poem.verses,
                  isDarkMode,
                ),
              ),

              // أزرار التحكم
              AnimatedBuilder(
                animation: _controlsAnimation,
                builder: (context, child) {
                  return Opacity(
                    opacity: _controlsAnimation.value,
                    child: IgnorePointer(
                      ignoring: !_showControls,
                      child: child,
                    ),
                  );
                },
                child: _buildControls(isDarkMode),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// عرض نافذة حوار لتغيير حجم الخط
  void _showFontSizeDialog(BuildContext context) {
    final isDarkMode = context.read<ManhalRawiProvider>().isDarkMode;
    final provider = context.read<ManhalRawiProvider>();
    double currentFontSize = provider.verseDisplaySettings.fontSize;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return Dialog(
            backgroundColor: isDarkMode ? ManhalColors.blue900 : Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
              side: BorderSide(
                color: isDarkMode
                    ? ManhalColors.gold500.withValues(alpha: 0.3)
                    : ManhalColors.gold300,
                width: 1,
              ),
            ),
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // عنوان الحوار
                    Text(
                      'حجم الخط',
                      style: TextStyle(
                        fontFamily: 'Amiri',
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: isDarkMode
                            ? ManhalColors.gold500
                            : ManhalColors.primary,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 16),

                    // وصف
                    Text(
                      'اختر حجم الخط المناسب',
                      style: TextStyle(
                        fontFamily: 'Amiri',
                        fontSize: 14,
                        color: isDarkMode ? Colors.white : Colors.black87,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 16),

                    // شريط التمرير
                    Slider(
                      value: currentFontSize,
                      min: 12,
                      max: 30,
                      divisions: 18,
                      label: currentFontSize.toInt().toString(),
                      activeColor: isDarkMode
                          ? ManhalColors.gold500
                          : ManhalColors.primary,
                      inactiveColor: isDarkMode
                          ? ManhalColors.gold500.withValues(alpha: 0.3)
                          : ManhalColors.primary.withValues(alpha: 0.3),
                      onChanged: (value) {
                        setState(() {
                          currentFontSize = value;
                        });
                      },
                    ),

                    const SizedBox(height: 16),

                    // مثال على حجم الخط
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: isDarkMode
                            ? ManhalColors.blue800.withValues(alpha: 0.5)
                            : ManhalColors.gold100.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: isDarkMode
                              ? ManhalColors.gold500.withValues(alpha: 0.3)
                              : ManhalColors.gold300,
                          width: 1,
                        ),
                      ),
                      child: Text(
                        'مثال على حجم الخط',
                        style: TextStyle(
                          fontFamily: 'Amiri',
                          fontSize: currentFontSize,
                          color: isDarkMode ? Colors.white : Colors.black87,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),

                    const SizedBox(height: 16),

                    // أزرار الإجراءات
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        TextButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          child: Text(
                            'إلغاء',
                            style: TextStyle(
                              fontFamily: 'Amiri',
                              fontSize: 14,
                              color: isDarkMode
                                  ? ManhalColors.gold300
                                  : ManhalColors.primary,
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        ElevatedButton(
                          onPressed: () {
                            provider.updateFontSize(currentFontSize);
                            Navigator.of(context).pop();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: isDarkMode
                                ? ManhalColors.gold500
                                : ManhalColors.primary,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: Text(
                            'تطبيق',
                            style: TextStyle(
                              fontFamily: 'Amiri',
                              fontSize: 14,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  /// عرض نافذة حوار لتغيير عدد الأبيات في الصفحة
  void _showVersesPerPageDialog(BuildContext context) {
    final isDarkMode = context.read<ManhalRawiProvider>().isDarkMode;
    final provider = context.read<ManhalRawiProvider>();
    int currentVersesPerPage = provider.verseDisplaySettings.versesPerPage;

    // قائمة الخيارات المتاحة لعدد الأبيات - تم تحديثها لتتوافق مع صفحة الإعدادات
    final List<int> availableOptions = [5, 10, 15, 20, 25, 30];

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return Dialog(
            backgroundColor: isDarkMode ? ManhalColors.blue900 : Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
              side: BorderSide(
                color: isDarkMode
                    ? ManhalColors.gold500.withValues(alpha: 0.3)
                    : ManhalColors.gold300,
                width: 1,
              ),
            ),
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // عنوان الحوار
                    Text(
                      'عدد الأبيات في الصفحة',
                      style: TextStyle(
                        fontFamily: 'Amiri',
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: isDarkMode
                            ? ManhalColors.gold500
                            : ManhalColors.primary,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 16),

                    // وصف
                    Text(
                      'اختر عدد الأبيات المناسب',
                      style: TextStyle(
                        fontFamily: 'Amiri',
                        fontSize: 14,
                        color: isDarkMode ? Colors.white : Colors.black87,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 16),

                    // عرض مثال للأبيات
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: isDarkMode
                            ? ManhalColors.blue800.withValues(alpha: 0.5)
                            : ManhalColors.gold100.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: isDarkMode
                              ? ManhalColors.gold500.withValues(alpha: 0.3)
                              : ManhalColors.gold300,
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.menu_book,
                            size: 24,
                            color: isDarkMode
                                ? ManhalColors.gold500
                                : ManhalColors.primary,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'عدد الأبيات: $currentVersesPerPage',
                            style: TextStyle(
                              fontFamily: provider.fontFamily,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: isDarkMode
                                  ? ManhalColors.gold500
                                  : ManhalColors.primary,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 16),

                    // خيارات عدد الأبيات
                    Wrap(
                      alignment: WrapAlignment.center,
                      spacing: 8,
                      runSpacing: 8,
                      children: availableOptions.map((option) {
                        final isSelected = option == currentVersesPerPage;
                        return InkWell(
                          onTap: () {
                            setState(() {
                              currentVersesPerPage = option;
                            });
                          },
                          child: Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? (isDarkMode
                                      ? ManhalColors.gold500
                                      : ManhalColors.primary)
                                  : (isDarkMode
                                      ? ManhalColors.blue800
                                          .withValues(alpha: 0.5)
                                      : Colors.white),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: isDarkMode
                                    ? ManhalColors.gold500
                                    : ManhalColors.primary,
                                width: 1,
                              ),
                            ),
                            child: Center(
                              child: Text(
                                option.toString(),
                                style: TextStyle(
                                  fontFamily: 'Amiri',
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: isSelected
                                      ? (isDarkMode
                                          ? Colors.black
                                          : Colors.white)
                                      : (isDarkMode
                                          ? ManhalColors.gold500
                                          : ManhalColors.primary),
                                ),
                              ),
                            ),
                          ),
                        );
                      }).toList(),
                    ),

                    const SizedBox(height: 16),

                    // أزرار الإجراءات
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        TextButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          child: Text(
                            'إلغاء',
                            style: TextStyle(
                              fontFamily: 'Amiri',
                              fontSize: 14,
                              color: isDarkMode
                                  ? ManhalColors.gold300
                                  : ManhalColors.primary,
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        ElevatedButton(
                          onPressed: () {
                            provider.updateVersesPerPage(currentVersesPerPage);
                            Navigator.of(context).pop();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: isDarkMode
                                ? ManhalColors.gold500
                                : ManhalColors.primary,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: Text(
                            'تطبيق',
                            style: TextStyle(
                              fontFamily: 'Amiri',
                              fontSize: 14,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildVerseDisplay(
    VerseDisplayType displayType,
    List<Verse> verses,
    bool isDarkMode,
  ) {
    switch (displayType) {
      case VerseDisplayType.professional:
        return ProfessionalVerseDisplay(
          verses: verses,
          selectedIndex: _currentPage,
          onVerseSelected: (index) {
            _onPageChanged(index);
          },
          isFullScreen: true,
          onFullScreenToggle: _exitFullscreen,
        );

      case VerseDisplayType.bookStyle:
        return BookStyleVerseDisplay(
          verses: verses,
          selectedIndex: _currentPage,
          onVerseSelected: (index) {
            _onPageChanged(index);
          },
          isFullScreen: true,
          onFullScreenToggle: _exitFullscreen,
          hideControls: true, // إخفاء عناصر التحكم المكررة
        );

      case VerseDisplayType.classic:
        return ClassicVerseDisplay(
          verses: verses,
          selectedIndex: _currentPage,
          onVerseSelected: (index) {
            _onPageChanged(index);
          },
          isFullScreen: true,
          onFullScreenToggle: _exitFullscreen,
        );
    }
  }

  Widget _buildControls(bool isDarkMode) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // تعديل التصميم بناءً على حجم الشاشة
        final double screenWidth = constraints.maxWidth;
        final bool isTablet = screenWidth > 600;

        // تحديد أحجام العناصر باستخدام ResponsiveHelper
        final double iconSize = ResponsiveHelper.getResponsiveIconSize(
          context,
          defaultSize: isTablet ? 28.0 : 24.0,
        );

        final double navIconSize = ResponsiveHelper.getResponsiveIconSize(
          context,
          defaultSize: isTablet ? 24.0 : 20.0,
        );

        final double titleFontSize = ResponsiveHelper.getResponsiveFontSize(
          context,
          fontSize: isTablet ? 22.0 : 18.0,
        );

        final double infoFontSize = ResponsiveHelper.getResponsiveFontSize(
          context,
          fontSize: isTablet ? 16.0 : 14.0,
        );

        // تحديد الهوامش باستخدام ResponsiveHelper
        final EdgeInsets topPadding = EdgeInsets.fromLTRB(
          ResponsiveHelper.getResponsiveSpacing(context,
              defaultValue: isTablet ? 24.0 : 16.0),
          ResponsiveHelper.getResponsiveSpacing(context,
              defaultValue: isTablet ? 48.0 : 40.0),
          ResponsiveHelper.getResponsiveSpacing(context,
              defaultValue: isTablet ? 24.0 : 16.0),
          ResponsiveHelper.getResponsiveSpacing(context,
              defaultValue: isTablet ? 12.0 : 8.0),
        );

        final EdgeInsets bottomPadding = EdgeInsets.fromLTRB(
          ResponsiveHelper.getResponsiveSpacing(context,
              defaultValue: isTablet ? 24.0 : 16.0),
          ResponsiveHelper.getResponsiveSpacing(context,
              defaultValue: isTablet ? 12.0 : 8.0),
          ResponsiveHelper.getResponsiveSpacing(context,
              defaultValue: isTablet ? 24.0 : 16.0),
          ResponsiveHelper.getResponsiveSpacing(context,
              defaultValue: isTablet ? 40.0 : 32.0),
        );

        // تحديد حجم الحاوية باستخدام ResponsiveHelper
        final double containerPadding = ResponsiveHelper.getResponsiveSpacing(
          context,
          defaultValue: isTablet ? 16.0 : 12.0,
        );

        // تعديل المسافة بين أزرار التنقل باستخدام ResponsiveHelper
        final double navigationButtonSpacing =
            ResponsiveHelper.getResponsiveSpacing(
          context,
          defaultValue: isTablet ? 16.0 : 8.0,
        );

        return Column(
          children: [
            // شريط العنوان
            Container(
              width: screenWidth,
              padding: topPadding,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    isDarkMode
                        ? Colors.black.withValues(alpha: 0.7)
                        : Colors.white.withValues(alpha: 0.7),
                    isDarkMode
                        ? Colors.black.withValues(alpha: 0.0)
                        : Colors.white.withValues(alpha: 0.0),
                  ],
                ),
              ),
              child: SafeArea(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // زر الرجوع
                    Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: isDarkMode
                            ? Colors.black.withValues(alpha: 0.3)
                            : Colors.white.withValues(alpha: 0.3),
                      ),
                      child: IconButton(
                        icon: Icon(
                          Icons
                              .arrow_back, // تغيير من arrow_back إلى arrow_forward للتوافق مع اتجاه RTL
                          color: isDarkMode
                              ? ManhalColors.gold500
                              : ManhalColors.primary,
                          size: iconSize,
                        ),
                        onPressed: _exitFullscreen,
                        tooltip: 'الرجوع',
                        padding: EdgeInsets.all(
                          ResponsiveHelper.getResponsiveSpacing(
                            context,
                            defaultValue: isTablet ? 12.0 : 8.0,
                          ),
                        ),
                      ),
                    ),

                    // عنوان القصيدة
                    Expanded(
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: ResponsiveHelper.getResponsiveSpacing(
                            context,
                            defaultValue: isTablet ? 16.0 : 8.0,
                          ),
                        ),
                        child: Text(
                          widget.poem.title,
                          style: TextStyle(
                            fontFamily: 'Amiri',
                            fontSize: titleFontSize,
                            fontWeight: FontWeight.bold,
                            color: isDarkMode
                                ? ManhalColors.gold500
                                : ManhalColors.primary,
                          ),
                          textAlign: TextAlign.center,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),

                    // زر تغيير نمط العرض
                    Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: isDarkMode
                            ? Colors.black.withValues(alpha: 0.3)
                            : Colors.white.withValues(alpha: 0.3),
                      ),
                      child: IconButton(
                        icon: Icon(
                          Icons.view_carousel,
                          color: isDarkMode
                              ? ManhalColors.gold500
                              : ManhalColors.primary,
                          size: iconSize,
                        ),
                        onPressed: () {
                          _showDisplayTypeDialog(context);
                        },
                        tooltip: 'تغيير نمط العرض',
                        padding: EdgeInsets.all(
                          ResponsiveHelper.getResponsiveSpacing(
                            context,
                            defaultValue: isTablet ? 12.0 : 8.0,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const Spacer(),

            // شريط التحكم السفلي
            Container(
              width: screenWidth,
              padding: bottomPadding,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  colors: [
                    isDarkMode
                        ? Colors.black.withValues(alpha: 0.7)
                        : Colors.white.withValues(alpha: 0.7),
                    isDarkMode
                        ? Colors.black.withValues(alpha: 0.0)
                        : Colors.white.withValues(alpha: 0.0),
                  ],
                ),
              ),
              child: SafeArea(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // معلومات الصفحة
                    Container(
                      padding: EdgeInsets.symmetric(
                          horizontal: containerPadding,
                          vertical: screenWidth > 600 ? 8.0 : 4.0),
                      decoration: BoxDecoration(
                        color: isDarkMode
                            ? Colors.black.withValues(alpha: 0.5)
                            : Colors.white.withValues(alpha: 0.5),
                        borderRadius: BorderRadius.circular(
                          ResponsiveHelper.getResponsiveBorderRadius(
                            context,
                            defaultValue: isTablet ? 20.0 : 16.0,
                          ),
                        ),
                        border: Border.all(
                          color: isDarkMode
                              ? ManhalColors.gold500.withValues(alpha: 0.3)
                              : ManhalColors.gold300,
                          width: ResponsiveHelper.getResponsiveBorderWidth(
                            context,
                            defaultValue: isTablet ? 1.5 : 1.0,
                          ),
                        ),
                      ),
                      child: Text(
                        'البيت ${_currentPage + 1} من ${widget.poem.verses.length}',
                        style: TextStyle(
                          fontFamily: 'Amiri',
                          fontSize: infoFontSize,
                          color: isDarkMode
                              ? ManhalColors.gold500
                              : ManhalColors.primary,
                        ),
                      ),
                    ),

                    // أزرار التنقل والإعدادات
                    Row(
                      children: [
                        // زر حجم الخط
                        Container(
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: isDarkMode
                                ? Colors.black.withValues(alpha: 0.3)
                                : Colors.white.withValues(alpha: 0.3),
                          ),
                          child: IconButton(
                            icon: Icon(
                              Icons.text_fields,
                              color: isDarkMode
                                  ? ManhalColors.gold500
                                  : ManhalColors.primary,
                              size: navIconSize,
                            ),
                            onPressed: () {
                              _showFontSizeDialog(context);
                            },
                            tooltip: 'حجم الخط',
                            padding: EdgeInsets.all(
                              ResponsiveHelper.getResponsiveSpacing(
                                context,
                                defaultValue: isTablet ? 12.0 : 8.0,
                              ),
                            ),
                          ),
                        ),

                        SizedBox(width: navigationButtonSpacing),

                        // زر عدد الأبيات في الصفحة
                        Container(
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: isDarkMode
                                ? Colors.black.withValues(alpha: 0.3)
                                : Colors.white.withValues(alpha: 0.3),
                          ),
                          child: IconButton(
                            icon: Icon(
                              Icons.view_agenda,
                              color: isDarkMode
                                  ? ManhalColors.gold500
                                  : ManhalColors.primary,
                              size: navIconSize,
                            ),
                            onPressed: () {
                              _showVersesPerPageDialog(context);
                            },
                            tooltip: 'عدد الأبيات في الصفحة',
                            padding: EdgeInsets.all(
                              ResponsiveHelper.getResponsiveSpacing(
                                context,
                                defaultValue: isTablet ? 12.0 : 8.0,
                              ),
                            ),
                          ),
                        ),

                        SizedBox(width: navigationButtonSpacing),

                        // زر البيت السابق
                        Container(
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: isDarkMode
                                ? Colors.black.withValues(alpha: 0.3)
                                : Colors.white.withValues(alpha: 0.3),
                          ),
                          child: IconButton(
                            icon: Icon(
                              Icons
                                  .arrow_back_ios, // تغيير من arrow_back_ios إلى arrow_forward_ios للتوافق مع اتجاه RTL
                              color: isDarkMode
                                  ? ManhalColors.gold500
                                  : ManhalColors.primary,
                              size: navIconSize,
                            ),
                            onPressed: _currentPage > 0
                                ? () {
                                    _onPageChanged(_currentPage - 1);
                                  }
                                : null,
                            tooltip: 'البيت السابق',
                            padding: EdgeInsets.all(
                              ResponsiveHelper.getResponsiveSpacing(
                                context,
                                defaultValue: isTablet ? 12.0 : 8.0,
                              ),
                            ),
                          ),
                        ),

                        SizedBox(width: navigationButtonSpacing),

                        // زر البيت التالي
                        Container(
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: isDarkMode
                                ? Colors.black.withValues(alpha: 0.3)
                                : Colors.white.withValues(alpha: 0.3),
                          ),
                          child: IconButton(
                            icon: Icon(
                              Icons
                                  .arrow_forward_ios, // تغيير من arrow_forward_ios إلى arrow_back_ios للتوافق مع اتجاه RTL
                              color: isDarkMode
                                  ? ManhalColors.gold500
                                  : ManhalColors.primary,
                              size: navIconSize,
                            ),
                            onPressed:
                                _currentPage < widget.poem.verses.length - 1
                                    ? () {
                                        _onPageChanged(_currentPage + 1);
                                      }
                                    : null,
                            tooltip: 'البيت التالي',
                            padding: EdgeInsets.all(
                              ResponsiveHelper.getResponsiveSpacing(
                                context,
                                defaultValue: isTablet ? 12.0 : 8.0,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showDisplayTypeDialog(BuildContext context) {
    final isDarkMode = context.read<ManhalRawiProvider>().isDarkMode;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: isDarkMode ? ManhalColors.blue900 : Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(
            color: isDarkMode
                ? ManhalColors.gold500.withValues(alpha: 0.3)
                : ManhalColors.gold300,
            width: 1,
          ),
        ),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // عنوان الحوار
                Text(
                  'اختر نمط العرض',
                  style: TextStyle(
                    fontFamily: 'Amiri',
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode
                        ? ManhalColors.gold500
                        : ManhalColors.primary,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 16),

                // النمط الاحترافي
                _buildDisplayTypeOption(
                  context,
                  isDarkMode,
                  title: 'النمط الاحترافي',
                  subtitle: 'عرض الأبيات بطريقة احترافية متقابلة',
                  value: VerseDisplayType.professional,
                  groupValue: _currentDisplayType,
                  onChanged: (value) {
                    setState(() {
                      _currentDisplayType = value!;
                    });
                    Navigator.of(context).pop();
                  },
                ),

                // نمط الكتاب المفتوح
                _buildDisplayTypeOption(
                  context,
                  isDarkMode,
                  title: 'نمط الكتاب المفتوح',
                  subtitle: 'عرض الأبيات على شكل كتاب مفتوح',
                  value: VerseDisplayType.bookStyle,
                  groupValue: _currentDisplayType,
                  onChanged: (value) {
                    setState(() {
                      _currentDisplayType = value!;
                    });
                    Navigator.of(context).pop();
                  },
                ),

                // النمط الكلاسيكي
                _buildDisplayTypeOption(
                  context,
                  isDarkMode,
                  title: 'النمط الكلاسيكي',
                  subtitle: 'عرض الأبيات بطريقة كلاسيكية فاخرة',
                  value: VerseDisplayType.classic,
                  groupValue: _currentDisplayType,
                  onChanged: (value) {
                    setState(() {
                      _currentDisplayType = value!;
                    });
                    Navigator.of(context).pop();
                  },
                ),

                const SizedBox(height: 8),

                // زر الإلغاء
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: Text(
                    'إلغاء',
                    style: TextStyle(
                      fontFamily: 'Amiri',
                      fontSize: 14,
                      color: isDarkMode
                          ? ManhalColors.gold300
                          : ManhalColors.primary,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء خيار نمط العرض
  Widget _buildDisplayTypeOption<T>(
    BuildContext context,
    bool isDarkMode, {
    required String title,
    required String subtitle,
    required T value,
    required T groupValue,
    required ValueChanged<T?> onChanged,
  }) {
    return InkWell(
      onTap: () => onChanged(value),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 4),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: value == groupValue
              ? (isDarkMode
                  ? ManhalColors.gold500.withValues(alpha: 0.2)
                  : ManhalColors.primary.withValues(alpha: 0.1))
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: value == groupValue
                ? (isDarkMode ? ManhalColors.gold500 : ManhalColors.primary)
                : Colors.transparent,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Radio<T>(
              value: value,
              groupValue: groupValue,
              activeColor:
                  isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
              onChanged: onChanged,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontFamily: 'Amiri',
                      fontSize: 16,
                      color: isDarkMode ? Colors.white : Colors.black87,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontFamily: 'Amiri',
                      fontSize: 13,
                      color: isDarkMode ? Colors.white70 : Colors.black54,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
