class BookInfo {
  final String title;
  final String subtitle;
  final int publishYear;
  final String description;
  final String? coverImageUrl;

  BookInfo({
    required this.title,
    required this.subtitle,
    required this.publishYear,
    required this.description,
    this.coverImageUrl,
  });

  factory BookInfo.fromJson(Map<String, dynamic> json) {
    return BookInfo(
      title: json['title'],
      subtitle: json['subtitle'],
      publishYear: json['publishYear'],
      description: json['description'],
      coverImageUrl: json['coverImageUrl'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'subtitle': subtitle,
      'publishYear': publishYear,
      'description': description,
      'coverImageUrl': coverImageUrl,
    };
  }
}
