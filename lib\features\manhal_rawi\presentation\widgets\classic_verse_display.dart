import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'dart:math' as math;

import '../../data/models/verse.dart';
import '../providers/manhal_rawi_provider.dart';
import '../../../../utils/theme/manhal_colors.dart';
import '../../../../utils/theme/islamic_patterns.dart';

/// عارض الأبيات بالنمط الكلاسيكي الفاخر
///
/// يعرض الأبيات بطريقة كلاسيكية فاخرة تشبه الكتب التراثية القديمة
/// مع إمكانية التكبير إلى ملء الشاشة
class ClassicVerseDisplay extends StatefulWidget {
  final List<Verse> verses;
  final int selectedIndex;
  final Function(int)? onVerseSelected;
  final bool showAnimation;
  final bool enableSelection;

  /// دالة يتم استدعاؤها عند تبديل وضع ملء الشاشة
  final Function()? onFullScreenToggle;

  /// حالة وضع ملء الشاشة
  final bool isFullScreen;

  const ClassicVerseDisplay({
    super.key,
    required this.verses,
    this.selectedIndex = 0,
    this.onVerseSelected,
    this.showAnimation = true,
    this.enableSelection = true,
    this.onFullScreenToggle,
    this.isFullScreen = false,
  });

  @override
  State<ClassicVerseDisplay> createState() => _ClassicVerseDisplayState();
}

class _ClassicVerseDisplayState extends State<ClassicVerseDisplay>
    with SingleTickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _animationController;
  late Animation<double> _pageAnimation;
  int _currentIndex = 0;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.selectedIndex;

    // إنشاء متحكم الصفحات
    _pageController = PageController(
      initialPage: _getPageIndex(_currentIndex),
    );

    // إنشاء متحكم الرسوم المتحركة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _pageAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // تحديث عند تغيير نوع الخط أو حجمه في المزود
    // استخدام Provider.of لإعادة بناء الواجهة عند تغيير المزود
    Provider.of<ManhalRawiProvider>(context);

    // إعادة بناء الواجهة عند تغيير نوع الخط أو حجمه
    setState(() {});
  }

  @override
  void didUpdateWidget(ClassicVerseDisplay oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.selectedIndex != _currentIndex) {
      _currentIndex = widget.selectedIndex;

      // الانتقال إلى الصفحة المناسبة
      final pageIndex = _getPageIndex(_currentIndex);
      if (_pageController.hasClients) {
        _pageController.animateToPage(
          pageIndex,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  // الحصول على رقم الصفحة من رقم البيت
  int _getPageIndex(int verseIndex) {
    final versesPerPage = context.read<ManhalRawiProvider>().versesPerPage;
    return verseIndex ~/ versesPerPage;
  }

  void _onPageChanged(int pageIndex) {
    final versesPerPage = context.read<ManhalRawiProvider>().versesPerPage;
    final newIndex = pageIndex * versesPerPage;

    setState(() {
      _currentIndex = newIndex;
    });

    if (widget.onVerseSelected != null) {
      widget.onVerseSelected!(newIndex);
    }

    _animationController.reset();
    _animationController.forward();
  }

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<ManhalRawiProvider>();
    final isDarkMode = provider.isDarkMode;
    final screenSize = MediaQuery.of(context).size;

    // تحديد ما إذا كانت الشاشة صغيرة أو كبيرة
    final isSmallScreen = screenSize.width < 360;
    final isLargeScreen = screenSize.width > 600;

    // تعديل عدد الأبيات في كل صفحة بناءً على حجم الشاشة
    int versesPerPage = provider.versesPerPage;
    if (isSmallScreen) {
      // تقليل عدد الأبيات للشاشات الصغيرة
      versesPerPage = versesPerPage > 2 ? versesPerPage - 1 : versesPerPage;
    } else if (isLargeScreen) {
      // زيادة عدد الأبيات للشاشات الكبيرة
      versesPerPage = versesPerPage + 1;
    }

    // حساب عدد الصفحات
    final totalPages = (widget.verses.length / versesPerPage).ceil();

    return Container(
      decoration: BoxDecoration(
        color: isDarkMode
            ? ManhalColors.blue900
                .withValues(alpha: widget.isFullScreen ? 1.0 : 0.7)
            : Colors.white.withValues(alpha: widget.isFullScreen ? 1.0 : 0.9),
        borderRadius: BorderRadius.circular(widget.isFullScreen ? 0 : 16),
        border: Border.all(
          color: isDarkMode
              ? ManhalColors.gold500.withValues(alpha: 0.3)
              : ManhalColors.gold300,
          width: 1,
        ),
        boxShadow: widget.isFullScreen
            ? []
            : [
                BoxShadow(
                  color: isDarkMode
                      ? Colors.black.withValues(alpha: 0.3)
                      : Colors.black.withValues(alpha: 0.1),
                  blurRadius: 10,
                  spreadRadius: 1,
                ),
              ],
        image: DecorationImage(
          image: AssetImage(
            isDarkMode
                ? 'assets/images/paper_texture_dark.png'
                : 'assets/images/paper_texture_light.png',
          ),
          opacity: 0.1,
          fit: BoxFit.cover,
        ),
      ),
      child: Column(
        children: [
          // شريط العنوان
          _buildHeader(isDarkMode, totalPages),

          // عارض الأبيات
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: _onPageChanged,
              itemCount: totalPages,
              physics: const BouncingScrollPhysics(),
              itemBuilder: (context, pageIndex) {
                // حساب نطاق الأبيات في هذه الصفحة
                final startIndex = pageIndex * versesPerPage;
                final endIndex =
                    math.min(startIndex + versesPerPage, widget.verses.length);
                final pageVerses = widget.verses.sublist(startIndex, endIndex);

                return AnimatedBuilder(
                  animation: _pageAnimation,
                  builder: (context, child) {
                    final animationValue =
                        pageIndex == _getPageIndex(_currentIndex)
                            ? _pageAnimation.value
                            : 1.0;

                    return Opacity(
                      opacity: animationValue,
                      child: Transform.scale(
                        scale: 0.9 + (0.1 * animationValue),
                        child: child,
                      ),
                    );
                  },
                  child: _buildClassicVersePage(
                    context,
                    pageVerses,
                    startIndex,
                    isDarkMode,
                  ),
                );
              },
            ),
          ),

          // مؤشر الصفحات
          _buildPageIndicator(totalPages, isDarkMode),
        ],
      ),
    );
  }

  Widget _buildHeader(bool isDarkMode, int totalPages) {
    final pageIndex =
        _pageController.hasClients ? (_pageController.page?.toInt() ?? 0) : 0;

    // تحديد حجم الشاشة لتكييف العناصر
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isLargeScreen = screenSize.width > 600;

    // تعديل الأحجام بناءً على حجم الشاشة
    final horizontalPadding =
        isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0);
    final verticalPadding = isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0);
    final borderRadius = isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0);
    final containerPadding =
        isSmallScreen ? 8.0 : (isLargeScreen ? 16.0 : 12.0);
    final fontSize = isSmallScreen ? 12.0 : (isLargeScreen ? 16.0 : 14.0);
    final iconSize = isSmallScreen ? 16.0 : (isLargeScreen ? 24.0 : 20.0);
    final buttonPadding = isSmallScreen ? 2.0 : (isLargeScreen ? 6.0 : 4.0);
    final minButtonSize = isSmallScreen ? 28.0 : (isLargeScreen ? 36.0 : 32.0);

    return Container(
      padding: EdgeInsets.symmetric(
          horizontal: horizontalPadding, vertical: verticalPadding),
      decoration: BoxDecoration(
        color: isDarkMode
            ? ManhalColors.blue800.withValues(alpha: 0.7)
            : Colors.white.withValues(alpha: 0.9),
        borderRadius: widget.isFullScreen
            ? BorderRadius.only(
                bottomLeft: Radius.circular(borderRadius),
                bottomRight: Radius.circular(borderRadius),
              )
            : BorderRadius.only(
                topLeft: Radius.circular(borderRadius),
                topRight: Radius.circular(borderRadius),
              ),
        border: Border(
          bottom: BorderSide(
            color: isDarkMode
                ? ManhalColors.gold500.withValues(alpha: 0.3)
                : ManhalColors.gold300,
            width: isSmallScreen ? 0.5 : (isLargeScreen ? 1.5 : 1.0),
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        textDirection:
            TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
        children: [
          // أزرار التحكم (على اليمين في اللغة العربية)
          Row(
            textDirection:
                TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
            children: [
              // زر تغيير حجم الخط
              IconButton(
                icon: Icon(
                  Icons.text_fields,
                  color:
                      isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
                  size: iconSize,
                ),
                onPressed: () {
                  _showFontSizeDialog(context);
                },
                tooltip: 'تغيير حجم الخط',
                padding: EdgeInsets.all(buttonPadding),
                constraints: BoxConstraints(
                  minWidth: minButtonSize,
                  minHeight: minButtonSize,
                ),
              ),

              // زر ملء الشاشة
              if (widget.onFullScreenToggle != null)
                IconButton(
                  icon: Icon(
                    widget.isFullScreen
                        ? Icons.fullscreen_exit
                        : Icons.fullscreen,
                    color: isDarkMode
                        ? ManhalColors.gold500
                        : ManhalColors.primary,
                    size: iconSize,
                  ),
                  onPressed: widget.onFullScreenToggle,
                  tooltip:
                      widget.isFullScreen ? 'إغلاق ملء الشاشة' : 'ملء الشاشة',
                  padding: EdgeInsets.all(buttonPadding),
                  constraints: BoxConstraints(
                    minWidth: minButtonSize,
                    minHeight: minButtonSize,
                  ),
                ),
            ],
          ),

          // معلومات الصفحة (على اليسار في اللغة العربية)
          Container(
            padding: EdgeInsets.symmetric(
                horizontal: containerPadding,
                vertical: isSmallScreen ? 2.0 : (isLargeScreen ? 6.0 : 4.0)),
            decoration: BoxDecoration(
              color: isDarkMode
                  ? ManhalColors.blue900.withValues(alpha: 0.7)
                  : ManhalColors.gold100.withValues(alpha: 0.7),
              borderRadius: BorderRadius.circular(
                  isSmallScreen ? 8.0 : (isLargeScreen ? 16.0 : 12.0)),
              border: Border.all(
                color: isDarkMode
                    ? ManhalColors.gold500.withValues(alpha: 0.3)
                    : ManhalColors.gold300,
                width: isSmallScreen ? 0.5 : (isLargeScreen ? 1.5 : 1.0),
              ),
            ),
            child: Text(
              'صفحة ${pageIndex + 1} من $totalPages',
              style: TextStyle(
                fontFamily: context.read<ManhalRawiProvider>().fontFamily,
                fontSize: fontSize,
                color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
              ),
              textDirection:
                  TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClassicVersePage(
    BuildContext context,
    List<Verse> verses,
    int startIndex,
    bool isDarkMode,
  ) {
    final provider = context.read<ManhalRawiProvider>();
    final fontSize = provider.fontSize;

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode
            ? ManhalColors.blue900.withValues(alpha: 0.5)
            : Colors.white.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isDarkMode
              ? ManhalColors.gold500.withValues(alpha: 0.2)
              : ManhalColors.gold300.withValues(alpha: 0.5),
          width: 1,
        ),
        image: DecorationImage(
          image: AssetImage(
            isDarkMode
                ? 'assets/images/classic_paper_dark.png'
                : 'assets/images/classic_paper_light.png',
          ),
          opacity: 0.2,
          fit: BoxFit.cover,
        ),
      ),
      child: Column(
        children: [
          // زخرفة أعلى الصفحة
          Padding(
            padding: const EdgeInsets.only(top: 4), // تم تقليل المسافة
            child: CustomPaint(
              painter: IslamicPatterns.getFloralPattern(
                isDark: isDarkMode,
                opacity: 0.2,
                complexity: 1,
              ),
              size: const Size(40, 20),
            ),
          ),

          // الأبيات
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.symmetric(
                  horizontal: 12, vertical: 4), // تم تقليل المسافة
              itemCount: verses.length,
              itemBuilder: (context, index) {
                final verseIndex = startIndex + index;
                final verse = verses[index];
                final isSelected = verseIndex == _currentIndex;

                return _buildClassicVerse(
                  verse: verse,
                  verseIndex: verseIndex,
                  isSelected: isSelected,
                  isDarkMode: isDarkMode,
                  fontSize: fontSize,
                );
              },
            ),
          ),

          // زخرفة أسفل الصفحة
          Padding(
            padding: const EdgeInsets.only(bottom: 4), // تم تقليل المسافة
            child: CustomPaint(
              painter: IslamicPatterns.getFloralPattern(
                isDark: isDarkMode,
                opacity: 0.2,
                complexity: 1,
              ),
              size: const Size(40, 20),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClassicVerse({
    required Verse verse,
    required int verseIndex,
    required bool isSelected,
    required bool isDarkMode,
    required double fontSize,
  }) {
    // تحديد حجم الشاشة لتكييف العناصر
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isLargeScreen = screenSize.width > 600;

    // تعديل الأحجام بناءً على حجم الشاشة
    final adjustedFontSize = isSmallScreen
        ? fontSize * 0.9
        : (isLargeScreen ? fontSize * 1.1 : fontSize);

    // تقليل ارتفاع النص والمسافات لجعل الأبيات أكثر تقاربًا
    final textHeight = isSmallScreen
        ? 1.3
        : (isLargeScreen ? 1.5 : 1.4); // تم تقليل ارتفاع النص
    final bottomMargin = isSmallScreen
        ? 6.0
        : (isLargeScreen ? 10.0 : 8.0); // تم تقليل المسافة بين الأبيات
    final containerPadding = isSmallScreen
        ? 4.0
        : (isLargeScreen ? 6.0 : 5.0); // تم تقليل المسافة الداخلية
    final borderRadius = isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0);
    final borderWidth = isSmallScreen ? 0.5 : (isLargeScreen ? 1.5 : 1.0);
    final versePadding = isSmallScreen
        ? 4.0
        : (isLargeScreen ? 6.0 : 5.0); // تم تقليل المسافة الداخلية للأبيات
    final patternSize = isSmallScreen
        ? Size(1.5, 25.0) // تم تقليل ارتفاع النمط الزخرفي
        : (isLargeScreen ? Size(2.5, 40.0) : Size(2.0, 30.0));
    final patternMargin = isSmallScreen
        ? 4.0
        : (isLargeScreen ? 6.0 : 5.0); // تم تقليل المسافة بين النمط والأبيات

    final textStyle = TextStyle(
      fontFamily: context.read<ManhalRawiProvider>().fontFamily,
      fontSize: adjustedFontSize,
      height: textHeight,
      color: isDarkMode ? Colors.white : Colors.black87,
      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
    );

    final decorationColor = isDarkMode
        ? ManhalColors.gold500.withValues(alpha: 0.3)
        : ManhalColors.gold300;

    return GestureDetector(
      onTap: () {
        setState(() {
          _currentIndex = verseIndex;
        });

        if (widget.onVerseSelected != null) {
          widget.onVerseSelected!(verseIndex);
        }
      },
      onLongPress: () {
        // نسخ البيت إلى الحافظة
        final text = '${verse.first} ${verse.second}';
        Clipboard.setData(ClipboardData(text: text)).then((_) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'تم نسخ البيت إلى الحافظة',
                  style: TextStyle(
                    fontFamily: context.read<ManhalRawiProvider>().fontFamily,
                    fontSize:
                        isSmallScreen ? 12.0 : (isLargeScreen ? 16.0 : 14.0),
                  ),
                  textAlign: TextAlign.center,
                ),
                duration: const Duration(seconds: 2),
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(
                      isSmallScreen ? 8.0 : (isLargeScreen ? 12.0 : 10.0)),
                ),
                margin: EdgeInsets.all(
                    isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0)),
              ),
            );
          }
        });
      },
      child: Container(
        margin: EdgeInsets.only(bottom: bottomMargin),
        padding: EdgeInsets.all(containerPadding),
        decoration: BoxDecoration(
          color: isSelected
              ? (isDarkMode
                  ? ManhalColors.blue800.withValues(alpha: 0.5)
                  : ManhalColors.gold100.withValues(alpha: 0.3))
              : Colors.transparent,
          borderRadius: BorderRadius.circular(borderRadius),
          border: Border.all(
            color: isSelected
                ? (isDarkMode
                    ? ManhalColors.gold500.withValues(alpha: 0.5)
                    : ManhalColors.gold300)
                : Colors.transparent,
            width: borderWidth,
          ),
        ),
        child: Column(
          children: [
            // الشطر الأول والثاني
            Row(
              textDirection:
                  TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
              children: [
                // الشطر الأول
                Expanded(
                  child: Container(
                    padding: EdgeInsets.all(versePadding),
                    decoration: BoxDecoration(
                      color: isDarkMode
                          ? ManhalColors.blue900.withValues(alpha: 0.3)
                          : ManhalColors.gold100.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.only(
                        topRight: Radius.circular(borderRadius),
                        bottomRight: Radius.circular(borderRadius),
                      ),
                      border: Border.all(
                        color: decorationColor,
                        width: borderWidth,
                      ),
                    ),
                    child: Text(
                      verse.first,
                      style: textStyle,
                      textAlign: TextAlign.center,
                      textDirection: TextDirection.rtl,
                    ),
                  ),
                ),

                // فاصل زخرفي
                Container(
                  margin: EdgeInsets.symmetric(horizontal: patternMargin),
                  child: CustomPaint(
                    painter: IslamicPatterns.getFloralPattern(
                      isDark: isDarkMode,
                      opacity: 0.3,
                      complexity: 1,
                    ),
                    size: patternSize,
                  ),
                ),

                // الشطر الثاني
                Expanded(
                  child: Container(
                    padding: EdgeInsets.all(versePadding),
                    decoration: BoxDecoration(
                      color: isDarkMode
                          ? ManhalColors.blue900.withValues(alpha: 0.3)
                          : ManhalColors.gold100.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(borderRadius),
                        bottomLeft: Radius.circular(borderRadius),
                      ),
                      border: Border.all(
                        color: decorationColor,
                        width: borderWidth,
                      ),
                    ),
                    child: Text(
                      verse.second,
                      style: textStyle,
                      textAlign: TextAlign.center,
                      textDirection: TextDirection.rtl,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPageIndicator(int totalPages, bool isDarkMode) {
    final currentPage =
        _pageController.hasClients ? (_pageController.page?.toInt() ?? 0) : 0;

    // تحديد حجم الشاشة لتكييف العناصر
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isLargeScreen = screenSize.width > 600;

    // تعديل الأحجام بناءً على حجم الشاشة
    final verticalPadding =
        isSmallScreen ? 4.0 : (isLargeScreen ? 6.0 : 5.0); // تم تقليل المسافة
    final borderWidth = isSmallScreen ? 0.5 : (isLargeScreen ? 1.5 : 1.0);
    final iconSize = isSmallScreen ? 16.0 : (isLargeScreen ? 24.0 : 20.0);
    final fontSize = isSmallScreen ? 12.0 : (isLargeScreen ? 16.0 : 14.0);
    final buttonPadding = isSmallScreen ? 2.0 : (isLargeScreen ? 6.0 : 4.0);
    final minButtonSize = isSmallScreen ? 28.0 : (isLargeScreen ? 36.0 : 32.0);

    return Container(
      padding: EdgeInsets.symmetric(vertical: verticalPadding),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: isDarkMode
                ? ManhalColors.gold500.withValues(alpha: 0.3)
                : ManhalColors.gold300,
            width: borderWidth,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        textDirection:
            TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
        children: [
          // زر الصفحة السابقة (في اللغة العربية يكون على اليسار)
          IconButton(
            icon: Icon(
              Icons.arrow_back_ios,
              color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
              size: iconSize,
            ),
            onPressed: currentPage > 0
                ? () {
                    _pageController.previousPage(
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                  }
                : null,
            padding: EdgeInsets.all(buttonPadding),
            constraints: BoxConstraints(
              minWidth: minButtonSize,
              minHeight: minButtonSize,
            ),
          ),

          // مؤشر الصفحة الحالية
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: isSmallScreen ? 8.0 : (isLargeScreen ? 16.0 : 12.0),
            ),
            child: Text(
              '${currentPage + 1} / $totalPages',
              style: TextStyle(
                fontFamily: context.read<ManhalRawiProvider>().fontFamily,
                fontSize: fontSize,
                color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
              ),
              textDirection:
                  TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
            ),
          ),

          // زر الصفحة التالية (في اللغة العربية يكون على اليمين)
          IconButton(
            icon: Icon(
              Icons.arrow_forward_ios,
              color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
              size: iconSize,
            ),
            onPressed: currentPage < totalPages - 1
                ? () {
                    _pageController.nextPage(
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                  }
                : null,
            padding: EdgeInsets.all(buttonPadding),
            constraints: BoxConstraints(
              minWidth: minButtonSize,
              minHeight: minButtonSize,
            ),
          ),
        ],
      ),
    );
  }

  void _showFontSizeDialog(BuildContext context) {
    final isDarkMode = context.read<ManhalRawiProvider>().isDarkMode;
    final provider = context.read<ManhalRawiProvider>();
    double currentFontSize = provider.fontSize;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            backgroundColor: isDarkMode ? ManhalColors.blue900 : Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
              side: BorderSide(
                color: isDarkMode
                    ? ManhalColors.gold500.withValues(alpha: 0.3)
                    : ManhalColors.gold300,
                width: 1,
              ),
            ),
            title: Text(
              'حجم الخط',
              style: TextStyle(
                fontFamily: context.read<ManhalRawiProvider>().fontFamily,
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
              ),
              textAlign: TextAlign.center,
              textDirection:
                  TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'اختر حجم الخط المناسب',
                  style: TextStyle(
                    fontFamily: context.read<ManhalRawiProvider>().fontFamily,
                    fontSize: 14,
                    color: isDarkMode ? Colors.white70 : Colors.black54,
                  ),
                  textAlign: TextAlign.center,
                  textDirection: TextDirection
                      .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  textDirection: TextDirection
                      .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                  children: [
                    IconButton(
                      icon: Icon(
                        Icons.remove,
                        color: isDarkMode
                            ? ManhalColors.gold500
                            : ManhalColors.primary,
                      ),
                      onPressed: currentFontSize > 12
                          ? () {
                              setState(() {
                                currentFontSize =
                                    math.max(12, currentFontSize - 1);
                              });
                            }
                          : null,
                    ),
                    Container(
                      width: 50,
                      alignment: Alignment.center,
                      child: Text(
                        currentFontSize.toInt().toString(),
                        style: TextStyle(
                          fontFamily:
                              context.read<ManhalRawiProvider>().fontFamily,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: isDarkMode
                              ? ManhalColors.gold500
                              : ManhalColors.primary,
                        ),
                        textDirection: TextDirection
                            .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                      ),
                    ),
                    IconButton(
                      icon: Icon(
                        Icons.add,
                        color: isDarkMode
                            ? ManhalColors.gold500
                            : ManhalColors.primary,
                      ),
                      onPressed: currentFontSize < 30
                          ? () {
                              setState(() {
                                currentFontSize =
                                    math.min(30, currentFontSize + 1);
                              });
                            }
                          : null,
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  'مثال على حجم الخط',
                  style: TextStyle(
                    fontFamily: provider.fontFamily,
                    fontSize: currentFontSize,
                    color: isDarkMode ? Colors.white : Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                  textDirection: TextDirection
                      .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                ),
              ],
            ),
            actions: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                textDirection:
                    TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                children: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: Text(
                      'إلغاء',
                      style: TextStyle(
                        fontFamily:
                            context.read<ManhalRawiProvider>().fontFamily,
                        fontSize: 14,
                        color: isDarkMode
                            ? ManhalColors.gold300
                            : ManhalColors.primary,
                      ),
                      textDirection: TextDirection
                          .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                    ),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      provider.updateFontSize(currentFontSize);
                      Navigator.of(context).pop();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: isDarkMode
                          ? ManhalColors.gold500
                          : ManhalColors.primary,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      'تأكيد',
                      style: TextStyle(
                        fontFamily:
                            context.read<ManhalRawiProvider>().fontFamily,
                        fontSize: 14,
                      ),
                      textDirection: TextDirection
                          .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                    ),
                  ),
                ],
              ),
            ],
          );
        },
      ),
    );
  }
}
