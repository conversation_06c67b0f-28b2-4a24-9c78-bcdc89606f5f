import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

/// مساعد لفتح الروابط الخارجية
class UrlLauncherHelper {
  /// فتح رابط في المتصفح الافتراضي
  static Future<void> launchURL(String url, {BuildContext? context}) async {
    final Uri uri = Uri.parse(url);
    final BuildContext? localContext = context; // Capture context locally

    try {
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        if (localContext != null && localContext.mounted) {
          ScaffoldMessenger.of(localContext).showSnackBar(
            SnackBar(
              content: Text(
                'لا يمكن فتح الرابط: $url',
                style: const TextStyle(
                  fontFamily: 'Amiri',
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
              duration: const Duration(seconds: 2),
              behavior: SnackBarBehavior.floating,
            ),
          );
        }

        debugPrint('لا يمكن فتح الرابط: $url');
      }
    } catch (e) {
      if (localContext != null && localContext.mounted) {
        ScaffoldMessenger.of(localContext).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ أثناء فتح الرابط: $e',
              style: const TextStyle(
                fontFamily: 'Amiri',
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }

      debugPrint('حدث خطأ أثناء فتح الرابط: $e');
    }
  }

  /// فتح رابط تلجرام
  static Future<void> openTelegramChannel(String username,
      {BuildContext? context}) async {
    // محاولة فتح التطبيق أولاً
    String url = 'tg://resolve?domain=$username';
    final Uri appUri = Uri.parse(url);
    final BuildContext? localContext = context; // Capture context locally

    try {
      if (await canLaunchUrl(appUri)) {
        await launchUrl(appUri);
      } else {
        // إذا فشل فتح التطبيق، نفتح الرابط في المتصفح
        url = 'https://t.me/$username';
        if (localContext != null && localContext.mounted) {
          await launchURL(url, context: localContext);
        } else {
          await launchURL(url);
        }
      }
    } catch (e) {
      // إذا فشل فتح التطبيق، نفتح الرابط في المتصفح
      url = 'https://t.me/$username';
      if (localContext != null && localContext.mounted) {
        await launchURL(url, context: localContext);
      } else {
        await launchURL(url);
      }
    }
  }
}
