import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/manhal_rawi_provider.dart';
import '../widgets/poem_card.dart';
import '../widgets/poet_card.dart';
import '../widgets/category_selector.dart';
import '../widgets/book_info_card.dart';
import 'poem_details_screen.dart';
import 'poet_details_screen.dart';
import 'search_screen.dart';
import 'settings_screen.dart';
import '../../../../utils/theme/manhal_colors.dart';
import '../../../../utils/theme/typography.dart';
import '../../../../utils/theme/decorations.dart';

class ManhalRawiHome extends StatefulWidget {
  const ManhalRawiHome({super.key});

  @override
  State<ManhalRawiHome> createState() => _ManhalRawiHomeState();
}

class _ManhalRawiHomeState extends State<ManhalRawiHome> {
  @override
  void initState() {
    super.initState();
    // تحميل البيانات الأولية
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ManhalRawiProvider>().loadInitialData();
    });
  }

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<ManhalRawiProvider>();
    final isDarkMode = provider.isDarkMode;
    final status = provider.status;

    return Scaffold(
      body: Container(
        decoration: ManhalDecorations.backgroundDecoration(isDark: isDarkMode),
        child: SafeArea(
          child: Column(
            children: [
              _buildAppBar(context),
              if (status == ManhalRawiStatus.loading)
                const Expanded(
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                )
              else if (status == ManhalRawiStatus.error)
                Expanded(
                  child: Center(
                    child: Text(
                      'حدث خطأ: ${provider.errorMessage}',
                      style: ManhalTypography.bodyMedium.copyWith(
                        color: Colors.red,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                )
              else
                Expanded(
                  child: RefreshIndicator(
                    onRefresh: () => provider.loadInitialData(),
                    child: ListView(
                      children: [
                        const SizedBox(height: 16),

                        // معلومات الكتاب
                        if (provider.bookInfo != null)
                          BookInfoCard(
                            bookInfo: provider.bookInfo!,
                            isDarkMode: isDarkMode,
                          ),

                        const SizedBox(height: 24),

                        // قسم الشعراء المميزين
                        _buildFeaturedPoetsSection(context),

                        const SizedBox(height: 24),

                        // قسم التصنيفات
                        _buildCategoriesSection(context),

                        const SizedBox(height: 24),

                        // قسم القصائد
                        _buildPoemsSection(context),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar(BuildContext context) {
    final provider = context.watch<ManhalRawiProvider>();
    final isDarkMode = provider.isDarkMode;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: isDarkMode
            ? ManhalColors.blue900.withValues(alpha: 0.7)
            : Colors.white.withValues(alpha: 0.9),
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withValues(alpha: 0.2)
                : Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // زر الإعدادات
          IconButton(
            icon: Icon(
              Icons.settings,
              color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
            ),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SettingsScreen(),
                ),
              );
            },
          ),

          // عنوان التطبيق
          Column(
            children: [
              Text(
                'المنهل الروي',
                style: ManhalTypography.headingMedium.copyWith(
                  color:
                      isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
                ),
              ),
              Text(
                'محمد هزاع باعلوي الحضرمي',
                style: ManhalTypography.bodySmall.copyWith(
                  color:
                      isDarkMode ? ManhalColors.gold300 : ManhalColors.gold600,
                ),
              ),
            ],
          ),

          // زر البحث
          IconButton(
            icon: Icon(
              Icons.search,
              color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
            ),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SearchScreen(),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturedPoetsSection(BuildContext context) {
    final provider = context.watch<ManhalRawiProvider>();
    final poets = provider.poets;
    final selectedPoetId = provider.selectedPoetId;

    // التأكد من وجود شاعر واحد على الأقل
    if (poets.isEmpty) {
      return const SizedBox.shrink();
    }

    // الحصول على الشاعر (محمد هزاع باعلوي)
    final poet = poets.first;
    final isSelected = selectedPoetId == poet.id;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'المؤلف',
                style: ManhalTypography.headingSmall.copyWith(
                  color: provider.isDarkMode
                      ? ManhalColors.textLight
                      : ManhalColors.textDark,
                ),
              ),
              if (selectedPoetId != null)
                TextButton(
                  onPressed: () {
                    provider.filterByPoet(null);
                  },
                  child: Text(
                    'عرض الكل',
                    style: ManhalTypography.bodySmall.copyWith(
                      color: provider.isDarkMode
                          ? ManhalColors.gold500
                          : ManhalColors.primary,
                    ),
                  ),
                ),
            ],
          ),
        ),

        const SizedBox(height: 8),

        // عرض بطاقة الشاعر في المنتصف
        Center(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: PoetCard(
              poet: poet,
              isSelected: isSelected,
              onTap: () {
                if (isSelected) {
                  provider.filterByPoet(null);
                } else {
                  provider.filterByPoet(poet.id);

                  // الانتقال إلى صفحة تفاصيل الشاعر
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => PoetDetailsScreen(poet: poet),
                    ),
                  );
                }
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCategoriesSection(BuildContext context) {
    final provider = context.watch<ManhalRawiProvider>();
    final categories = provider.categories;

    if (categories.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            'التصنيفات',
            style: ManhalTypography.headingSmall.copyWith(
              color: provider.isDarkMode
                  ? ManhalColors.textLight
                  : ManhalColors.textDark,
            ),
          ),
        ),
        const SizedBox(height: 8),
        CategorySelector(categories: categories),
      ],
    );
  }

  Widget _buildPoemsSection(BuildContext context) {
    final provider = context.watch<ManhalRawiProvider>();
    final poems = provider.filteredPoems;
    final poets = provider.poets;
    final isDarkMode = provider.isDarkMode;

    // عنوان القسم
    String sectionTitle = 'القصائد';
    if (provider.selectedCategoryId != null) {
      final categoryName = provider.categories
          .firstWhere((cat) => cat.id == provider.selectedCategoryId)
          .name;
      sectionTitle = 'قصائد $categoryName';
    } else if (provider.selectedPoetId != null) {
      final poetName =
          poets.firstWhere((poet) => poet.id == provider.selectedPoetId).name;
      sectionTitle = 'قصائد $poetName';
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                sectionTitle,
                style: ManhalTypography.headingSmall.copyWith(
                  color: isDarkMode
                      ? ManhalColors.textLight
                      : ManhalColors.textDark,
                ),
              ),
              if (provider.selectedCategoryId != null ||
                  provider.selectedPoetId != null)
                TextButton(
                  onPressed: () {
                    provider.clearFilters();
                  },
                  child: Text(
                    'مسح التصفية',
                    style: ManhalTypography.bodySmall.copyWith(
                      color: isDarkMode
                          ? ManhalColors.gold500
                          : ManhalColors.primary,
                    ),
                  ),
                ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        if (poems.isEmpty)
          Center(
            child: Padding(
              padding: const EdgeInsets.all(32.0),
              child: Text(
                'لا توجد قصائد متاحة',
                style: ManhalTypography.bodyMedium.copyWith(
                  color: isDarkMode
                      ? ManhalColors.textMuted
                      : ManhalColors.textMuted,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          )
        else
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: const EdgeInsets.only(bottom: 16),
            itemCount: poems.length,
            itemBuilder: (context, index) {
              final poem = poems[index];
              final poet = poets.firstWhere((p) => p.id == poem.poetId);

              return PoemCard(
                poem: poem,
                poet: poet,
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => PoemDetailsScreen(
                        poem: poem,
                        poet: poet,
                      ),
                    ),
                  );
                },
              );
            },
          ),
      ],
    );
  }
}
