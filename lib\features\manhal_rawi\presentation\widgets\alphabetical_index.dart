import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/manhal_rawi_provider.dart';
import '../../../../utils/theme/manhal_colors.dart';
import '../../../../utils/theme/islamic_typography.dart';

class AlphabeticalIndex extends StatefulWidget {
  final Function(String) onLetterSelected;
  final Function()? onClearFilter;
  final String? selectedLetter;

  const AlphabeticalIndex({
    super.key,
    required this.onLetterSelected,
    this.onClearFilter,
    this.selectedLetter,
  });

  @override
  State<AlphabeticalIndex> createState() => _AlphabeticalIndexState();
}

class _AlphabeticalIndexState extends State<AlphabeticalIndex> {
  String? _selectedLetter;

  // قائمة الحروف العربية
  final List<String> arabicLetters = [
    'ء', // إضافة الهمزة في بداية الفهرس
    'ا',
    'ب',
    'ت',
    'ث',
    'ج',
    'ح',
    'خ',
    'د',
    'ذ',
    'ر',
    'ز',
    'س',
    'ش',
    'ص',
    'ض',
    'ط',
    'ظ',
    'ع',
    'غ',
    'ف',
    'ق',
    'ك',
    'ل',
    'م',
    'ن',
    'هـ',
    'و',
    'ي'
  ];

  @override
  void initState() {
    super.initState();
    _selectedLetter = widget.selectedLetter;
  }

  @override
  void didUpdateWidget(AlphabeticalIndex oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.selectedLetter != oldWidget.selectedLetter) {
      setState(() {
        _selectedLetter = widget.selectedLetter;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<ManhalRawiProvider>();
    final isDarkMode = provider.isDarkMode;
    final hasActiveFilter = _selectedLetter != null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان الفهرس مع زر إعادة التعيين
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'الفهرس الهجائي',
                style: IslamicTypography.luxurySubtitle(
                  isDark: isDarkMode,
                  fontSize: 20,
                ),
              ),
              if (hasActiveFilter && widget.onClearFilter != null)
                TextButton.icon(
                  onPressed: () {
                    setState(() {
                      _selectedLetter = null;
                    });
                    widget.onClearFilter!();
                  },
                  icon: Icon(
                    Icons.clear,
                    color: isDarkMode
                        ? ManhalColors.gold500
                        : ManhalColors.primary,
                    size: 16,
                  ),
                  label: Text(
                    'إعادة تعيين',
                    style: TextStyle(
                      color: isDarkMode
                          ? ManhalColors.gold500
                          : ManhalColors.primary,
                      fontSize: 14,
                    ),
                  ),
                ),
            ],
          ),
        ),

        // شريط الحروف الهجائية
        Container(
          height: 60,
          margin: const EdgeInsets.symmetric(vertical: 8),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: arabicLetters.length,
            itemBuilder: (context, index) {
              final letter = arabicLetters[index];
              final isSelected = _selectedLetter == letter;

              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedLetter = letter;
                  });
                  widget.onLetterSelected(letter);
                },
                child: Container(
                  width: 50,
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: isSelected
                          ? (isDarkMode
                              ? [ManhalColors.gold600, ManhalColors.gold500]
                              : [ManhalColors.primary, ManhalColors.gold600])
                          : (isDarkMode
                              ? [ManhalColors.blue800, ManhalColors.blue900]
                              : [
                                  Colors.white,
                                  ManhalColors.gold100.withValues(alpha: 0.3)
                                ]),
                    ),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isSelected
                          ? (isDarkMode
                              ? ManhalColors.gold500
                              : ManhalColors.primary)
                          : (isDarkMode
                              ? ManhalColors.blue700
                              : ManhalColors.gold200),
                      width: isSelected ? 2 : 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: isDarkMode
                            ? Colors.black.withValues(alpha: 0.3)
                            : Colors.black.withValues(alpha: 0.1),
                        blurRadius: 4,
                        spreadRadius: 0,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Center(
                    child: Text(
                      letter,
                      style: IslamicTypography.luxuryTitle(
                        isDark: isDarkMode,
                        fontSize: 22,
                      ).copyWith(
                        color: isSelected
                            ? Colors.white
                            : (isDarkMode
                                ? ManhalColors.gold500
                                : ManhalColors.primary),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}
