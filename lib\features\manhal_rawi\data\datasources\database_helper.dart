import 'dart:async';
import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import '../models/poem.dart';
import '../models/poet.dart';
import '../models/category.dart';
import '../models/book_info.dart';
import '../models/verse.dart';
import '../models/mukhammas_verse.dart';
import 'package:flutter/foundation.dart' show kDebugMode;
import '../../../../utils/helpers/error_logger.dart';
import '../../../../utils/helpers/arabic_text_helper.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  factory DatabaseHelper() => _instance;
  DatabaseHelper._internal();

  // مسجل الأخطاء
  final ErrorLogger _errorLogger = ErrorLogger();

  // دالة مساعدة لتحويل List<dynamic> إلى List<Verse>
  List<Verse> _convertToVerses(List<dynamic> versesList) {
    try {
      return versesList.map((verse) {
        // التحقق من أن verse هو Map
        if (verse is Map) {
          return Verse(
            first: verse['first']?.toString() ?? '',
            second: verse['second']?.toString() ?? '',
          );
        } else {
          // إذا لم يكن verse من نوع Map، نعيد Verse افتراضي
          if (kDebugMode) {
            print('تحذير: تم العثور على بيت شعر بتنسيق غير صحيح');
          }
          return Verse(
            first: 'بيت شعر افتراضي',
            second: 'بيت شعر افتراضي',
          );
        }
      }).toList();
    } catch (e, stackTrace) {
      // تسجيل الخطأ
      _errorLogger.logError(
        'DatabaseHelper',
        'خطأ في تحويل الأبيات',
        e,
        stackTrace,
      );

      // في حالة حدوث خطأ، نعيد قائمة فارغة
      return [];
    }
  }

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  // الإصدار الحالي لقاعدة البيانات
  static const int _databaseVersion = 2;

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'manhal_rawi.db');
    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _createDatabase,
      onUpgrade: _upgradeDatabase,
    );
  }

  /// دالة لترقية قاعدة البيانات عند تغيير الإصدار
  Future<void> _upgradeDatabase(
      Database db, int oldVersion, int newVersion) async {
    if (kDebugMode) {
      print(
          'ترقية قاعدة البيانات من الإصدار $oldVersion إلى الإصدار $newVersion');
    }

    // تنفيذ عمليات الترقية بناءً على الإصدار القديم والجديد
    if (oldVersion < 2) {
      // ترقية من أي إصدار أقل من 2 إلى الإصدار 2

      // حفظ المفضلات الحالية قبل إعادة تحميل البيانات
      List<int> favoriteIds = [];
      try {
        final List<Map<String, dynamic>> favorites =
            await db.query('favorites');
        favoriteIds =
            favorites.map<int>((map) => map['poem_id'] as int).toList();
        if (kDebugMode) {
          print('تم حفظ ${favoriteIds.length} قصيدة مفضلة');
        }
      } catch (e) {
        if (kDebugMode) {
          print('خطأ في استرجاع المفضلات: $e');
        }
      }

      // إعادة تحميل البيانات من ملفات JSON
      await _loadInitialData(db);

      // استعادة المفضلات
      for (final poemId in favoriteIds) {
        try {
          await db.insert(
            'favorites',
            {'poem_id': poemId},
            conflictAlgorithm: ConflictAlgorithm.replace,
          );
        } catch (e) {
          if (kDebugMode) {
            print('خطأ في استعادة القصيدة المفضلة $poemId: $e');
          }
        }
      }

      if (kDebugMode) {
        print('تم ترقية قاعدة البيانات بنجاح إلى الإصدار 2');
      }
    }
  }

  Future<void> _createDatabase(Database db, int version) async {
    // إنشاء جدول معلومات الكتاب
    await db.execute('''
      CREATE TABLE book_info(
        id INTEGER PRIMARY KEY,
        title TEXT,
        subtitle TEXT,
        publish_year INTEGER,
        description TEXT,
        cover_image_url TEXT
      )
    ''');

    // إنشاء جدول الشعراء
    await db.execute('''
      CREATE TABLE poets(
        id INTEGER PRIMARY KEY,
        name TEXT,
        full_name TEXT,
        title TEXT,
        era TEXT,
        birth_year INTEGER,
        death_year INTEGER,
        bio TEXT,
        achievements TEXT,
        image_url TEXT
      )
    ''');

    // إنشاء جدول التصنيفات
    await db.execute('''
      CREATE TABLE categories(
        id INTEGER PRIMARY KEY,
        name TEXT,
        description TEXT
      )
    ''');

    // إنشاء جدول القصائد
    await db.execute('''
      CREATE TABLE poems(
        id INTEGER PRIMARY KEY,
        title TEXT,
        poet_id INTEGER,
        category_id INTEGER,
        description TEXT,
        meter TEXT,
        first_letter TEXT,
        verses TEXT,
        mukhammas_verses TEXT,
        type TEXT DEFAULT 'regular',
        is_favorite INTEGER DEFAULT 0,
        FOREIGN KEY (poet_id) REFERENCES poets (id),
        FOREIGN KEY (category_id) REFERENCES categories (id)
      )
    ''');

    // إنشاء جدول المفضلات
    await db.execute('''
      CREATE TABLE favorites(
        poem_id INTEGER PRIMARY KEY,
        FOREIGN KEY (poem_id) REFERENCES poems (id)
      )
    ''');

    // تهيئة قاعدة البيانات بالبيانات الأولية
    await _loadInitialData(db);
  }

  Future<void> _loadInitialData(Database db) async {
    try {
      if (kDebugMode) {
        print('جاري تحميل البيانات من ملفات JSON...');
      }

      // تحميل البيانات من ملف JSON الرئيسي
      String jsonString = '';
      try {
        jsonString =
            await rootBundle.loadString('assets/data/manhal_rawi_poems.json');
        if (kDebugMode) {
          print('تم تحميل ملف القصائد العادية بنجاح');
        }
      } catch (e) {
        if (kDebugMode) {
          print('خطأ في تحميل ملف القصائد العادية: $e');
        }
        throw Exception('فشل في تحميل ملف القصائد العادية');
      }

      // تحميل بيانات القصائد المخمسة إذا كانت موجودة
      String mukhammasJsonString = '';
      try {
        mukhammasJsonString =
            await rootBundle.loadString('assets/data/mukhammas_example.json');
        if (kDebugMode) {
          print('تم تحميل ملف القصائد المخمسة بنجاح');
        }
      } catch (e) {
        if (kDebugMode) {
          print('لم يتم العثور على ملف القصائد المخمسة: $e');
        }
        // لا نرمي استثناء هنا لأن ملف القصائد المخمسة اختياري
      }

      // التحقق من أن البيانات صالحة
      if (jsonString.isEmpty) {
        throw Exception('ملف JSON فارغ');
      }

      // تحليل البيانات من ملف JSON الرئيسي
      Map<String, dynamic> data;
      try {
        data = json.decode(jsonString);
        if (kDebugMode) {
          print('تم تحليل ملف القصائد العادية بنجاح');
        }
      } catch (e) {
        if (kDebugMode) {
          print('خطأ في تحليل ملف القصائد العادية: $e');
        }
        throw Exception(
            'فشل في تحليل ملف القصائد العادية: تنسيق JSON غير صالح');
      }

      // التحقق من وجود جميع المفاتيح المطلوبة
      if (!data.containsKey('bookInfo') ||
          !data.containsKey('poets') ||
          !data.containsKey('categories') ||
          !data.containsKey('poems')) {
        throw Exception('بنية ملف JSON غير صحيحة: مفاتيح مطلوبة مفقودة');
      }

      // إدخال معلومات الكتاب
      final bookInfo = data['bookInfo'] as Map<String, dynamic>;
      if (bookInfo.isEmpty) {
        throw Exception('معلومات الكتاب غير موجودة');
      }

      if (kDebugMode) {
        print('جاري إدخال معلومات الكتاب...');
      }

      try {
        await db.insert('book_info', {
          'id': 1,
          'title': bookInfo['title'] ?? 'المنهل الراوي',
          'subtitle': bookInfo['subtitle'] ?? 'ديوان شعري أصيل',
          'publish_year': bookInfo['publishYear'] ?? 2010,
          'description': bookInfo['description'] ??
              'كتاب المنهل الراوي هو ديوان شعري يضم مجموعة من القصائد الأصيلة.',
          'cover_image_url': bookInfo['coverImageUrl'],
        });
        if (kDebugMode) {
          print('تم إدخال معلومات الكتاب بنجاح');
        }
      } catch (e) {
        if (kDebugMode) {
          print('خطأ في إدخال معلومات الكتاب: $e');
        }
        throw Exception('فشل في إدخال معلومات الكتاب: $e');
      }

      // إدخال الشعراء
      final List<dynamic> poets = data['poets'] as List<dynamic>;
      if (poets.isEmpty) {
        throw Exception('قائمة الشعراء فارغة');
      }

      if (kDebugMode) {
        print('جاري إدخال بيانات الشعراء (${poets.length} شاعر)...');
      }

      for (var poet in poets) {
        try {
          await db.insert('poets', {
            'id': poet['id'] ?? 1,
            'name': poet['name'] ?? 'محمد هزاع باعلوي',
            'full_name': poet['fullName'] ?? 'محمد هزاع باعلوي الحضرمي',
            'title': poet['title'] ?? 'شاعر وأديب',
            'era': poet['era'] ?? 'العصر الحديث',
            'birth_year': poet['birthYear'] ?? 1950,
            'death_year': poet['deathYear'],
            'bio': poet['bio'] ??
                'محمد هزاع باعلوي الحضرمي هو شاعر وأديب يمني معاصر.',
            'achievements': json
                .encode(poet['achievements'] ?? ['مؤلف كتاب المنهل الراوي']),
            'image_url': poet['imageUrl'],
          });
        } catch (e) {
          if (kDebugMode) {
            print('خطأ في إدخال بيانات الشاعر ${poet['name']}: $e');
          }
          // نستمر في الحلقة حتى لو فشل إدخال شاعر واحد
        }
      }
      if (kDebugMode) {
        print('تم إدخال بيانات الشعراء بنجاح');
      }

      // إدخال التصنيفات
      final List<dynamic> categories = data['categories'] as List<dynamic>;
      if (categories.isEmpty) {
        throw Exception('قائمة التصنيفات فارغة');
      }

      if (kDebugMode) {
        print('جاري إدخال بيانات التصنيفات (${categories.length} تصنيف)...');
      }

      for (var category in categories) {
        try {
          await db.insert('categories', {
            'id': category['id'] ?? 1,
            'name': category['name'] ?? 'تصنيف افتراضي',
            'description': category['description'] ?? 'وصف التصنيف الافتراضي',
          });
        } catch (e) {
          if (kDebugMode) {
            print('خطأ في إدخال بيانات التصنيف ${category['name']}: $e');
          }
          // نستمر في الحلقة حتى لو فشل إدخال تصنيف واحد
        }
      }
      if (kDebugMode) {
        print('تم إدخال بيانات التصنيفات بنجاح');
      }

      // إدخال القصائد العادية
      final List<dynamic> poems = data['poems'] as List<dynamic>;
      if (poems.isEmpty) {
        if (kDebugMode) {
          print('تحذير: قائمة القصائد العادية فارغة');
        }
      } else {
        if (kDebugMode) {
          print('جاري إدخال بيانات القصائد العادية (${poems.length} قصيدة)...');
        }
      }

      // تحميل القصائد المخمسة إذا كانت موجودة
      List<dynamic> mukhammasPoems = [];
      if (mukhammasJsonString.isNotEmpty) {
        try {
          mukhammasPoems = json.decode(mukhammasJsonString) as List<dynamic>;
          if (kDebugMode) {
            print('تم تحليل ملف القصائد المخمسة بنجاح');
            print('عدد القصائد المخمسة: ${mukhammasPoems.length}');
          }
        } catch (e) {
          if (kDebugMode) {
            print('خطأ في تحليل ملف القصائد المخمسة: $e');
          }
          // نستمر حتى لو فشل تحليل ملف القصائد المخمسة
          mukhammasPoems = [];
        }
      }

      // دمج القصائد العادية والمخمسة
      final List<dynamic> allPoems = [...poems, ...mukhammasPoems];
      if (allPoems.isEmpty) {
        throw Exception('لا توجد قصائد للإدخال في قاعدة البيانات');
      }

      if (kDebugMode) {
        print('إجمالي عدد القصائد للإدخال: ${allPoems.length}');
      }

      int successCount = 0;
      int errorCount = 0;

      for (var poem in allPoems) {
        try {
          // التحقق من وجود المعرف
          if (poem['id'] == null) {
            if (kDebugMode) {
              print(
                  'تحذير: القصيدة بدون معرف، سيتم تخطيها: ${poem['title'] ?? 'بدون عنوان'}');
            }
            errorCount++;
            continue;
          }

          // التحقق من نوع القصيدة
          final bool isMukhammas = poem['type'] == 'mukhammas';
          final String poemType = isMukhammas ? 'mukhammas' : 'regular';

          // التحقق من وجود الأبيات المناسبة حسب نوع القصيدة
          if (poemType == 'regular' &&
              (poem['verses'] == null || (poem['verses'] as List).isEmpty)) {
            if (kDebugMode) {
              print(
                  'تحذير: القصيدة العادية ${poem['title']} لا تحتوي على أبيات');
            }
          } else if (poemType == 'mukhammas' &&
              (poem['mukhammas_verses'] == null ||
                  (poem['mukhammas_verses'] as List).isEmpty)) {
            if (kDebugMode) {
              print(
                  'تحذير: القصيدة المخمسة ${poem['title']} لا تحتوي على أبيات مخمسة');
            }
          }

          // إعداد بيانات القصيدة للإدخال في قاعدة البيانات
          final Map<String, dynamic> poemData = {
            'id': poem['id'],
            'title': poem['title'] ?? 'قصيدة بدون عنوان',
            'poet_id': poem['poetId'] ?? 1,
            'category_id': poem['categoryId'] ?? 1,
            'description': poem['description'] ?? 'وصف القصيدة',
            'meter': poem['meter'] ?? 'غير محدد',
            'first_letter': poem['firstLetter'] ?? 'ا',
            'type': poemType,
            'is_favorite': 0,
          };

          // إضافة الأبيات المناسبة حسب نوع القصيدة
          if (poemType == 'regular') {
            final verses = poem['verses'] as List<dynamic>? ?? [];
            poemData['verses'] = json.encode(verses);
            poemData['mukhammas_verses'] = json.encode([]);
          } else if (poemType == 'mukhammas') {
            final mukhammasVerses =
                poem['mukhammas_verses'] as List<dynamic>? ?? [];
            poemData['mukhammas_verses'] = json.encode(mukhammasVerses);
            poemData['verses'] = json.encode([]);
          }

          // إدخال القصيدة في قاعدة البيانات
          await db.insert('poems', poemData);
          successCount++;
        } catch (e) {
          if (kDebugMode) {
            print('خطأ في إدخال القصيدة ${poem['title'] ?? 'بدون عنوان'}: $e');
          }
          errorCount++;
        }
      }

      if (kDebugMode) {
        print('تم إدخال $successCount قصيدة بنجاح');
        if (errorCount > 0) {
          print('فشل إدخال $errorCount قصيدة');
        }
        print('تم تحميل البيانات بنجاح');
      }
    } catch (e, stackTrace) {
      // تسجيل الخطأ
      _errorLogger.logError(
        'DatabaseHelper',
        'فشل في تحميل البيانات الأولية',
        e,
        stackTrace,
      );

      // في حالة فشل تحميل البيانات من الملف، نقوم بإدخال بيانات افتراضية
      if (kDebugMode) {
        print('جاري تحميل البيانات الافتراضية...');
      }
      await _loadDefaultData(db);
    }
  }

  Future<void> _loadDefaultData(Database db) async {
    // إدخال معلومات الكتاب الافتراضية
    await db.insert('book_info', {
      'id': 1,
      'title': 'المنهل الراوي',
      'subtitle': 'ديوان شعري أصيل',
      'publish_year': 2010,
      'description':
          'كتاب المنهل الراوي هو ديوان شعري يضم مجموعة من القصائد الأصيلة.',
      'cover_image_url': null,
    });

    // إدخال شاعر افتراضي
    await db.insert('poets', {
      'id': 1,
      'name': 'محمد هزاع باعلوي',
      'full_name': 'محمد هزاع باعلوي الحضرمي',
      'title': 'شاعر وأديب',
      'era': 'العصر الحديث',
      'birth_year': 1950,
      'death_year': null,
      'bio': 'محمد هزاع باعلوي الحضرمي هو شاعر وأديب يمني معاصر.',
      'achievements': json.encode(['مؤلف كتاب المنهل الراوي']),
      'image_url': null,
    });

    // إدخال تصنيفات افتراضية
    await db.insert('categories', {
      'id': 1,
      'name': 'مدح',
      'description': 'قصائد في مدح الشخصيات البارزة والقيم النبيلة',
    });

    await db.insert('categories', {
      'id': 2,
      'name': 'وطنية',
      'description': 'قصائد في حب الوطن والانتماء',
    });

    await db.insert('categories', {
      'id': 3,
      'name': 'حكمة',
      'description': 'قصائد تحتوي على حكم وأمثال ومواعظ',
    });

    // إدخال قصيدة افتراضية
    await db.insert('poems', {
      'id': 1,
      'title': 'نشيد الوطن',
      'poet_id': 1,
      'category_id': 2,
      'description': 'قصيدة وطنية من ديوان المنهل الراوي.',
      'meter': 'الوافر',
      'first_letter': 'و',
      'verses': json.encode([
        {
          'first': 'وطني الحبيب إليك أشدو بالقوافي',
          'second': 'وأغني للمعالي والشرف'
        }
      ]),
      'is_favorite': 0,
    });
  }

  // استرجاع معلومات الكتاب
  Future<BookInfo> getBookInfo() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('book_info');

    if (maps.isEmpty) {
      throw Exception('لا توجد معلومات للكتاب');
    }

    return BookInfo(
      title: maps[0]['title'],
      subtitle: maps[0]['subtitle'],
      publishYear: maps[0]['publish_year'],
      description: maps[0]['description'],
      coverImageUrl: maps[0]['cover_image_url'],
    );
  }

  // استرجاع الشعراء
  Future<List<Poet>> getPoets() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('poets');

    return List.generate(maps.length, (i) {
      return Poet(
        id: maps[i]['id'],
        name: maps[i]['name'],
        fullName: maps[i]['full_name'],
        title: maps[i]['title'],
        era: maps[i]['era'],
        birthYear: maps[i]['birth_year'],
        deathYear: maps[i]['death_year'],
        bio: maps[i]['bio'],
        achievements: List<String>.from(json.decode(maps[i]['achievements'])),
        imageUrl: maps[i]['image_url'],
      );
    });
  }

  // استرجاع التصنيفات
  Future<List<Category>> getCategories() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('categories');

    return List.generate(maps.length, (i) {
      return Category(
        id: maps[i]['id'],
        name: maps[i]['name'],
        description: maps[i]['description'],
      );
    });
  }

  // استرجاع القصائد
  Future<List<Poem>> getPoems() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('poems');

    return List.generate(maps.length, (i) {
      // تحديد نوع القصيدة
      final poemType = maps[i]['type'] == 'mukhammas'
          ? PoemType.mukhammas
          : PoemType.regular;

      // تحميل الأبيات المناسبة حسب نوع القصيدة
      List<Verse> verses = [];
      List<MukhammasVerse>? mukhammasVerses;

      if (poemType == PoemType.regular) {
        // تحميل الأبيات العادية
        final versesJson = json.decode(maps[i]['verses'] ?? '[]');
        verses = _convertToVerses(versesJson);
      } else {
        // تحميل الأبيات المخمسة
        final mukhammasVersesJson =
            json.decode(maps[i]['mukhammas_verses'] ?? '[]');
        mukhammasVerses = _convertToMukhammasVerses(mukhammasVersesJson);

        // إنشاء أبيات عادية فارغة للتوافق مع الواجهات القديمة
        verses = [];
      }

      return Poem(
        id: maps[i]['id'],
        title: maps[i]['title'],
        poetId: maps[i]['poet_id'],
        categoryId: maps[i]['category_id'],
        description: maps[i]['description'],
        meter: maps[i]['meter'],
        firstLetter: maps[i]['first_letter'],
        verses: verses,
        mukhammasVerses: mukhammasVerses,
        isFavorite: maps[i]['is_favorite'] == 1,
        type: poemType,
      );
    });
  }

  // تحويل JSON إلى قائمة من الأبيات المخمسة
  List<MukhammasVerse> _convertToMukhammasVerses(List<dynamic> versesJson) {
    return versesJson.map((verseJson) {
      return MukhammasVerse(
        firstLine1: verseJson['first_line_1'] ?? '',
        firstLine2: verseJson['first_line_2'] ?? '',
        secondLine1: verseJson['second_line_1'] ?? '',
        secondLine2: verseJson['second_line_2'] ?? '',
        fifthLine: verseJson['fifth_line'] ?? '',
      );
    }).toList();
  }

  // استرجاع القصائد مع حالة المفضلة
  Future<List<Poem>> getPoemsWithFavoriteStatus() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT p.*, CASE WHEN f.poem_id IS NULL THEN 0 ELSE 1 END as is_favorite
      FROM poems p
      LEFT JOIN favorites f ON p.id = f.poem_id
    ''');

    return List.generate(maps.length, (i) {
      // تحديد نوع القصيدة
      final poemType = maps[i]['type'] == 'mukhammas'
          ? PoemType.mukhammas
          : PoemType.regular;

      // تحميل الأبيات المناسبة حسب نوع القصيدة
      List<Verse> verses = [];
      List<MukhammasVerse>? mukhammasVerses;

      if (poemType == PoemType.regular) {
        // تحميل الأبيات العادية
        final versesJson = json.decode(maps[i]['verses'] ?? '[]');
        verses = _convertToVerses(versesJson);
      } else {
        // تحميل الأبيات المخمسة
        final mukhammasVersesJson =
            json.decode(maps[i]['mukhammas_verses'] ?? '[]');
        mukhammasVerses = _convertToMukhammasVerses(mukhammasVersesJson);

        // إنشاء أبيات عادية فارغة للتوافق مع الواجهات القديمة
        verses = [];
      }

      return Poem(
        id: maps[i]['id'],
        title: maps[i]['title'],
        poetId: maps[i]['poet_id'],
        categoryId: maps[i]['category_id'],
        description: maps[i]['description'],
        meter: maps[i]['meter'],
        firstLetter: maps[i]['first_letter'],
        verses: verses,
        mukhammasVerses: mukhammasVerses,
        isFavorite: maps[i]['is_favorite'] == 1,
        type: poemType,
      );
    });
  }

  // استرجاع القصائد حسب الشاعر
  Future<List<Poem>> getPoemsByPoetId(int poetId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT p.*, CASE WHEN f.poem_id IS NULL THEN 0 ELSE 1 END as is_favorite
      FROM poems p
      LEFT JOIN favorites f ON p.id = f.poem_id
      WHERE p.poet_id = ?
    ''', [poetId]);

    return List.generate(maps.length, (i) {
      // تحديد نوع القصيدة
      final poemType = maps[i]['type'] == 'mukhammas'
          ? PoemType.mukhammas
          : PoemType.regular;

      // تحميل الأبيات المناسبة حسب نوع القصيدة
      List<Verse> verses = [];
      List<MukhammasVerse>? mukhammasVerses;

      if (poemType == PoemType.regular) {
        // تحميل الأبيات العادية
        final versesJson = json.decode(maps[i]['verses'] ?? '[]');
        verses = _convertToVerses(versesJson);
      } else {
        // تحميل الأبيات المخمسة
        final mukhammasVersesJson =
            json.decode(maps[i]['mukhammas_verses'] ?? '[]');
        mukhammasVerses = _convertToMukhammasVerses(mukhammasVersesJson);

        // إنشاء أبيات عادية فارغة للتوافق مع الواجهات القديمة
        verses = [];
      }

      return Poem(
        id: maps[i]['id'],
        title: maps[i]['title'],
        poetId: maps[i]['poet_id'],
        categoryId: maps[i]['category_id'],
        description: maps[i]['description'],
        meter: maps[i]['meter'],
        firstLetter: maps[i]['first_letter'],
        verses: verses,
        mukhammasVerses: mukhammasVerses,
        isFavorite: maps[i]['is_favorite'] == 1,
        type: poemType,
      );
    });
  }

  // استرجاع القصائد حسب التصنيف
  Future<List<Poem>> getPoemsByCategoryId(int categoryId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT p.*, CASE WHEN f.poem_id IS NULL THEN 0 ELSE 1 END as is_favorite
      FROM poems p
      LEFT JOIN favorites f ON p.id = f.poem_id
      WHERE p.category_id = ?
    ''', [categoryId]);

    return List.generate(maps.length, (i) {
      // تحديد نوع القصيدة
      final poemType = maps[i]['type'] == 'mukhammas'
          ? PoemType.mukhammas
          : PoemType.regular;

      // تحميل الأبيات المناسبة حسب نوع القصيدة
      List<Verse> verses = [];
      List<MukhammasVerse>? mukhammasVerses;

      if (poemType == PoemType.regular) {
        // تحميل الأبيات العادية
        final versesJson = json.decode(maps[i]['verses'] ?? '[]');
        verses = _convertToVerses(versesJson);
      } else {
        // تحميل الأبيات المخمسة
        final mukhammasVersesJson =
            json.decode(maps[i]['mukhammas_verses'] ?? '[]');
        mukhammasVerses = _convertToMukhammasVerses(mukhammasVersesJson);

        // إنشاء أبيات عادية فارغة للتوافق مع الواجهات القديمة
        verses = [];
      }

      return Poem(
        id: maps[i]['id'],
        title: maps[i]['title'],
        poetId: maps[i]['poet_id'],
        categoryId: maps[i]['category_id'],
        description: maps[i]['description'],
        meter: maps[i]['meter'],
        firstLetter: maps[i]['first_letter'],
        verses: verses,
        mukhammasVerses: mukhammasVerses,
        isFavorite: maps[i]['is_favorite'] == 1,
        type: poemType,
      );
    });
  }

  // البحث في القصائد
  Future<List<Poem>> searchPoems(String query) async {
    // إذا كان النص فارغًا، نعيد قائمة فارغة
    if (query.trim().isEmpty) {
      return [];
    }

    final db = await database;

    // تطبيع نص البحث لإزالة التشكيل وتوحيد أشكال الحروف
    final String normalizedQuery = ArabicTextHelper.normalizeArabicText(query);

    // تحسين البحث ليشمل البحث في محتوى الأبيات
    // البحث باستخدام النص الأصلي والنص المطبع (بدون تشكيل)
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT DISTINCT p.*, CASE WHEN f.poem_id IS NULL THEN 0 ELSE 1 END as is_favorite
      FROM poems p
      LEFT JOIN favorites f ON p.id = f.poem_id
      WHERE p.title LIKE ? OR p.title LIKE ? OR p.description LIKE ? OR p.description LIKE ?
    ''', ['%$query%', '%$normalizedQuery%', '%$query%', '%$normalizedQuery%']);

    // قائمة لتخزين نتائج البحث النهائية
    List<Map<String, dynamic>> finalResults = List.from(maps);

    // البحث في محتوى الأبيات
    final List<Poem> allPoems = await getPoems();

    // البحث في محتوى الأبيات للقصائد العادية والمخمسة
    for (var poem in allPoems) {
      bool foundInVerses = false;

      // البحث في الأبيات العادية
      if (poem.type == PoemType.regular) {
        for (var verse in poem.verses) {
          // البحث باستخدام النص الأصلي
          if (verse.first.contains(query) || verse.second.contains(query)) {
            foundInVerses = true;
            break;
          }

          // البحث باستخدام النص المطبع (بدون تشكيل)
          if (ArabicTextHelper.containsTextIgnoringDiacritics(
                  verse.first, query) ||
              ArabicTextHelper.containsTextIgnoringDiacritics(
                  verse.second, query)) {
            foundInVerses = true;
            break;
          }
        }
      }

      // البحث في الأبيات المخمسة
      if (poem.type == PoemType.mukhammas && poem.mukhammasVerses != null) {
        for (var verse in poem.mukhammasVerses!) {
          // البحث باستخدام النص الأصلي
          if (verse.firstLine1.contains(query) ||
              verse.firstLine2.contains(query) ||
              verse.secondLine1.contains(query) ||
              verse.secondLine2.contains(query) ||
              verse.fifthLine.contains(query)) {
            foundInVerses = true;
            break;
          }

          // البحث باستخدام النص المطبع (بدون تشكيل)
          if (ArabicTextHelper.containsTextIgnoringDiacritics(
                  verse.firstLine1, query) ||
              ArabicTextHelper.containsTextIgnoringDiacritics(
                  verse.firstLine2, query) ||
              ArabicTextHelper.containsTextIgnoringDiacritics(
                  verse.secondLine1, query) ||
              ArabicTextHelper.containsTextIgnoringDiacritics(
                  verse.secondLine2, query) ||
              ArabicTextHelper.containsTextIgnoringDiacritics(
                  verse.fifthLine, query)) {
            foundInVerses = true;
            break;
          }
        }
      }

      // إذا وجدنا تطابقًا في الأبيات ولم تكن القصيدة موجودة بالفعل في النتائج
      if (foundInVerses &&
          !finalResults.any((result) => result['id'] == poem.id)) {
        // إضافة القصيدة إلى النتائج
        final db = await database;
        final poemMap = await db.rawQuery('''
          SELECT p.*, CASE WHEN f.poem_id IS NULL THEN 0 ELSE 1 END as is_favorite
          FROM poems p
          LEFT JOIN favorites f ON p.id = f.poem_id
          WHERE p.id = ?
        ''', [poem.id]);

        if (poemMap.isNotEmpty) {
          finalResults.add(poemMap.first);
        }
      }
    }

    return List.generate(finalResults.length, (i) {
      // تحديد نوع القصيدة
      final poemType = finalResults[i]['type'] == 'mukhammas'
          ? PoemType.mukhammas
          : PoemType.regular;

      // تحميل الأبيات المناسبة حسب نوع القصيدة
      List<Verse> verses = [];
      List<MukhammasVerse>? mukhammasVerses;

      if (poemType == PoemType.regular) {
        // تحميل الأبيات العادية
        final versesJson = json.decode(finalResults[i]['verses'] ?? '[]');
        verses = _convertToVerses(versesJson);
      } else {
        // تحميل الأبيات المخمسة
        final mukhammasVersesJson =
            json.decode(finalResults[i]['mukhammas_verses'] ?? '[]');
        mukhammasVerses = _convertToMukhammasVerses(mukhammasVersesJson);

        // إنشاء أبيات عادية فارغة للتوافق مع الواجهات القديمة
        verses = [];
      }

      return Poem(
        id: finalResults[i]['id'],
        title: finalResults[i]['title'],
        poetId: finalResults[i]['poet_id'],
        categoryId: finalResults[i]['category_id'],
        description: finalResults[i]['description'],
        meter: finalResults[i]['meter'],
        firstLetter: finalResults[i]['first_letter'],
        verses: verses,
        mukhammasVerses: mukhammasVerses,
        isFavorite: finalResults[i]['is_favorite'] == 1,
        type: poemType,
      );
    });
  }

  // إضافة قصيدة إلى المفضلة
  Future<void> addToFavorites(int poemId) async {
    final db = await database;
    await db.insert(
      'favorites',
      {'poem_id': poemId},
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  // إزالة قصيدة من المفضلة
  Future<void> removeFromFavorites(int poemId) async {
    final db = await database;
    await db.delete(
      'favorites',
      where: 'poem_id = ?',
      whereArgs: [poemId],
    );
  }

  // التحقق مما إذا كانت القصيدة مفضلة
  Future<bool> isFavorite(int poemId) async {
    final db = await database;
    final List<Map<String, dynamic>> result = await db.query(
      'favorites',
      where: 'poem_id = ?',
      whereArgs: [poemId],
    );
    return result.isNotEmpty;
  }

  // تبديل حالة المفضلة للقصيدة
  Future<bool> toggleFavorite(int poemId) async {
    final isFav = await isFavorite(poemId);
    if (isFav) {
      await removeFromFavorites(poemId);
      return false;
    } else {
      await addToFavorites(poemId);
      return true;
    }
  }

  // استرجاع القصائد المفضلة
  Future<List<Poem>> getFavoritePoems() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT p.*
      FROM poems p
      INNER JOIN favorites f ON p.id = f.poem_id
    ''');

    return List.generate(maps.length, (i) {
      // تحديد نوع القصيدة
      final poemType = maps[i]['type'] == 'mukhammas'
          ? PoemType.mukhammas
          : PoemType.regular;

      // تحميل الأبيات المناسبة حسب نوع القصيدة
      List<Verse> verses = [];
      List<MukhammasVerse>? mukhammasVerses;

      if (poemType == PoemType.regular) {
        // تحميل الأبيات العادية
        final versesJson = json.decode(maps[i]['verses'] ?? '[]');
        verses = _convertToVerses(versesJson);
      } else {
        // تحميل الأبيات المخمسة
        final mukhammasVersesJson =
            json.decode(maps[i]['mukhammas_verses'] ?? '[]');
        mukhammasVerses = _convertToMukhammasVerses(mukhammasVersesJson);

        // إنشاء أبيات عادية فارغة للتوافق مع الواجهات القديمة
        verses = [];
      }

      return Poem(
        id: maps[i]['id'],
        title: maps[i]['title'],
        poetId: maps[i]['poet_id'],
        categoryId: maps[i]['category_id'],
        description: maps[i]['description'],
        meter: maps[i]['meter'],
        firstLetter: maps[i]['first_letter'],
        verses: verses,
        mukhammasVerses: mukhammasVerses,
        isFavorite: true,
        type: poemType,
      );
    });
  }

  /// مزامنة البيانات من ملفات JSON إلى قاعدة البيانات
  ///
  /// تستخدم هذه الدالة لتحديث البيانات في قاعدة البيانات من ملفات JSON
  /// مع الحفاظ على المفضلات الحالية
  Future<void> syncDatabaseFromJson() async {
    try {
      if (kDebugMode) {
        print('جاري مزامنة البيانات من ملفات JSON...');
      }

      // الحصول على قاعدة البيانات
      final db = await database;

      // حفظ المفضلات الحالية
      List<int> favoriteIds = [];
      try {
        final List<Map<String, dynamic>> favorites =
            await db.query('favorites');
        favoriteIds =
            favorites.map<int>((map) => map['poem_id'] as int).toList();
        if (kDebugMode) {
          print('تم حفظ ${favoriteIds.length} قصيدة مفضلة');
        }
      } catch (e) {
        if (kDebugMode) {
          print('خطأ في استرجاع المفضلات: $e');
        }
      }

      // حذف البيانات الحالية من الجداول مع الاحتفاظ بالجداول نفسها
      await db.delete('poems');
      await db.delete('poets');
      await db.delete('categories');
      await db.delete('book_info');
      await db.delete('favorites');

      // إعادة تحميل البيانات من ملفات JSON
      await _loadInitialData(db);

      // استعادة المفضلات
      for (final poemId in favoriteIds) {
        try {
          await db.insert(
            'favorites',
            {'poem_id': poemId},
            conflictAlgorithm: ConflictAlgorithm.replace,
          );
        } catch (e) {
          if (kDebugMode) {
            print('خطأ في استعادة القصيدة المفضلة $poemId: $e');
          }
        }
      }

      if (kDebugMode) {
        print('تم مزامنة البيانات بنجاح');
      }

      return;
    } catch (e, stackTrace) {
      // تسجيل الخطأ
      _errorLogger.logError(
        'DatabaseHelper',
        'فشل في مزامنة البيانات',
        e,
        stackTrace,
      );

      rethrow;
    }
  }

  // إعادة تهيئة قاعدة البيانات
  Future<void> resetDatabase() async {
    try {
      // إغلاق قاعدة البيانات إذا كانت مفتوحة
      if (_database != null && _database!.isOpen) {
        await _database!.close();
      }
      _database = null;

      // حذف قاعدة البيانات الحالية
      final String path = join(await getDatabasesPath(), 'manhal_rawi.db');
      if (kDebugMode) {
        print('جاري حذف قاعدة البيانات من المسار: $path');
      }

      // التأكد من حذف الملف
      bool exists = await databaseExists(path);
      if (exists) {
        await deleteDatabase(path);
        if (kDebugMode) {
          print('تم حذف قاعدة البيانات القديمة');
        }
      }

      // إعادة إنشاء قاعدة البيانات من الصفر
      if (kDebugMode) {
        print('جاري إنشاء قاعدة بيانات جديدة...');
      }

      final db = await openDatabase(
        path,
        version: _databaseVersion,
        onCreate: (Database db, int version) async {
          if (kDebugMode) {
            print('جاري إنشاء جداول قاعدة البيانات...');
          }

          // إنشاء جدول معلومات الكتاب
          await db.execute('''
            CREATE TABLE book_info(
              id INTEGER PRIMARY KEY,
              title TEXT,
              subtitle TEXT,
              publish_year INTEGER,
              description TEXT,
              cover_image_url TEXT
            )
          ''');

          // إنشاء جدول الشعراء
          await db.execute('''
            CREATE TABLE poets(
              id INTEGER PRIMARY KEY,
              name TEXT,
              full_name TEXT,
              title TEXT,
              era TEXT,
              birth_year INTEGER,
              death_year INTEGER,
              bio TEXT,
              achievements TEXT,
              image_url TEXT
            )
          ''');

          // إنشاء جدول التصنيفات
          await db.execute('''
            CREATE TABLE categories(
              id INTEGER PRIMARY KEY,
              name TEXT,
              description TEXT
            )
          ''');

          // إنشاء جدول القصائد
          await db.execute('''
            CREATE TABLE poems(
              id INTEGER PRIMARY KEY,
              title TEXT,
              poet_id INTEGER,
              category_id INTEGER,
              description TEXT,
              meter TEXT,
              first_letter TEXT,
              verses TEXT,
              mukhammas_verses TEXT,
              type TEXT DEFAULT 'regular',
              is_favorite INTEGER DEFAULT 0,
              FOREIGN KEY (poet_id) REFERENCES poets (id),
              FOREIGN KEY (category_id) REFERENCES categories (id)
            )
          ''');

          // إنشاء جدول المفضلات
          await db.execute('''
            CREATE TABLE favorites(
              poem_id INTEGER PRIMARY KEY,
              FOREIGN KEY (poem_id) REFERENCES poems (id)
            )
          ''');
        },
      );

      _database = db;

      // تحميل البيانات الأولية
      await _loadInitialData(db);

      if (kDebugMode) {
        print('تم إعادة تهيئة قاعدة البيانات بنجاح');
      }

      return;
    } catch (e, stackTrace) {
      // تسجيل الخطأ
      _errorLogger.logError(
        'DatabaseHelper',
        'فشل في إعادة تهيئة قاعدة البيانات',
        e,
        stackTrace,
      );

      rethrow;
    }
  }
}
