import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../../../utils/theme/manhal_colors.dart';
import '../../../../utils/theme/islamic_typography.dart';
import '../../../../utils/theme/islamic_patterns.dart';
import '../../data/models/mukhammas_verse.dart';
import '../providers/manhal_rawi_provider.dart';

/// عرض القصائد المخمسة بتصميم كتب الشعر الفاخرة
class LuxuryMukhammasDisplay extends StatefulWidget {
  /// قائمة الأبيات المخمسة
  final List<MukhammasVerse> verses;

  /// مؤشر البيت المحدد
  final int selectedIndex;

  /// دالة يتم استدعاؤها عند اختيار بيت
  final Function(int) onVerseSelected;

  /// دالة يتم استدعاؤها عند تبديل وضع ملء الشاشة
  final Function()? onFullScreenToggle;

  /// حالة وضع ملء الشاشة
  final bool isFullScreen;

  /// دالة يتم استدعاؤها عند تغيير حالة التفاعل مع المكون
  final Function(bool)? onInteractionStateChanged;

  const LuxuryMukhammasDisplay({
    super.key,
    required this.verses,
    this.selectedIndex = 0,
    required this.onVerseSelected,
    this.onFullScreenToggle,
    this.isFullScreen = false,
    this.onInteractionStateChanged,
  });

  @override
  State<LuxuryMukhammasDisplay> createState() => _LuxuryMukhammasDisplayState();
}

class _LuxuryMukhammasDisplayState extends State<LuxuryMukhammasDisplay> {
  // متغير لتخزين مزود البيانات
  late ManhalRawiProvider _provider;

  // متغير لتخزين وحدة تحكم التمرير
  final ScrollController _scrollController = ScrollController();

  // متغير لتخزين مؤشر الصفحة الحالية
  int _currentPageIndex = 0;

  // متغير لتخزين عدد الأبيات في كل صفحة
  int _versesPerPage = 5;

  // لم نعد نستخدم متغير _totalPages لأننا نحسب عدد الصفحات في كل مرة في دالة build

  // متغير لتخزين حجم الخط الحالي
  late double _fontSize;

  @override
  void initState() {
    super.initState();

    // الحصول على مزود البيانات
    _provider = context.read<ManhalRawiProvider>();

    // تعيين حجم الخط من الإعدادات
    _fontSize = _provider.fontSize;

    // حساب عدد الأبيات في كل صفحة
    _calculateVersesPerPage();
  }

  @override
  void didUpdateWidget(LuxuryMukhammasDisplay oldWidget) {
    super.didUpdateWidget(oldWidget);

    // لا نحتاج إلى إعادة حساب عدد الأبيات هنا لأننا نحسبه في كل مرة في دالة build
    // ولكن يمكننا إعادة ضبط مؤشر الصفحة الحالية إذا تغير وضع ملء الشاشة
    if (widget.isFullScreen != oldWidget.isFullScreen) {
      // إعادة ضبط موضع التمرير
      _scrollController.jumpTo(0);
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // تحديث عند تغيير نوع الخط أو حجمه في المزود
    final provider = Provider.of<ManhalRawiProvider>(context);

    // تحديث حجم الخط إذا تغير في المزود
    if (_fontSize != provider.fontSize) {
      setState(() {
        _fontSize = provider.fontSize;
      });
    }

    // تحديث عدد الأبيات الأساسي إذا تغير في المزود
    if (_versesPerPage != provider.versesPerPage) {
      _calculateVersesPerPage();
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  // حساب عدد الأبيات في كل صفحة
  void _calculateVersesPerPage() {
    // التحقق من وجود أبيات
    if (widget.verses.isEmpty) {
      _versesPerPage = 1;
      return;
    }

    // استخدام عدد الأبيات المحدد من إعدادات المستخدم
    final provider = context.read<ManhalRawiProvider>();
    _versesPerPage = provider.versesPerPage;

    // لا نقوم بحساب _totalPages هنا لأننا نستخدم calculatedTotalPages في دالة build
  }

  // الانتقال إلى الصفحة التالية
  void _nextPage() {
    // نحسب عدد الصفحات الحالي بناءً على عدد الأبيات المعدل
    final provider = context.read<ManhalRawiProvider>();
    int versesPerPage = provider.versesPerPage;

    // تعديل عدد الأبيات بناءً على حجم الشاشة ووضع ملء الشاشة
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isLargeScreen = screenSize.width > 600;
    final isLandscape = screenSize.width > screenSize.height;

    if (widget.isFullScreen) {
      if (isLargeScreen) {
        versesPerPage = (versesPerPage * 1.3).round();
      } else if (isLandscape) {
        versesPerPage = (versesPerPage * 1.2).round();
      } else if (!isSmallScreen) {
        versesPerPage = (versesPerPage * 1.15).round();
      }
    } else {
      if (isSmallScreen) {
        versesPerPage = versesPerPage > 2 ? versesPerPage - 1 : versesPerPage;
      }
    }

    versesPerPage = versesPerPage.clamp(1, 50);
    final calculatedTotalPages = (widget.verses.length / versesPerPage).ceil();

    if (_currentPageIndex < calculatedTotalPages - 1) {
      setState(() {
        _currentPageIndex++;
      });
      _scrollController.jumpTo(0);
    }
  }

  // الانتقال إلى الصفحة السابقة
  void _previousPage() {
    if (_currentPageIndex > 0) {
      setState(() {
        _currentPageIndex--;
      });
      _scrollController.jumpTo(0);
    }
  }

  // زيادة حجم الخط
  void _increaseFontSize() {
    // إخطار بأن المستخدم يتفاعل مع المكون
    widget.onInteractionStateChanged?.call(true);

    setState(() {
      _fontSize = _fontSize + 1.0 > 24.0 ? 24.0 : _fontSize + 1.0;
    });

    // تحديث حجم الخط في الإعدادات
    _provider.setFontSize(_fontSize);

    // إخطار بأن التفاعل انتهى بعد فترة قصيرة
    Future.delayed(const Duration(milliseconds: 500), () {
      widget.onInteractionStateChanged?.call(false);
    });
  }

  // تقليل حجم الخط
  void _decreaseFontSize() {
    // إخطار بأن المستخدم يتفاعل مع المكون
    widget.onInteractionStateChanged?.call(true);

    setState(() {
      _fontSize = _fontSize - 1.0 < 12.0 ? 12.0 : _fontSize - 1.0;
    });

    // تحديث حجم الخط في الإعدادات
    _provider.setFontSize(_fontSize);

    // إخطار بأن التفاعل انتهى بعد فترة قصيرة
    Future.delayed(const Duration(milliseconds: 500), () {
      widget.onInteractionStateChanged?.call(false);
    });
  }

  @override
  Widget build(BuildContext context) {
    // الحصول على مزود البيانات والإعدادات
    final provider = context.watch<ManhalRawiProvider>();
    final isDarkMode = provider.isDarkMode;

    // تحديث حجم الخط إذا تغير في الإعدادات
    if (_fontSize != provider.fontSize) {
      _fontSize = provider.fontSize;
    }

    // الحصول على أبعاد الشاشة
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isLargeScreen = screenSize.width > 600;

    // التحقق من وجود أبيات
    if (widget.verses.isEmpty) {
      return Center(
        child: Text(
          'لا توجد أبيات مخمسة',
          style: IslamicTypography.luxuryTitle(
            isDark: isDarkMode,
            fontSize: isSmallScreen ? 16.0 : (isLargeScreen ? 20.0 : 18.0),
            fontFamily: context.read<ManhalRawiProvider>().fontFamily,
          ),
          textAlign: TextAlign.center,
        ),
      );
    }

    // تعديل عدد الأبيات في كل صفحة بناءً على حجم الشاشة ووضع ملء الشاشة
    int versesPerPage = _versesPerPage;
    final isLandscape = screenSize.width > screenSize.height;

    if (widget.isFullScreen) {
      // زيادة عدد الأبيات في وضع ملء الشاشة
      if (isLargeScreen) {
        // للشاشات الكبيرة، زيادة عدد الأبيات بنسبة 30%
        versesPerPage = (versesPerPage * 1.3).round();
      } else if (isLandscape) {
        // للشاشات في وضع أفقي، زيادة عدد الأبيات بنسبة 20%
        versesPerPage = (versesPerPage * 1.2).round();
      } else if (!isSmallScreen) {
        // للشاشات المتوسطة، زيادة عدد الأبيات بنسبة 15%
        versesPerPage = (versesPerPage * 1.15).round();
      }
    } else {
      // تعديل عدد الأبيات في الوضع العادي
      if (isSmallScreen) {
        // تقليل عدد الأبيات للشاشات الصغيرة
        versesPerPage = versesPerPage > 2 ? versesPerPage - 1 : versesPerPage;
      }
    }

    // التأكد من أن عدد الأبيات لا يقل عن 1
    versesPerPage = versesPerPage.clamp(1, 50);

    // إعادة حساب إجمالي عدد الصفحات بناءً على عدد الأبيات المعدل
    final int calculatedTotalPages =
        (widget.verses.length / versesPerPage).ceil();

    // حساب مؤشر البداية والنهاية للأبيات في هذه الصفحة
    final startIndex = _currentPageIndex * versesPerPage;
    final endIndex = (startIndex + versesPerPage <= widget.verses.length)
        ? startIndex + versesPerPage
        : widget.verses.length;

    // الحصول على الأبيات في هذه الصفحة
    final pageVerses = widget.verses.sublist(startIndex, endIndex);

    // تعديل الأحجام بناءً على حجم الشاشة
    final horizontalPadding =
        isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0);
    final verticalPadding = isSmallScreen ? 3.0 : (isLargeScreen ? 5.0 : 4.0);
    final borderRadius = isSmallScreen ? 16.0 : (isLargeScreen ? 24.0 : 20.0);
    final iconSize = isSmallScreen ? 16.0 : (isLargeScreen ? 24.0 : 20.0);
    final buttonPadding = isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0);
    final minButtonSize = isSmallScreen ? 32.0 : (isLargeScreen ? 40.0 : 36.0);
    final dividerHeight = isSmallScreen ? 20.0 : (isLargeScreen ? 28.0 : 24.0);
    final dividerMargin = isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0);
    final borderWidth = isSmallScreen ? 0.5 : (isLargeScreen ? 1.5 : 1.0);

    // بناء شريط الأدوات
    final toolbar = Container(
      padding: EdgeInsets.symmetric(
          horizontal: horizontalPadding, vertical: verticalPadding),
      decoration: BoxDecoration(
        color: isDarkMode
            ? ManhalColors.blue900.withValues(alpha: 0.8)
            : ManhalColors.gold100.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withValues(alpha: 0.2)
                : Colors.grey.withValues(alpha: 0.2),
            blurRadius: isSmallScreen ? 3.0 : (isLargeScreen ? 7.0 : 5.0),
            offset:
                Offset(0, isSmallScreen ? 1.0 : (isLargeScreen ? 3.0 : 2.0)),
          ),
        ],
        border: Border.all(
          color: isDarkMode
              ? ManhalColors.gold500.withValues(alpha: 0.3)
              : ManhalColors.gold300,
          width: borderWidth,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // زر تقليل حجم الخط
          IconButton(
            icon: Icon(
              Icons.text_decrease,
              color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
              size: iconSize,
            ),
            onPressed: _decreaseFontSize,
            tooltip: 'تصغير الخط',
            padding: EdgeInsets.all(buttonPadding),
            constraints: BoxConstraints(
              minWidth: minButtonSize,
              minHeight: minButtonSize,
            ),
          ),

          // زر زيادة حجم الخط
          IconButton(
            icon: Icon(
              Icons.text_increase,
              color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
              size: iconSize,
            ),
            onPressed: _increaseFontSize,
            tooltip: 'تكبير الخط',
            padding: EdgeInsets.all(buttonPadding),
            constraints: BoxConstraints(
              minWidth: minButtonSize,
              minHeight: minButtonSize,
            ),
          ),

          // فاصل
          Container(
            height: dividerHeight,
            width: borderWidth,
            margin: EdgeInsets.symmetric(horizontal: dividerMargin),
            color: isDarkMode
                ? ManhalColors.gold500.withValues(alpha: 0.3)
                : ManhalColors.gold300,
          ),

          // زر ملء الشاشة
          if (widget.onFullScreenToggle != null)
            IconButton(
              icon: Icon(
                widget.isFullScreen ? Icons.fullscreen_exit : Icons.fullscreen,
                color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
                size: iconSize,
              ),
              onPressed: widget.onFullScreenToggle,
              tooltip: widget.isFullScreen ? 'إلغاء ملء الشاشة' : 'ملء الشاشة',
              padding: EdgeInsets.all(buttonPadding),
              constraints: BoxConstraints(
                minWidth: minButtonSize,
                minHeight: minButtonSize,
              ),
            ),
        ],
      ),
    );

    // تعديل المزيد من الأحجام بناءً على حجم الشاشة
    final contentPadding = isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0);
    final contentBorderRadius =
        isSmallScreen ? 8.0 : (isLargeScreen ? 16.0 : 12.0);
    final contentBorderWidth =
        isSmallScreen ? 0.5 : (isLargeScreen ? 1.5 : 1.0);
    final contentBlurRadius =
        isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0);
    final contentOffset = isSmallScreen ? 3.0 : (isLargeScreen ? 5.0 : 4.0);
    final titleFontSize = isSmallScreen ? 14.0 : (isLargeScreen ? 18.0 : 16.0);
    final spacingHeight = isSmallScreen ? 16.0 : (isLargeScreen ? 32.0 : 24.0);

    return Container(
      decoration: BoxDecoration(
        color: isDarkMode ? ManhalColors.blue900 : Colors.white,
        image: DecorationImage(
          image: AssetImage(
            isDarkMode
                ? 'assets/images/paper_texture_dark.png'
                : 'assets/images/paper_texture_light.png',
          ),
          opacity: 0.1,
          fit: BoxFit.cover,
        ),
      ),
      child: Column(
        children: [
          // رأس الصفحة
          Padding(
            padding: EdgeInsets.symmetric(
                vertical: isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0),
                horizontal: contentPadding),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // عنوان الصفحة
                Text(
                  'صفحة ${_currentPageIndex + 1} من $calculatedTotalPages',
                  style: IslamicTypography.luxuryTitle(
                    isDark: isDarkMode,
                    fontSize: titleFontSize,
                    fontFamily: context.read<ManhalRawiProvider>().fontFamily,
                  ),
                ),

                // شريط الأدوات
                toolbar,
              ],
            ),
          ),

          // محتوى الصفحة
          Expanded(
            child: SingleChildScrollView(
              controller: _scrollController,
              padding: EdgeInsets.symmetric(horizontal: contentPadding),
              child: Container(
                constraints: BoxConstraints(
                  minHeight: screenSize.height *
                      (widget.isFullScreen
                          ? (isSmallScreen ? 0.7 : (isLargeScreen ? 0.85 : 0.8))
                          : (isSmallScreen
                              ? 0.65
                              : (isLargeScreen ? 0.75 : 0.7))),
                ),
                decoration: BoxDecoration(
                  color: isDarkMode
                      ? ManhalColors.blue900.withValues(alpha: 0.5)
                      : Colors.white,
                  borderRadius: BorderRadius.circular(contentBorderRadius),
                  boxShadow: [
                    BoxShadow(
                      color: isDarkMode
                          ? Colors.black.withValues(alpha: 0.2)
                          : Colors.grey.withValues(alpha: 0.2),
                      blurRadius: contentBlurRadius,
                      offset: Offset(0, contentOffset),
                    ),
                  ],
                  border: Border.all(
                    color: isDarkMode
                        ? ManhalColors.gold500.withValues(alpha: 0.3)
                        : ManhalColors.gold300.withValues(alpha: 0.5),
                    width: contentBorderWidth,
                  ),
                ),
                padding: EdgeInsets.all(contentPadding),
                child: Column(
                  children: [
                    // عنوان الصفحة
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.symmetric(
                          vertical: isSmallScreen
                              ? 6.0
                              : (isLargeScreen ? 10.0 : 8.0)),
                      decoration: BoxDecoration(
                        border: Border(
                          bottom: BorderSide(
                            color: isDarkMode
                                ? ManhalColors.gold500.withValues(alpha: 0.3)
                                : ManhalColors.gold300,
                            width: contentBorderWidth,
                          ),
                        ),
                      ),
                      child: Text(
                        'الأبيات ${startIndex + 1} - ${startIndex + pageVerses.length}',
                        style: IslamicTypography.luxuryTitle(
                          isDark: isDarkMode,
                          fontSize: titleFontSize,
                          fontFamily:
                              context.read<ManhalRawiProvider>().fontFamily,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),

                    SizedBox(height: spacingHeight),

                    // الأبيات المخمسة
                    ...pageVerses.asMap().entries.map((entry) {
                      final index = entry.key;
                      final verse = entry.value;
                      final verseIndex = startIndex + index;
                      final isSelected = verseIndex == widget.selectedIndex;

                      return _buildBookStyleVerse(
                        verse: verse,
                        verseIndex: verseIndex,
                        isSelected: isSelected,
                        isDarkMode: isDarkMode,
                        isSmallScreen: isSmallScreen,
                        isLargeScreen: isLargeScreen,
                      );
                    }),
                  ],
                ),
              ),
            ),
          ),

          // أزرار التنقل
          Padding(
            padding: EdgeInsets.symmetric(
                vertical: isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              textDirection:
                  TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
              children: [
                // زر الصفحة السابقة (في اللغة العربية يكون على اليسار)
                ElevatedButton(
                  onPressed: _currentPageIndex > 0 ? _previousPage : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: isDarkMode
                        ? ManhalColors.blue800
                        : ManhalColors.gold100,
                    foregroundColor: isDarkMode
                        ? ManhalColors.gold500
                        : ManhalColors.primary,
                    padding: EdgeInsets.symmetric(
                      horizontal:
                          isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0),
                      vertical:
                          isSmallScreen ? 8.0 : (isLargeScreen ? 16.0 : 12.0),
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                          isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0)),
                      side: BorderSide(
                        color: isDarkMode
                            ? ManhalColors.gold500.withValues(alpha: 0.3)
                            : ManhalColors.gold300,
                        width:
                            isSmallScreen ? 0.5 : (isLargeScreen ? 1.5 : 1.0),
                      ),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    textDirection: TextDirection
                        .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                    children: [
                      Icon(
                        Icons.arrow_back_ios,
                        size: isSmallScreen
                            ? 12.0
                            : (isLargeScreen ? 20.0 : 16.0),
                        color: isDarkMode
                            ? ManhalColors.gold500
                            : ManhalColors.primary,
                      ),
                      SizedBox(
                          width: isSmallScreen
                              ? 6.0
                              : (isLargeScreen ? 10.0 : 8.0)),
                      Text(
                        'السابق',
                        style: TextStyle(
                          fontFamily:
                              context.read<ManhalRawiProvider>().fontFamily,
                          fontSize: isSmallScreen
                              ? 12.0
                              : (isLargeScreen ? 16.0 : 14.0),
                          color: isDarkMode
                              ? ManhalColors.gold500
                              : ManhalColors.primary,
                        ),
                        textDirection: TextDirection
                            .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                      ),
                    ],
                  ),
                ),

                SizedBox(
                    width:
                        isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0)),

                // مؤشرات الصفحات
                Row(
                  children: List.generate(
                    calculatedTotalPages,
                    (index) => Container(
                      width: isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0),
                      height:
                          isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0),
                      margin: EdgeInsets.symmetric(
                          horizontal: isSmallScreen
                              ? 3.0
                              : (isLargeScreen ? 5.0 : 4.0)),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: index == _currentPageIndex
                            ? (isDarkMode
                                ? ManhalColors.gold500
                                : ManhalColors.primary)
                            : (isDarkMode
                                ? ManhalColors.gold500.withValues(alpha: 0.3)
                                : ManhalColors.primary.withValues(alpha: 0.3)),
                      ),
                    ),
                  ),
                ),

                SizedBox(
                    width:
                        isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0)),

                // زر الصفحة التالية (في اللغة العربية يكون على اليمين)
                ElevatedButton(
                  onPressed: _currentPageIndex < calculatedTotalPages - 1
                      ? _nextPage
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: isDarkMode
                        ? ManhalColors.blue800
                        : ManhalColors.gold100,
                    foregroundColor: isDarkMode
                        ? ManhalColors.gold500
                        : ManhalColors.primary,
                    padding: EdgeInsets.symmetric(
                      horizontal:
                          isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0),
                      vertical:
                          isSmallScreen ? 8.0 : (isLargeScreen ? 16.0 : 12.0),
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                          isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0)),
                      side: BorderSide(
                        color: isDarkMode
                            ? ManhalColors.gold500.withValues(alpha: 0.3)
                            : ManhalColors.gold300,
                        width:
                            isSmallScreen ? 0.5 : (isLargeScreen ? 1.5 : 1.0),
                      ),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    textDirection: TextDirection
                        .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                    children: [
                      Text(
                        'التالي',
                        style: TextStyle(
                          fontFamily:
                              context.read<ManhalRawiProvider>().fontFamily,
                          fontSize: isSmallScreen
                              ? 12.0
                              : (isLargeScreen ? 16.0 : 14.0),
                          color: isDarkMode
                              ? ManhalColors.gold500
                              : ManhalColors.primary,
                        ),
                        textDirection: TextDirection
                            .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                      ),
                      SizedBox(
                          width: isSmallScreen
                              ? 6.0
                              : (isLargeScreen ? 10.0 : 8.0)),
                      Icon(
                        Icons.arrow_forward_ios,
                        size: isSmallScreen
                            ? 12.0
                            : (isLargeScreen ? 20.0 : 16.0),
                        color: isDarkMode
                            ? ManhalColors.gold500
                            : ManhalColors.primary,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // بناء البيت المخمس بتصميم كتابي فاخر
  Widget _buildBookStyleVerse({
    required MukhammasVerse verse,
    required int verseIndex,
    required bool isSelected,
    required bool isDarkMode,
    bool isSmallScreen = false,
    bool isLargeScreen = false,
  }) {
    // تعديل الأحجام بناءً على حجم الشاشة ووضع ملء الشاشة
    // تقليل المسافة بين الأبيات لتقريبها من بعضها
    final bottomMargin = widget.isFullScreen
        ? (isSmallScreen
            ? 12.0
            : (isLargeScreen ? 18.0 : 14.0)) // مسافة أقل في وضع ملء الشاشة
        : (isSmallScreen ? 16.0 : (isLargeScreen ? 24.0 : 20.0));

    final borderRadius = isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0);
    final borderWidth = isSmallScreen ? 0.5 : (isLargeScreen ? 1.5 : 1.0);

    final contentPadding = widget.isFullScreen
        ? (isSmallScreen
            ? 6.0
            : (isLargeScreen ? 12.0 : 8.0)) // مسافة أقل في وضع ملء الشاشة
        : (isSmallScreen ? 8.0 : (isLargeScreen ? 16.0 : 12.0));

    // تقليل المسافات الداخلية بشكل أكبر لتقريب الأبيات
    final spacingHeight1 = widget.isFullScreen
        ? (isSmallScreen
            ? 3.0
            : (isLargeScreen ? 6.0 : 4.0)) // مسافة أقل في وضع ملء الشاشة
        : (isSmallScreen ? 4.0 : (isLargeScreen ? 8.0 : 6.0));

    final spacingHeight2 = widget.isFullScreen
        ? (isSmallScreen
            ? 1.0
            : (isLargeScreen ? 3.0 : 2.0)) // مسافة أقل في وضع ملء الشاشة
        : (isSmallScreen ? 2.0 : (isLargeScreen ? 4.0 : 3.0));

    final fifthLineFontSize = isSmallScreen
        ? _fontSize * 0.9 + 1
        : (isLargeScreen ? _fontSize * 1.1 + 1 : _fontSize + 1);

    return GestureDetector(
      // منع انتشار الحدث للعناصر الأب
      behavior: HitTestBehavior.opaque,
      onTap: () {
        widget.onVerseSelected(verseIndex);
        HapticFeedback.selectionClick(); // تأثير اهتزاز خفيف عند التحديد
      },
      child: Container(
        margin: EdgeInsets.only(bottom: bottomMargin),
        decoration: BoxDecoration(
          color: isSelected
              ? (isDarkMode
                  ? ManhalColors.blue800.withValues(alpha: 0.3)
                  : ManhalColors.gold100.withValues(alpha: 0.1))
              : Colors.transparent,
          borderRadius: BorderRadius.circular(borderRadius),
          border: isSelected
              ? Border.all(
                  color:
                      isDarkMode ? ManhalColors.gold500 : ManhalColors.gold300,
                  width: borderWidth,
                )
              : null,
        ),
        padding: EdgeInsets.all(contentPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // رقم البيت (مخفي)
            // تم إخفاء شعار "البيت رقم X" بناءً على طلب المستخدم

            // السطر الأول (الشطران الأول والثاني)
            _buildVerseLine(
              rightText: verse.firstLine1,
              leftText: verse.firstLine2,
              isDarkMode: isDarkMode,
              isSmallScreen: isSmallScreen,
              isLargeScreen: isLargeScreen,
            ),

            SizedBox(height: spacingHeight2),

            // السطر الثاني (الشطران الثالث والرابع)
            _buildVerseLine(
              rightText: verse.secondLine1,
              leftText: verse.secondLine2,
              isDarkMode: isDarkMode,
              isSmallScreen: isSmallScreen,
              isLargeScreen: isLargeScreen,
            ),

            SizedBox(height: spacingHeight1),

            // تم إزالة الفاصل الزخرفي قبل الشطر الخامس لتقريب الأبيات
            // تم إضافة مسافة صغيرة جدًا فقط
            SizedBox(height: 2),
            

            // الشطر الخامس (في المنتصف)
            Container(
              width: double.infinity,
              alignment: Alignment.center,
              child: Text(
                verse.fifthLine,
                style: TextStyle(
                  fontFamily: context.read<ManhalRawiProvider>().fontFamily,
                  fontSize: fifthLineFontSize,
                  fontWeight:
                      FontWeight.normal, // تأكيد على إزالة التنسيق الغامق
                  color: isDarkMode
                      ? ManhalColors.textLight
                      : ManhalColors
                          .textDark, // تغيير اللون ليتطابق مع الأبيات الأخرى
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
                textDirection:
                    TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
              ),
            ),
          ],
        ),
      ),
    );
  }

  // بناء سطر من البيت (شطرين متقابلين)
  Widget _buildVerseLine({
    required String rightText,
    required String leftText,
    required bool isDarkMode,
    bool isSmallScreen = false,
    bool isLargeScreen = false,
  }) {
    // تعديل الأحجام بناءً على حجم الشاشة
    final horizontalPadding =
        isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0);
    final dividerHeight = isSmallScreen
        ? _fontSize * 1.3
        : (isLargeScreen ? _fontSize * 1.7 : _fontSize * 1.5);
    final dividerWidth = isSmallScreen ? 0.5 : (isLargeScreen ? 1.5 : 1.0);

    // تعديل حجم الخط بناءً على حجم الشاشة
    final fontSize = isSmallScreen
        ? _fontSize * 0.9
        : (isLargeScreen ? _fontSize * 1.1 : _fontSize);

    return Row(
      textDirection: TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
      children: [
        // الشطر الأيمن (يظهر على اليمين في اللغة العربية)
        Expanded(
          child: Text(
            rightText,
            style: TextStyle(
              fontFamily: context.read<ManhalRawiProvider>().fontFamily,
              fontSize: fontSize,
              color:
                  isDarkMode ? ManhalColors.textLight : ManhalColors.textDark,
              height: 1.5,
            ),
            textAlign: TextAlign.start,
            textDirection:
                TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
          ),
        ),

        // فاصل زخرفي بين الشطرين
        Padding(
          padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
          child: Container(
            height: dividerHeight,
            width: dividerWidth,
            color: isDarkMode
                ? ManhalColors.gold500.withValues(alpha: 0.2)
                : ManhalColors.gold300.withValues(alpha: 0.5),
          ),
        ),

        // الشطر الأيسر (يظهر على اليسار في اللغة العربية)
        Expanded(
          child: Text(
            leftText,
            style: TextStyle(
              fontFamily: context.read<ManhalRawiProvider>().fontFamily,
              fontSize: fontSize,
              color:
                  isDarkMode ? ManhalColors.textLight : ManhalColors.textDark,
              height: 1.5,
            ),
            textAlign: TextAlign.end,
            textDirection:
                TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
          ),
        ),
      ],
    );
  }
}
