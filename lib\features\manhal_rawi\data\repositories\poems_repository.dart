import '../datasources/local_datasource.dart';
import '../datasources/favorites_local_datasource.dart';
import '../models/poem.dart';
import '../models/poet.dart';
import '../models/category.dart';
import '../models/book_info.dart';

class PoemsRepository {
  final LocalDataSource localDataSource;
  final FavoritesLocalDataSource favoritesDataSource;

  PoemsRepository({
    required this.localDataSource,
    required this.favoritesDataSource,
  });

  Future<BookInfo> getBookInfo() async {
    return await localDataSource.getBookInfo();
  }

  Future<List<Poet>> getPoets() async {
    return await localDataSource.getPoets();
  }

  Future<List<Category>> getCategories() async {
    return await localDataSource.getCategories();
  }

  Future<List<Poem>> getPoems() async {
    return await localDataSource.getPoems();
  }

  Future<Poet?> getPoetById(int id) async {
    return await localDataSource.getPoetById(id);
  }

  Future<Category?> getCategoryById(int id) async {
    return await localDataSource.getCategoryById(id);
  }

  Future<Poem?> getPoemById(int id) async {
    return await localDataSource.getPoemById(id);
  }

  Future<List<Poem>> getPoemsByPoetId(int poetId) async {
    return await localDataSource.getPoemsByPoetId(poetId);
  }

  Future<List<Poem>> getPoemsByCategoryId(int categoryId) async {
    return await localDataSource.getPoemsByCategoryId(categoryId);
  }

  Future<List<Poem>> searchPoems(String query) async {
    return await localDataSource.searchPoems(query);
  }

  // دوال المفضلات

  /// الحصول على قائمة القصائد المفضلة
  Future<List<Poem>> getFavoritePoems() async {
    try {
      // استخدام الطريقة المباشرة للحصول على القصائد المفضلة
      final List<int> favoriteIds = await favoritesDataSource.getFavoriteIds();
      final List<Poem> allPoems = await getPoems();

      // تحديث حالة المفضلة لجميع القصائد
      final List<Poem> updatedPoems =
          await _updatePoemsFavoriteStatus(allPoems);

      // تصفية القصائد المفضلة فقط
      return updatedPoems
          .where((poem) => favoriteIds.contains(poem.id))
          .toList();
    } catch (e) {
      // في حالة حدوث خطأ، نعيد قائمة فارغة
      return [];
    }
  }

  /// إضافة قصيدة إلى المفضلة
  Future<void> addToFavorites(int poemId) async {
    await favoritesDataSource.addFavorite(poemId);
  }

  /// إزالة قصيدة من المفضلة
  Future<void> removeFromFavorites(int poemId) async {
    await favoritesDataSource.removeFavorite(poemId);
  }

  /// تبديل حالة المفضلة للقصيدة
  Future<bool> toggleFavorite(int poemId) async {
    return await favoritesDataSource.toggleFavorite(poemId);
  }

  /// التحقق مما إذا كانت القصيدة مفضلة
  Future<bool> isFavorite(int poemId) async {
    return await favoritesDataSource.isFavorite(poemId);
  }

  /// تحديث حالة المفضلة لقائمة من القصائد
  Future<List<Poem>> _updatePoemsFavoriteStatus(List<Poem> poems) async {
    final List<int> favoriteIds = await favoritesDataSource.getFavoriteIds();

    return poems.map((poem) {
      final bool isFav = favoriteIds.contains(poem.id);
      if (poem.isFavorite != isFav) {
        return poem.copyWith(isFavorite: isFav);
      }
      return poem;
    }).toList();
  }

  /// تحديث حالة المفضلة لجميع القصائد
  Future<List<Poem>> getPoemsWithFavoriteStatus() async {
    final List<Poem> poems = await getPoems();
    return await _updatePoemsFavoriteStatus(poems);
  }
}
