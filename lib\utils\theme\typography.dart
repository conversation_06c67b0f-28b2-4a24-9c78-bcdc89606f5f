import 'package:flutter/material.dart';
import 'manhal_colors.dart';

class ManhalTypography {
  // أنماط النصوص للعناوين
  static TextStyle get headingLarge => const TextStyle(
        fontFamily: 'Scheherazade',
        fontSize: 32,
        fontWeight: FontWeight.bold,
        color: ManhalColors.textDark,
        height: 1.3,
      );

  static TextStyle get headingMedium => const TextStyle(
        fontFamily: 'Scheherazade',
        fontSize: 24,
        fontWeight: FontWeight.bold,
        color: ManhalColors.textDark,
        height: 1.3,
      );

  static TextStyle get headingSmall => const TextStyle(
        fontFamily: 'Scheherazade',
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: ManhalColors.textDark,
        height: 1.3,
      );

  // أنماط النصوص للأبيات الشعرية
  static TextStyle get verseText => const TextStyle(
        fontFamily: 'Amir<PERSON>',
        fontSize: 22,
        fontWeight: FontWeight.w500,
        color: ManhalColors.textDark,
        height: 1.8,
      );

  // أنماط النصوص للوصف
  static TextStyle get bodyLarge => const TextStyle(
        fontFamily: 'Amiri',
        fontSize: 18,
        fontWeight: FontWeight.normal,
        color: ManhalColors.textDark,
        height: 1.5,
      );

  static TextStyle get bodyMedium => const TextStyle(
        fontFamily: 'Amiri',
        fontSize: 16,
        fontWeight: FontWeight.normal,
        color: ManhalColors.textDark,
        height: 1.5,
      );

  static TextStyle get bodySmall => const TextStyle(
        fontFamily: 'Amiri',
        fontSize: 14,
        fontWeight: FontWeight.normal,
        color: ManhalColors.textDark,
        height: 1.5,
      );

  // أنماط النصوص للشعراء
  static TextStyle get poetName => const TextStyle(
        fontFamily: 'ArefRuqaa',
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: ManhalColors.textDark,
        height: 1.3,
      );

  // أنماط النصوص للتصنيفات
  static TextStyle get categoryText => const TextStyle(
        fontFamily: 'ArefRuqaa',
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: ManhalColors.textDark,
        height: 1.3,
      );

  // نسخ الأنماط مع تغيير اللون للوضع المظلم
  static TextStyle withColor(TextStyle style, Color color) {
    return style.copyWith(color: color);
  }
}
