import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../data/models/poem.dart';
import '../../data/models/poet.dart';
import '../providers/manhal_rawi_provider.dart';
import '../widgets/luxury_poem_card.dart';
import '../widgets/animated_islamic_background.dart';
import 'luxury_poem_details_screen.dart';
import 'settings_screen.dart';
import '../../../../utils/theme/manhal_colors.dart';
import '../../../../utils/theme/islamic_typography.dart';
import '../../../../utils/animations/islamic_animations.dart';
import '../../../../utils/helpers/responsive_helper.dart';

class FavoritesScreen extends StatefulWidget {
  const FavoritesScreen({super.key});

  @override
  State<FavoritesScreen> createState() => _FavoritesScreenState();
}

class _FavoritesScreenState extends State<FavoritesScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    _animationController.forward();

    // تحميل المفضلات عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ManhalRawiProvider>().loadFavorites();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<ManhalRawiProvider>();
    final isDarkMode = provider.isDarkMode;
    final favoritePoems = provider.favoritePoems;
    final isLoading = provider.isFavoritesLoading;
    final poets = provider.poets;

    // الحصول على الشاعر (محمد هزاع باعلوي)
    // إنشاء شاعر افتراضي في حالة عدم وجود شعراء
    final poet = poets.isNotEmpty
        ? poets.first
        : Poet(
            id: 1,
            name: "محمد هزاع باعلوي",
            fullName: "محمد هزاع باعلوي الحضرمي",
            era: "العصر الحديث",
            birthYear: 1950,
            bio: "مؤلف كتاب المنهل الراوي",
          );

    return Scaffold(
      body: AnimatedIslamicBackground(
        child: SafeArea(
          child: Column(
            children: [
              // شريط التطبيق
              _buildAppBar(context),

              // محتوى الشاشة
              Expanded(
                child: isLoading
                    ? _buildLoadingIndicator(isDarkMode)
                    : favoritePoems.isEmpty
                        ? _buildEmptyState(isDarkMode)
                        : _buildFavoritesList(favoritePoems, poet, isDarkMode),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar(BuildContext context) {
    final isDarkMode = context.watch<ManhalRawiProvider>().isDarkMode;

    return LayoutBuilder(
      builder: (context, constraints) {
        // تعديل التصميم بناءً على حجم الشاشة
        final bool isSmallScreen = ResponsiveHelper.isSmallScreen(context);
        final bool isLargeScreen = ResponsiveHelper.isLargeScreen(context) ||
            ResponsiveHelper.isExtraLargeScreen(context);

        final double iconSize = ResponsiveHelper.getResponsiveIconSize(context,
            defaultSize: isLargeScreen ? 24.0 : 20.0);

        final double titleFontSize = ResponsiveHelper.getResponsiveFontSize(
            context,
            fontSize: isLargeScreen ? 28.0 : 24.0);

        final double buttonSize = ResponsiveHelper.getResponsiveIconSize(
            context,
            defaultSize: isLargeScreen ? 48.0 : 40.0);

        final EdgeInsets padding = EdgeInsets.symmetric(
            horizontal: ResponsiveHelper.getResponsiveSpacing(context,
                defaultValue: isLargeScreen ? 24.0 : 16.0),
            vertical: ResponsiveHelper.getResponsiveSpacing(context,
                defaultValue: isLargeScreen ? 12.0 : 8.0));

        return Container(
          padding: padding,
          decoration: BoxDecoration(
            color: Colors.transparent,
            boxShadow: [
              BoxShadow(
                color: isDarkMode
                    ? Colors.black.withValues(alpha: 0.1)
                    : Colors.black.withValues(alpha: 0.05),
                blurRadius: ResponsiveHelper.getResponsiveShadow(
                  context,
                  defaultBlurRadius: 8.0,
                )[0],
                spreadRadius: ResponsiveHelper.getResponsiveShadow(
                  context,
                  defaultSpreadRadius: 0.0,
                )[1],
                offset: Offset(
                    0,
                    ResponsiveHelper.getResponsiveShadow(
                      context,
                      defaultYOffset: 2.0,
                    )[2]),
              ),
            ],
          ),
          child: Row(
            children: [
              // زر الرجوع
              Container(
                height: buttonSize,
                width: buttonSize,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: isDarkMode
                      ? ManhalColors.blue800.withValues(alpha: 0.7)
                      : Colors.white.withValues(alpha: 0.7),
                  border: Border.all(
                    color: isDarkMode
                        ? ManhalColors.gold500.withValues(alpha: 0.3)
                        : ManhalColors.gold300,
                    width: ResponsiveHelper.getResponsiveBorderWidth(
                      context,
                      defaultValue: 1.0,
                    ),
                  ),
                ),
                child: IconButton(
                  icon: Icon(
                    Icons.arrow_back_ios_new,
                    color: isDarkMode
                        ? ManhalColors.gold500
                        : ManhalColors.primary,
                    size: iconSize,
                  ),
                  onPressed: () {
                    Navigator.pop(context);
                  },
                ),
              ),

              const Spacer(),

              // عنوان الشاشة
              Text(
                'المفضلة',
                style: IslamicTypography.luxuryTitle(
                  isDark: isDarkMode,
                  fontSize: titleFontSize,
                ),
              ),

              const Spacer(),

              // زر الإعدادات
              Container(
                height: buttonSize,
                width: buttonSize,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: isDarkMode
                      ? ManhalColors.blue800.withValues(alpha: 0.7)
                      : Colors.white.withValues(alpha: 0.7),
                  border: Border.all(
                    color: isDarkMode
                        ? ManhalColors.gold500.withValues(alpha: 0.3)
                        : ManhalColors.gold300,
                    width: ResponsiveHelper.getResponsiveBorderWidth(
                      context,
                      defaultValue: 1.0,
                    ),
                  ),
                ),
                child: IconButton(
                  icon: Icon(
                    Icons.settings,
                    color: isDarkMode
                        ? ManhalColors.gold500
                        : ManhalColors.primary,
                    size: iconSize,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      PageRouteBuilder(
                        pageBuilder: (context, animation, secondaryAnimation) =>
                            const SettingsScreen(),
                        transitionsBuilder:
                            (context, animation, secondaryAnimation, child) {
                          return FadeTransition(
                            opacity: animation,
                            child: child,
                          );
                        },
                        transitionDuration: const Duration(milliseconds: 500),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLoadingIndicator(bool isDarkMode) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // تعديل التصميم بناءً على حجم الشاشة
        final bool isLargeScreen = ResponsiveHelper.isLargeScreen(context) ||
            ResponsiveHelper.isExtraLargeScreen(context);

        final double iconSize = ResponsiveHelper.getResponsiveIconSize(context,
            defaultSize: isLargeScreen ? 48.0 : 32.0);

        final double fontSize = ResponsiveHelper.getResponsiveFontSize(context,
            fontSize: isLargeScreen ? 18.0 : 16.0);

        final double containerPadding = ResponsiveHelper.getResponsiveSpacing(
            context,
            defaultValue: isLargeScreen ? 24.0 : 16.0);

        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // أيقونة التحميل
              IslamicAnimations.pulsatingEffect(
                child: Container(
                  padding: EdgeInsets.all(containerPadding),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isDarkMode
                        ? ManhalColors.blue800.withValues(alpha: 0.7)
                        : Colors.white.withValues(alpha: 0.7),
                    border: Border.all(
                      color: isDarkMode
                          ? ManhalColors.gold500
                          : ManhalColors.primary,
                      width: ResponsiveHelper.getResponsiveBorderWidth(
                        context,
                        defaultValue: isLargeScreen ? 3.0 : 2.0,
                      ),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: (isDarkMode
                                ? ManhalColors.gold500
                                : ManhalColors.primary)
                            .withValues(alpha: 0.3),
                        blurRadius: ResponsiveHelper.getResponsiveShadow(
                          context,
                          defaultBlurRadius: isLargeScreen ? 15.0 : 10.0,
                        )[0],
                        spreadRadius: ResponsiveHelper.getResponsiveShadow(
                          context,
                          defaultSpreadRadius: isLargeScreen ? 3.0 : 2.0,
                        )[1],
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.favorite,
                    size: iconSize,
                    color: isDarkMode
                        ? ManhalColors.gold500
                        : ManhalColors.primary,
                  ),
                ),
                isDark: isDarkMode,
              ),

              SizedBox(
                height: ResponsiveHelper.getResponsiveSpacing(
                  context,
                  defaultValue: isLargeScreen ? 24.0 : 16.0,
                ),
              ),

              // نص التحميل
              Text(
                'جاري تحميل المفضلة...',
                style: IslamicTypography.luxuryBody(
                  isDark: isDarkMode,
                  fontSize: fontSize,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEmptyState(bool isDarkMode) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: LayoutBuilder(
        builder: (context, constraints) {
          // تعديل التصميم بناءً على حجم الشاشة
          final bool isLargeScreen = ResponsiveHelper.isLargeScreen(context) ||
              ResponsiveHelper.isExtraLargeScreen(context);

          final double iconSize = ResponsiveHelper.getResponsiveIconSize(
              context,
              defaultSize: isLargeScreen ? 80.0 : 64.0);

          final double titleFontSize = ResponsiveHelper.getResponsiveFontSize(
              context,
              fontSize: isLargeScreen ? 24.0 : 20.0);

          final double bodyFontSize = ResponsiveHelper.getResponsiveFontSize(
              context,
              fontSize: isLargeScreen ? 18.0 : 16.0);

          final double buttonFontSize = ResponsiveHelper.getResponsiveFontSize(
              context,
              fontSize: isLargeScreen ? 18.0 : 16.0);

          final EdgeInsets padding = EdgeInsets.all(
              ResponsiveHelper.getResponsiveSpacing(context,
                  defaultValue: isLargeScreen ? 48.0 : 32.0));

          return Center(
            child: SingleChildScrollView(
              child: Padding(
                padding: padding,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // أيقونة القلب الفارغ
                    Icon(
                      Icons.favorite_border,
                      size: iconSize,
                      color: isDarkMode
                          ? ManhalColors.gold500
                          : ManhalColors.primary,
                    ),

                    SizedBox(
                      height: ResponsiveHelper.getResponsiveSpacing(
                        context,
                        defaultValue: isLargeScreen ? 24.0 : 16.0,
                      ),
                    ),

                    // عنوان الحالة الفارغة
                    Text(
                      'لا توجد قصائد في المفضلة',
                      style: IslamicTypography.luxuryTitle(
                        isDark: isDarkMode,
                        fontSize: titleFontSize,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    SizedBox(
                      height: ResponsiveHelper.getResponsiveSpacing(
                        context,
                        defaultValue: isLargeScreen ? 16.0 : 8.0,
                      ),
                    ),

                    // وصف الحالة الفارغة
                    Text(
                      'يمكنك إضافة القصائد إلى المفضلة بالضغط على زر القلب في صفحة تفاصيل القصيدة',
                      style: IslamicTypography.luxuryBody(
                        isDark: isDarkMode,
                        fontSize: bodyFontSize,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    SizedBox(
                      height: ResponsiveHelper.getResponsiveSpacing(
                        context,
                        defaultValue: isLargeScreen ? 32.0 : 24.0,
                      ),
                    ),

                    // زر العودة إلى الصفحة الرئيسية
                    ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: isDarkMode
                            ? ManhalColors.gold500
                            : ManhalColors.primary,
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(
                          horizontal: ResponsiveHelper.getResponsiveSpacing(
                            context,
                            defaultValue: isLargeScreen ? 32.0 : 24.0,
                          ),
                          vertical: ResponsiveHelper.getResponsiveSpacing(
                            context,
                            defaultValue: isLargeScreen ? 16.0 : 12.0,
                          ),
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(
                            ResponsiveHelper.getResponsiveBorderRadius(
                              context,
                              defaultValue: 16.0,
                            ),
                          ),
                        ),
                      ),
                      child: Text(
                        'العودة إلى الصفحة الرئيسية',
                        style: IslamicTypography.luxuryButton(
                          isDark: isDarkMode,
                          fontSize: buttonFontSize,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildFavoritesList(
      List<Poem> favoritePoems, Poet poet, bool isDarkMode) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: LayoutBuilder(
        builder: (context, constraints) {
          // تحديد عدد الأعمدة بناءً على حجم الشاشة
          final int crossAxisCount =
              _calculateCrossAxisCount(constraints.maxWidth);

          // استخدام GridView للشاشات الكبيرة وListView للشاشات الصغيرة
          if (crossAxisCount > 1) {
            return _buildGridView(
                favoritePoems, poet, isDarkMode, crossAxisCount);
          } else {
            return _buildListView(favoritePoems, poet, isDarkMode);
          }
        },
      ),
    );
  }

  // حساب عدد الأعمدة بناءً على حجم الشاشة
  int _calculateCrossAxisCount(double width) {
    if (ResponsiveHelper.isExtraLargeScreen(context)) {
      return 3; // شاشات كبيرة جداً (أجهزة الكمبيوتر)
    } else if (ResponsiveHelper.isLargeScreen(context)) {
      return 2; // شاشات متوسطة (الأجهزة اللوحية)
    } else {
      return 1; // شاشات صغيرة (الهواتف)
    }
  }

  // بناء عرض الشبكة للشاشات الكبيرة
  Widget _buildGridView(List<Poem> favoritePoems, Poet poet, bool isDarkMode,
      int crossAxisCount) {
    return CustomScrollView(
      slivers: [
        // عنوان القسم
        SliverToBoxAdapter(
          child: Padding(
            padding: EdgeInsets.fromLTRB(
              ResponsiveHelper.getResponsiveSpacing(context,
                  defaultValue: 16.0),
              ResponsiveHelper.getResponsiveSpacing(context,
                  defaultValue: 16.0),
              ResponsiveHelper.getResponsiveSpacing(context,
                  defaultValue: 16.0),
              ResponsiveHelper.getResponsiveSpacing(context, defaultValue: 8.0),
            ),
            child: Row(
              children: [
                Text(
                  'القصائد المفضلة',
                  style: IslamicTypography.luxurySubtitle(
                    isDark: isDarkMode,
                    fontSize: ResponsiveHelper.getResponsiveFontSize(
                      context,
                      fontSize: 20.0,
                    ),
                  ),
                ),
                const Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: ResponsiveHelper.getResponsiveSpacing(
                      context,
                      defaultValue: 12.0,
                    ),
                    vertical: ResponsiveHelper.getResponsiveSpacing(
                      context,
                      defaultValue: 4.0,
                    ),
                  ),
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? ManhalColors.blue900.withValues(alpha: 0.7)
                        : ManhalColors.gold100.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(
                      ResponsiveHelper.getResponsiveBorderRadius(
                        context,
                        defaultValue: 12.0,
                      ),
                    ),
                    border: Border.all(
                      color: isDarkMode
                          ? ManhalColors.gold600.withValues(alpha: 0.3)
                          : ManhalColors.gold300,
                      width: ResponsiveHelper.getResponsiveBorderWidth(
                        context,
                        defaultValue: 1.0,
                      ),
                    ),
                  ),
                  child: Text(
                    '${favoritePoems.length} قصيدة',
                    style: IslamicTypography.luxuryCategory(
                      isDark: isDarkMode,
                      fontSize: ResponsiveHelper.getResponsiveFontSize(
                        context,
                        fontSize: 14.0,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),

        // شبكة القصائد المفضلة
        SliverPadding(
          padding: EdgeInsets.all(
            ResponsiveHelper.getResponsiveSpacing(
              context,
              defaultValue: 8.0,
            ),
          ),
          sliver: SliverGrid(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: crossAxisCount,
              childAspectRatio: 1.2,
              crossAxisSpacing: ResponsiveHelper.getResponsiveSpacing(
                context,
                defaultValue: 8.0,
              ),
              mainAxisSpacing: ResponsiveHelper.getResponsiveSpacing(
                context,
                defaultValue: 8.0,
              ),
            ),
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                final poem = favoritePoems[index];
                return GestureDetector(
                  onTap: () {
                    Navigator.push(
                      context,
                      PageRouteBuilder(
                        pageBuilder: (context, animation, secondaryAnimation) =>
                            LuxuryPoemDetailsScreen(
                          poem: poem,
                          poet: poet,
                        ),
                        transitionsBuilder:
                            (context, animation, secondaryAnimation, child) {
                          return FadeTransition(
                            opacity: animation,
                            child: child,
                          );
                        },
                        transitionDuration: const Duration(milliseconds: 500),
                      ),
                    );
                  },
                  child: LuxuryPoemCard(
                    poem: poem,
                    poet: poet,
                    category: null,
                    showAnimation: index < 3,
                  ),
                );
              },
              childCount: favoritePoems.length,
            ),
          ),
        ),

        // مساحة إضافية في النهاية
        SliverToBoxAdapter(
          child: SizedBox(
            height: ResponsiveHelper.getResponsiveSpacing(
              context,
              defaultValue: 32.0,
            ),
          ),
        ),
      ],
    );
  }

  // بناء عرض القائمة للشاشات الصغيرة
  Widget _buildListView(List<Poem> favoritePoems, Poet poet, bool isDarkMode) {
    return CustomScrollView(
      slivers: [
        // عنوان القسم
        SliverToBoxAdapter(
          child: Padding(
            padding: EdgeInsets.fromLTRB(
              ResponsiveHelper.getResponsiveSpacing(context,
                  defaultValue: 16.0),
              ResponsiveHelper.getResponsiveSpacing(context,
                  defaultValue: 16.0),
              ResponsiveHelper.getResponsiveSpacing(context,
                  defaultValue: 16.0),
              ResponsiveHelper.getResponsiveSpacing(context, defaultValue: 8.0),
            ),
            child: Row(
              children: [
                Text(
                  'القصائد المفضلة',
                  style: IslamicTypography.luxurySubtitle(
                    isDark: isDarkMode,
                    fontSize: ResponsiveHelper.getResponsiveFontSize(
                      context,
                      fontSize: 20.0,
                    ),
                  ),
                ),
                const Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: ResponsiveHelper.getResponsiveSpacing(
                      context,
                      defaultValue: 12.0,
                    ),
                    vertical: ResponsiveHelper.getResponsiveSpacing(
                      context,
                      defaultValue: 4.0,
                    ),
                  ),
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? ManhalColors.blue900.withValues(alpha: 0.7)
                        : ManhalColors.gold100.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(
                      ResponsiveHelper.getResponsiveBorderRadius(
                        context,
                        defaultValue: 12.0,
                      ),
                    ),
                    border: Border.all(
                      color: isDarkMode
                          ? ManhalColors.gold600.withValues(alpha: 0.3)
                          : ManhalColors.gold300,
                      width: ResponsiveHelper.getResponsiveBorderWidth(
                        context,
                        defaultValue: 1.0,
                      ),
                    ),
                  ),
                  child: Text(
                    '${favoritePoems.length} قصيدة',
                    style: IslamicTypography.luxuryCategory(
                      isDark: isDarkMode,
                      fontSize: ResponsiveHelper.getResponsiveFontSize(
                        context,
                        fontSize: 14.0,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),

        // قائمة القصائد المفضلة
        SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) {
              final poem = favoritePoems[index];
              return GestureDetector(
                onTap: () {
                  Navigator.push(
                    context,
                    PageRouteBuilder(
                      pageBuilder: (context, animation, secondaryAnimation) =>
                          LuxuryPoemDetailsScreen(
                        poem: poem,
                        poet: poet,
                      ),
                      transitionsBuilder:
                          (context, animation, secondaryAnimation, child) {
                        return FadeTransition(
                          opacity: animation,
                          child: child,
                        );
                      },
                      transitionDuration: const Duration(milliseconds: 500),
                    ),
                  );
                },
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    vertical: ResponsiveHelper.getResponsiveSpacing(
                      context,
                      defaultValue: 4.0,
                    ),
                    horizontal: ResponsiveHelper.getResponsiveSpacing(
                      context,
                      defaultValue: 8.0,
                    ),
                  ),
                  child: LuxuryPoemCard(
                    poem: poem,
                    poet: poet,
                    category: null,
                    showAnimation: index < 3,
                  ),
                ),
              );
            },
            childCount: favoritePoems.length,
          ),
        ),

        // مساحة إضافية في النهاية
        SliverToBoxAdapter(
          child: SizedBox(
            height: ResponsiveHelper.getResponsiveSpacing(
              context,
              defaultValue: 32.0,
            ),
          ),
        ),
      ],
    );
  }
}
