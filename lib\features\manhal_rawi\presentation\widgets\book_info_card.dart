import 'package:flutter/material.dart';
import '../../data/models/book_info.dart';
import '../../../../utils/theme/manhal_colors.dart';
import '../../../../utils/theme/typography.dart';
import '../../../../utils/theme/decorations.dart';

class BookInfoCard extends StatelessWidget {
  final BookInfo bookInfo;
  final bool isDarkMode;

  const BookInfoCard({
    super.key,
    required this.bookInfo,
    required this.isDarkMode,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: ManhalDecorations.luxuryCardDecoration(isDark: isDarkMode),
      child: Column(
        children: [
          // صورة الغلاف (إذا كانت متوفرة)
          if (bookInfo.coverImageUrl != null)
            ClipRRect(
              borderRadius:
                  const BorderRadius.vertical(top: Radius.circular(12)),
              child: Image.asset(
                bookInfo.coverImageUrl!,
                width: double.infinity,
                height: 180,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: double.infinity,
                    height: 180,
                    color: isDarkMode
                        ? ManhalColors.blue800
                        : ManhalColors.gold100,
                    child: Center(
                      child: Icon(
                        Icons.book,
                        size: 64,
                        color: isDarkMode
                            ? ManhalColors.gold500
                            : ManhalColors.primary,
                      ),
                    ),
                  );
                },
              ),
            ),

          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // عنوان الكتاب
                Text(
                  bookInfo.title,
                  style: ManhalTypography.headingLarge.copyWith(
                    color: isDarkMode
                        ? ManhalColors.gold500
                        : ManhalColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 4),

                // العنوان الفرعي
                Text(
                  bookInfo.subtitle,
                  style: ManhalTypography.headingSmall.copyWith(
                    color: isDarkMode
                        ? ManhalColors.gold300
                        : ManhalColors.gold600,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 16),

                // سنة النشر
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? ManhalColors.blue800.withValues(alpha: 0.7)
                        : ManhalColors.gold200.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    "سنة النشر: ${bookInfo.publishYear}",
                    style: ManhalTypography.bodyMedium.copyWith(
                      color: isDarkMode
                          ? ManhalColors.textLight
                          : ManhalColors.textDark,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // وصف الكتاب
                Text(
                  bookInfo.description,
                  style: ManhalTypography.bodyMedium.copyWith(
                    color: isDarkMode
                        ? ManhalColors.textLight
                        : ManhalColors.textDark,
                  ),
                  textAlign: TextAlign.justify,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
