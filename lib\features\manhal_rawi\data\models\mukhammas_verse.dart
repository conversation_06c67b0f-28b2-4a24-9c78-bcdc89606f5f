/// نموذج البيت المخمس
/// يتكون من أربعة أشطر متقابلة وشطر خامس في المنتصف
class MukhammasVerse {
  /// الشطر الأول من السطر الأول
  final String firstLine1;

  /// الشطر الثاني من السطر الأول
  final String firstLine2;

  /// الشطر الأول من السطر الثاني
  final String secondLine1;

  /// الشطر الثاني من السطر الثاني
  final String secondLine2;

  /// الشطر الخامس (في المنتصف)
  final String fifthLine;

  const MukhammasVerse({
    required this.firstLine1,
    required this.firstLine2,
    required this.secondLine1,
    required this.secondLine2,
    required this.fifthLine,
  });

  /// إنشاء نسخة من البيت المخمس من JSON
  factory MukhammasVerse.fromJson(Map<String, dynamic> json) {
    return MukhammasVerse(
      firstLine1: json['first_line_1'] as String,
      firstLine2: json['first_line_2'] as String,
      secondLine1: json['second_line_1'] as String,
      secondLine2: json['second_line_2'] as String,
      fifthLine: json['fifth_line'] as String,
    );
  }

  /// تحويل البيت المخمس إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'first_line_1': firstLine1,
      'first_line_2': firstLine2,
      'second_line_1': secondLine1,
      'second_line_2': secondLine2,
      'fifth_line': fifthLine,
    };
  }

  /// نسخة معدلة من البيت المخمس
  MukhammasVerse copyWith({
    String? firstLine1,
    String? firstLine2,
    String? secondLine1,
    String? secondLine2,
    String? fifthLine,
  }) {
    return MukhammasVerse(
      firstLine1: firstLine1 ?? this.firstLine1,
      firstLine2: firstLine2 ?? this.firstLine2,
      secondLine1: secondLine1 ?? this.secondLine1,
      secondLine2: secondLine2 ?? this.secondLine2,
      fifthLine: fifthLine ?? this.fifthLine,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MukhammasVerse &&
        other.firstLine1 == firstLine1 &&
        other.firstLine2 == firstLine2 &&
        other.secondLine1 == secondLine1 &&
        other.secondLine2 == secondLine2 &&
        other.fifthLine == fifthLine;
  }

  @override
  int get hashCode {
    return Object.hash(
      firstLine1,
      firstLine2,
      secondLine1,
      secondLine2,
      fifthLine,
    );
  }
}
