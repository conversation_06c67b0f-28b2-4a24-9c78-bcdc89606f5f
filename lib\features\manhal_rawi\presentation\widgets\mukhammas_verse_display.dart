import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'dart:math' as math;
import '../../../../utils/theme/manhal_colors.dart';
import '../../../../utils/theme/islamic_typography.dart';
import '../../data/models/mukhammas_verse.dart';
import '../providers/manhal_rawi_provider.dart';
import '../../../../utils/helpers/responsive_helper.dart';

/// مكون عرض البيت المخمس بتصميم فاخر
/// يعرض البيت المخمس بشكل أربعة أشطر متقابلة وشطر خامس في المنتصف
class MukhammasVerseDisplay extends StatefulWidget {
  /// قائمة الأبيات المخمسة
  final List<MukhammasVerse> verses;

  /// مؤشر البيت المحدد
  final int selectedIndex;

  /// دالة يتم استدعاؤها عند اختيار بيت
  final Function(int) onVerseSelected;

  /// دالة يتم استدعاؤها عند تبديل وضع ملء الشاشة
  final Function()? onFullScreenToggle;

  /// حالة وضع ملء الشاشة
  final bool isFullScreen;

  /// دالة يتم استدعاؤها عند تغيير حالة التفاعل مع المكون
  final Function(bool)? onInteractionStateChanged;

  const MukhammasVerseDisplay({
    super.key,
    required this.verses,
    this.selectedIndex = 0,
    required this.onVerseSelected,
    this.onFullScreenToggle,
    this.isFullScreen = false,
    this.onInteractionStateChanged,
  });

  @override
  State<MukhammasVerseDisplay> createState() => _MukhammasVerseDisplayState();
}

class _MukhammasVerseDisplayState extends State<MukhammasVerseDisplay>
    with SingleTickerProviderStateMixin {
  // متغير لتخزين مزود البيانات
  late ManhalRawiProvider _provider;

  // متغير لتخزين وحدة تحكم التمرير
  final ScrollController _scrollController = ScrollController();

  // متغير لتخزين وحدة تحكم الصفحات
  late PageController _pageController;

  // متغير لتخزين وحدة تحكم الرسوم المتحركة
  late AnimationController _animationController;

  // متغير لتخزين مؤشر الصفحة الحالية
  int _currentIndex = 0;

  // متغير لتخزين عدد الأبيات في كل صفحة
  int _versesPerPage = 5;

  // متغير لتخزين إجمالي عدد الصفحات
  int _totalPages = 1;

  // متغير لتخزين حجم الخط الحالي
  double _fontSize = 16.0;

  // متغير لتخزين مقياس التكبير/التصغير
  double _scale = 1.0;

  @override
  void initState() {
    super.initState();

    // تهيئة مزود البيانات
    _provider = Provider.of<ManhalRawiProvider>(context, listen: false);

    // استخدام إعدادات العرض من المزود
    _fontSize = _provider.verseDisplaySettings.fontSize;
    _versesPerPage = _provider.verseDisplaySettings.versesPerPage;

    // تعيين الصفحة الحالية بناءً على البيت المحدد
    _currentIndex = widget.selectedIndex ~/ _versesPerPage;
    _pageController = PageController(initialPage: _currentIndex);

    // إعداد التحريك للصفحات
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    // حساب عدد الأبيات في كل صفحة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _calculateVersesPerPage();
    });
  }

  @override
  void didUpdateWidget(MukhammasVerseDisplay oldWidget) {
    super.didUpdateWidget(oldWidget);

    // إذا تغير البيت المحدد، تحديث الصفحة الحالية
    if (widget.selectedIndex != oldWidget.selectedIndex) {
      final newPage = widget.selectedIndex ~/ _versesPerPage;
      if (newPage != _currentIndex) {
        _pageController.animateToPage(
          newPage,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    }

    // إذا تغيرت الأبيات، إعادة حساب عدد الصفحات
    if (widget.verses != oldWidget.verses) {
      _calculateVersesPerPage();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // تحديث حجم الخط إذا تغير في المزود
    final provider = Provider.of<ManhalRawiProvider>(context);
    if (_fontSize != provider.verseDisplaySettings.fontSize) {
      setState(() {
        _fontSize = provider.verseDisplaySettings.fontSize;
      });

      // إعادة حساب عدد الأبيات في الصفحة
      _calculateVersesPerPage();
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  // حساب عدد الأبيات في كل صفحة
  void _calculateVersesPerPage() {
    // التحقق من وجود أبيات
    if (widget.verses.isEmpty) {
      _versesPerPage = 1;
      _totalPages = 0;
      return;
    }

    // استخدام عدد الأبيات من إعدادات المزود
    _versesPerPage = _provider.verseDisplaySettings.versesPerPage;

    // التأكد من أن عدد الأبيات في كل صفحة لا يقل عن 1
    _versesPerPage = math.max(_versesPerPage, 1);

    // حساب إجمالي عدد الصفحات
    _totalPages = (widget.verses.length / _versesPerPage).ceil();
    if (_totalPages == 0) _totalPages = 1;

    // إعادة بناء الواجهة
    if (mounted) {
      setState(() {});
    }
  }

  // زيادة حجم الخط
  void _increaseFontSize() {
    // إخطار بأن المستخدم يتفاعل مع المكون
    widget.onInteractionStateChanged?.call(true);

    setState(() {
      _fontSize = math.min(_fontSize + 2.0, 28.0);
    });

    // حفظ حجم الخط في الإعدادات
    _provider.updateFontSize(_fontSize);

    // إعادة حساب عدد الأبيات في الصفحة
    _calculateVersesPerPage();

    // إخطار بأن التفاعل انتهى بعد فترة قصيرة
    Future.delayed(const Duration(milliseconds: 500), () {
      widget.onInteractionStateChanged?.call(false);
    });
  }

  // تقليل حجم الخط
  void _decreaseFontSize() {
    // إخطار بأن المستخدم يتفاعل مع المكون
    widget.onInteractionStateChanged?.call(true);

    setState(() {
      _fontSize = math.max(_fontSize - 2.0, 12.0);
    });

    // حفظ حجم الخط في الإعدادات
    _provider.updateFontSize(_fontSize);

    // إعادة حساب عدد الأبيات في الصفحة
    _calculateVersesPerPage();

    // إخطار بأن التفاعل انتهى بعد فترة قصيرة
    Future.delayed(const Duration(milliseconds: 500), () {
      widget.onInteractionStateChanged?.call(false);
    });
  }

  // زيادة مقياس التكبير
  void _zoomIn() {
    // إخطار بأن المستخدم يتفاعل مع المكون
    widget.onInteractionStateChanged?.call(true);

    setState(() {
      _scale = math.min(_scale + 0.1, 2.0);
    });

    // تأثير اهتزاز خفيف عند التكبير
    HapticFeedback.lightImpact();

    // إخطار بأن التفاعل انتهى بعد فترة قصيرة
    Future.delayed(const Duration(milliseconds: 500), () {
      widget.onInteractionStateChanged?.call(false);
    });
  }

  // تقليل مقياس التكبير
  void _zoomOut() {
    // إخطار بأن المستخدم يتفاعل مع المكون
    widget.onInteractionStateChanged?.call(true);

    setState(() {
      _scale = math.max(_scale - 0.1, 0.5);
    });

    // تأثير اهتزاز خفيف عند التصغير
    HapticFeedback.lightImpact();

    // إخطار بأن التفاعل انتهى بعد فترة قصيرة
    Future.delayed(const Duration(milliseconds: 500), () {
      widget.onInteractionStateChanged?.call(false);
    });
  }

  @override
  Widget build(BuildContext context) {
    // الحصول على حالة السمة (داكنة أم فاتحة)
    final isDarkMode = _provider.isDarkMode;

    // التحقق من وجود أبيات
    if (widget.verses.isEmpty) {
      return Center(
        child: Text(
          'لا توجد أبيات مخمسة',
          style: IslamicTypography.luxuryTitle(
            isDark: isDarkMode,
            fontSize: 18,
            fontFamily: context.read<ManhalRawiProvider>().fontFamily,
          ),
          textAlign: TextAlign.center,
        ),
      );
    }

    // تعديل عدد الأبيات في كل صفحة بناءً على حجم الشاشة ووضع ملء الشاشة
    int versesPerPage = _versesPerPage;
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isLargeScreen = screenSize.width > 600;
    final isLandscape = screenSize.width > screenSize.height;

    if (widget.isFullScreen) {
      // زيادة عدد الأبيات في وضع ملء الشاشة
      if (isLargeScreen) {
        // للشاشات الكبيرة، زيادة عدد الأبيات بنسبة 30%
        versesPerPage = (versesPerPage * 1.3).round();
      } else if (isLandscape) {
        // للشاشات في وضع أفقي، زيادة عدد الأبيات بنسبة 20%
        versesPerPage = (versesPerPage * 1.2).round();
      } else if (!isSmallScreen) {
        // للشاشات المتوسطة، زيادة عدد الأبيات بنسبة 15%
        versesPerPage = (versesPerPage * 1.15).round();
      }
    } else {
      // تعديل عدد الأبيات في الوضع العادي
      if (isSmallScreen) {
        // تقليل عدد الأبيات للشاشات الصغيرة
        versesPerPage = versesPerPage > 2 ? versesPerPage - 1 : versesPerPage;
      }
    }

    // التأكد من أن عدد الأبيات لا يقل عن 1
    versesPerPage = versesPerPage.clamp(1, 50);

    // إعادة حساب إجمالي عدد الصفحات بناءً على عدد الأبيات المعدل
    final int calculatedTotalPages =
        (widget.verses.length / versesPerPage).ceil();

    // تعديل الأحجام بناءً على حجم الشاشة
    final horizontalPadding = ResponsiveHelper.getResponsiveSpacing(
      context,
      defaultValue: isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0),
    );

    final verticalPadding = ResponsiveHelper.getResponsiveSpacing(
      context,
      defaultValue: isSmallScreen ? 3.0 : (isLargeScreen ? 5.0 : 4.0),
    );

    final borderRadius = ResponsiveHelper.getResponsiveBorderRadius(
      context,
      defaultValue: isSmallScreen ? 16.0 : (isLargeScreen ? 24.0 : 20.0),
    );

    final iconSize = ResponsiveHelper.getResponsiveIconSize(
      context,
      defaultSize: isSmallScreen ? 16.0 : (isLargeScreen ? 24.0 : 20.0),
    );

    final minButtonSize = isSmallScreen ? 32.0 : (isLargeScreen ? 40.0 : 36.0);
    final dividerHeight = isSmallScreen ? 20.0 : (isLargeScreen ? 28.0 : 24.0);
    final dividerMargin = isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0);
    final borderWidth = ResponsiveHelper.getResponsiveBorderWidth(
      context,
      defaultValue: isSmallScreen ? 0.5 : (isLargeScreen ? 1.5 : 1.0),
    );

    // دالة مساعدة لبناء زر في شريط الأدوات
    Widget buildToolbarButton({
      required IconData icon,
      required String tooltip,
      required VoidCallback onPressed,
      required bool isDarkMode,
      required double iconSize,
      required double minButtonSize,
      bool isHighlighted = false,
    }) {
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 2),
        decoration: isHighlighted
            ? BoxDecoration(
                color: isDarkMode
                    ? ManhalColors.gold500.withValues(alpha: 0.2)
                    : ManhalColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(minButtonSize / 2),
              )
            : null,
        child: IconButton(
          icon: Icon(
            icon,
            color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
            size: iconSize,
          ),
          onPressed: onPressed,
          tooltip: tooltip,
          padding: EdgeInsets.all(ResponsiveHelper.getResponsiveSpacing(
            context,
            defaultValue: 8.0,
          )),
          constraints: BoxConstraints(
            minWidth: minButtonSize,
            minHeight: minButtonSize,
          ),
          splashRadius: minButtonSize * 0.7,
          splashColor: isDarkMode
              ? ManhalColors.gold500.withValues(alpha: 0.3)
              : ManhalColors.primary.withValues(alpha: 0.2),
        ),
      );
    }

    // دالة مساعدة لبناء فاصل في شريط الأدوات
    Widget buildToolbarDivider({
      required bool isDarkMode,
      required double dividerHeight,
      required double borderWidth,
      required double dividerMargin,
    }) {
      return Container(
        height: dividerHeight,
        width: borderWidth,
        margin: EdgeInsets.symmetric(horizontal: dividerMargin),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.transparent,
              isDarkMode
                  ? ManhalColors.gold500.withValues(alpha: 0.5)
                  : ManhalColors.gold300,
              Colors.transparent,
            ],
            stops: const [0.0, 0.5, 1.0],
          ),
        ),
      );
    }

    // بناء شريط الأدوات بتصميم فاخر
    final toolbar = Container(
      padding: EdgeInsets.symmetric(
        horizontal: horizontalPadding,
        vertical: verticalPadding,
      ),
      decoration: BoxDecoration(
        color: isDarkMode
            ? ManhalColors.blue900.withValues(alpha: 0.9)
            : ManhalColors.gold100.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withValues(alpha: 0.4)
                : Colors.grey.withValues(alpha: 0.3),
            blurRadius: isSmallScreen ? 3.0 : (isLargeScreen ? 7.0 : 5.0),
            offset:
                Offset(0, isSmallScreen ? 1.0 : (isLargeScreen ? 3.0 : 2.0)),
          ),
        ],
        border: Border.all(
          color: isDarkMode
              ? ManhalColors.gold500.withValues(alpha: 0.4)
              : ManhalColors.gold300,
          width: borderWidth,
        ),
        image: DecorationImage(
          image: AssetImage(
            isDarkMode
                ? 'assets/images/islamic_pattern_dark.png'
                : 'assets/images/islamic_pattern_light.png',
          ),
          opacity: 0.05,
          fit: BoxFit.cover,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // زر تقليل حجم الخط
          buildToolbarButton(
            icon: Icons.text_decrease,
            tooltip: 'تصغير الخط',
            onPressed: _decreaseFontSize,
            isDarkMode: isDarkMode,
            iconSize: iconSize,
            minButtonSize: minButtonSize,
          ),

          // زر زيادة حجم الخط
          buildToolbarButton(
            icon: Icons.text_increase,
            tooltip: 'تكبير الخط',
            onPressed: _increaseFontSize,
            isDarkMode: isDarkMode,
            iconSize: iconSize,
            minButtonSize: minButtonSize,
          ),

          // فاصل زخرفي
          buildToolbarDivider(
            isDarkMode: isDarkMode,
            dividerHeight: dividerHeight,
            borderWidth: borderWidth,
            dividerMargin: dividerMargin,
          ),

          // زر تصغير
          buildToolbarButton(
            icon: Icons.zoom_out,
            tooltip: 'تصغير',
            onPressed: _zoomOut,
            isDarkMode: isDarkMode,
            iconSize: iconSize,
            minButtonSize: minButtonSize,
          ),

          // زر تكبير
          buildToolbarButton(
            icon: Icons.zoom_in,
            tooltip: 'تكبير',
            onPressed: _zoomIn,
            isDarkMode: isDarkMode,
            iconSize: iconSize,
            minButtonSize: minButtonSize,
          ),

          // فاصل زخرفي
          buildToolbarDivider(
            isDarkMode: isDarkMode,
            dividerHeight: dividerHeight,
            borderWidth: borderWidth,
            dividerMargin: dividerMargin,
          ),

          // زر ملء الشاشة
          if (widget.onFullScreenToggle != null)
            buildToolbarButton(
              icon: widget.isFullScreen
                  ? Icons.fullscreen_exit
                  : Icons.fullscreen,
              tooltip: widget.isFullScreen ? 'إلغاء ملء الشاشة' : 'ملء الشاشة',
              onPressed: () {
                HapticFeedback.mediumImpact(); // تأثير اهتزاز عند النقر
                widget.onFullScreenToggle?.call();
              },
              isDarkMode: isDarkMode,
              iconSize: iconSize,
              minButtonSize: minButtonSize,
              isHighlighted: true, // تمييز زر ملء الشاشة
            ),
        ],
      ),
    );

    // بناء المحتوى الرئيسي
    final content = Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // عنوان الصفحة مع شريط الأدوات
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // عنوان الصفحة
              Expanded(
                child: Text(
                  'صفحة ${_currentIndex + 1} من $calculatedTotalPages',
                  style: IslamicTypography.luxuryTitle(
                    isDark: isDarkMode,
                    fontSize: 18,
                    fontFamily: context.read<ManhalRawiProvider>().fontFamily,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              // شريط الأدوات
              toolbar,
            ],
          ),
        ),

        // عرض الأبيات
        Expanded(
          child: AnimatedScale(
            scale: _scale,
            duration: const Duration(milliseconds: 300),
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentIndex = index;
                });
              },
              itemCount: calculatedTotalPages,
              itemBuilder: (context, pageIndex) {
                // حساب مؤشر البداية والنهاية للأبيات في هذه الصفحة
                final startIndex = pageIndex * versesPerPage;
                final endIndex =
                    (startIndex + versesPerPage <= widget.verses.length)
                        ? startIndex + versesPerPage
                        : widget.verses.length;

                // الحصول على الأبيات في هذه الصفحة
                final pageVerses = widget.verses.sublist(startIndex, endIndex);

                return _buildPage(
                  verses: pageVerses,
                  startIndex: startIndex,
                  isDarkMode: isDarkMode,
                );
              },
            ),
          ),
        ),

        // أزرار التنقل
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            textDirection:
                TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
            children: [
              // زر الصفحة السابقة (في اللغة العربية يكون على اليسار)
              IconButton(
                icon: Icon(
                  Icons.arrow_back_ios,
                  color:
                      isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
                ),
                onPressed: _currentIndex > 0
                    ? () {
                        _pageController.previousPage(
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                        );
                      }
                    : null,
              ),

              // مؤشرات الصفحات
              Row(
                textDirection:
                    TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                children: List.generate(
                  calculatedTotalPages,
                  (index) => Container(
                    width: 8,
                    height: 8,
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: index == _currentIndex
                          ? (isDarkMode
                              ? ManhalColors.gold500
                              : ManhalColors.primary)
                          : (isDarkMode
                              ? ManhalColors.gold500.withValues(alpha: 0.3)
                              : ManhalColors.primary.withValues(alpha: 0.3)),
                    ),
                  ),
                ),
              ),

              // زر الصفحة التالية (في اللغة العربية يكون على اليمين)
              IconButton(
                icon: Icon(
                  Icons.arrow_forward_ios,
                  color:
                      isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
                ),
                onPressed: _currentIndex < calculatedTotalPages - 1
                    ? () {
                        _pageController.nextPage(
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                        );
                      }
                    : null,
              ),
            ],
          ),
        ),
      ],
    );

    // إذا كان في وضع ملء الشاشة، نعرض المحتوى في شاشة كاملة مع تأثيرات انتقالية
    if (widget.isFullScreen) {
      return AnimatedContainer(
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOutCubic,
        color: isDarkMode ? ManhalColors.blue900 : Colors.white,
        child: SafeArea(
          child: Scaffold(
            backgroundColor: isDarkMode ? ManhalColors.blue900 : Colors.white,
            body: Stack(
              children: [
                // خلفية زخرفية للوضع الكامل
                Positioned.fill(
                  child: Opacity(
                    opacity: 0.03,
                    child: Image.asset(
                      isDarkMode
                          ? 'assets/images/islamic_pattern_dark.png'
                          : 'assets/images/islamic_pattern_light.png',
                      fit: BoxFit.cover,
                    ),
                  ),
                ),

                // المحتوى الرئيسي
                content,

                // زر العودة للخلف (يظهر فقط عند النقر)
                Positioned(
                  top: 16,
                  right: 16,
                  child: AnimatedOpacity(
                    opacity: 1.0,
                    duration: const Duration(milliseconds: 300),
                    child: Container(
                      decoration: BoxDecoration(
                        color: isDarkMode
                            ? ManhalColors.blue900.withValues(alpha: 0.8)
                            : Colors.white.withValues(alpha: 0.8),
                        borderRadius: BorderRadius.circular(30),
                        boxShadow: [
                          BoxShadow(
                            color: isDarkMode
                                ? Colors.black.withValues(alpha: 0.3)
                                : Colors.grey.withValues(alpha: 0.2),
                            blurRadius: 5,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: IconButton(
                        icon: Icon(
                          Icons.close,
                          color: isDarkMode
                              ? ManhalColors.gold500
                              : ManhalColors.primary,
                        ),
                        onPressed: () {
                          HapticFeedback.mediumImpact();
                          widget.onFullScreenToggle?.call();
                        },
                        tooltip: 'إغلاق وضع ملء الشاشة',
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    // وإلا نعرض المحتوى العادي
    return content;
  }

  // بناء صفحة عرض الأبيات المخمسة
  Widget _buildPage({
    required List<MukhammasVerse> verses,
    required int startIndex,
    required bool isDarkMode,
  }) {
    // استخدام ResponsiveHelper للحصول على قيم متوافقة مع حجم الشاشة
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isLargeScreen = screenSize.width > 600;

    // تعديل الأحجام بناءً على حجم الشاشة
    final containerPadding = ResponsiveHelper.getResponsiveSpacing(
      context,
      defaultValue: isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0),
    );

    final borderRadius = ResponsiveHelper.getResponsiveBorderRadius(
      context,
      defaultValue: isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0),
    );

    final borderWidth = ResponsiveHelper.getResponsiveBorderWidth(
      context,
      defaultValue: isSmallScreen ? 0.5 : (isLargeScreen ? 1.5 : 1.0),
    );

    // استخدام حجم الخط من المزود
    final provider = Provider.of<ManhalRawiProvider>(context, listen: false);
    final baseFontSize = provider.verseDisplaySettings.fontSize;

    // تعديل حجم الخط للعنوان بناءً على حجم الشاشة
    final titleFontSize = ResponsiveHelper.getResponsiveFontSize(
      context,
      fontSize: isSmallScreen
          ? baseFontSize * 0.9
          : (isLargeScreen ? baseFontSize * 1.1 : baseFontSize),
    );

    return Container(
      decoration: BoxDecoration(
        color: isDarkMode
            ? ManhalColors.blue900.withValues(alpha: 0.95)
            : Colors.white,
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withValues(alpha: 0.4)
                : Colors.grey.withValues(alpha: 0.3),
            blurRadius: isSmallScreen ? 6.0 : (isLargeScreen ? 12.0 : 8.0),
            spreadRadius: isSmallScreen ? 0.3 : (isLargeScreen ? 1.0 : 0.5),
            offset:
                Offset(0, isSmallScreen ? 2.0 : (isLargeScreen ? 6.0 : 4.0)),
          ),
        ],
        border: Border.all(
          color: isDarkMode
              ? ManhalColors.gold500.withValues(alpha: 0.4)
              : ManhalColors.gold300.withValues(alpha: 0.6),
          width: borderWidth,
        ),
        image: DecorationImage(
          image: AssetImage(
            isDarkMode
                ? 'assets/images/islamic_pattern_dark.png'
                : 'assets/images/islamic_pattern_light.png',
          ),
          opacity: isDarkMode ? 0.04 : 0.03,
          fit: BoxFit.cover,
        ),
      ),
      margin: EdgeInsets.symmetric(
        horizontal: containerPadding * 0.75,
        vertical: containerPadding * 0.25,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(borderRadius),
        child: Stack(
          children: [
            // خلفية زخرفية في الأعلى
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: Container(
                height: isSmallScreen ? 30.0 : (isLargeScreen ? 60.0 : 40.0),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      isDarkMode
                          ? ManhalColors.gold500.withValues(alpha: 0.15)
                          : ManhalColors.gold100.withValues(alpha: 0.3),
                      isDarkMode
                          ? ManhalColors.blue900.withValues(alpha: 0.0)
                          : Colors.white.withValues(alpha: 0.0),
                    ],
                  ),
                ),
              ),
            ),

            // المحتوى الرئيسي
            SingleChildScrollView(
              controller: _scrollController,
              physics: const BouncingScrollPhysics(),
              padding: EdgeInsets.all(containerPadding),
              child: Column(
                children: [
                  // عنوان الصفحة مع رقم الأبيات - تصميم فاخر
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: containerPadding,
                      vertical: containerPadding * 0.4,
                    ),
                    decoration: BoxDecoration(
                      color: isDarkMode
                          ? ManhalColors.blue800.withValues(alpha: 0.6)
                          : ManhalColors.gold100.withValues(alpha: 0.6),
                      borderRadius: BorderRadius.circular(borderRadius * 0.75),
                      border: Border.all(
                        color: isDarkMode
                            ? ManhalColors.gold500.withValues(alpha: 0.4)
                            : ManhalColors.gold300,
                        width: borderWidth,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: isDarkMode
                              ? Colors.black.withValues(alpha: 0.2)
                              : Colors.grey.withValues(alpha: 0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.menu_book,
                          size: titleFontSize,
                          color: isDarkMode
                              ? ManhalColors.gold500
                              : ManhalColors.primary,
                        ),
                        SizedBox(width: containerPadding * 0.5),
                        Text(
                          'الأبيات ${startIndex + 1} - ${startIndex + verses.length}',
                          style: IslamicTypography.luxuryTitle(
                            isDark: isDarkMode,
                            fontSize: titleFontSize,
                            fontFamily:
                                context.read<ManhalRawiProvider>().fontFamily,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: containerPadding * 0.75),

                  // الأبيات المخمسة
                  ...verses.asMap().entries.map((entry) {
                    final index = entry.key;
                    final verse = entry.value;
                    final verseIndex = startIndex + index;
                    final isSelected = verseIndex == widget.selectedIndex;

                    return _buildMukhammasVerse(
                      verse: verse,
                      verseIndex: verseIndex,
                      isSelected: isSelected,
                      isDarkMode: isDarkMode,
                    );
                  }),

                  SizedBox(height: containerPadding * 0.75),

                  // فاصل زخرفي في النهاية
                  Container(
                    height: 2,
                    width: 150,
                    margin:
                        EdgeInsets.symmetric(vertical: containerPadding * 0.5),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.transparent,
                          isDarkMode
                              ? ManhalColors.gold500.withValues(alpha: 0.6)
                              : ManhalColors.primary.withValues(alpha: 0.6),
                          Colors.transparent,
                        ],
                        stops: const [0.0, 0.5, 1.0],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // بناء البيت المخمس بتصميم كتابي فاخر
  Widget _buildMukhammasVerse({
    required MukhammasVerse verse,
    required int verseIndex,
    required bool isSelected,
    required bool isDarkMode,
  }) {
    // استخدام ResponsiveHelper للحصول على قيم متوافقة مع حجم الشاشة
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isLargeScreen = screenSize.width > 600;

    // تعديل الأحجام بناءً على حجم الشاشة
    // تقليل المسافات لتقريب الأبيات من بعضها بشكل أكبر
    final horizontalPadding = ResponsiveHelper.getResponsiveSpacing(
      context,
      defaultValue: isSmallScreen
          ? 4.0
          : (isLargeScreen ? 8.0 : 6.0), // تم تقليل المسافة بشكل أكبر
    );

    final verticalPadding = ResponsiveHelper.getResponsiveSpacing(
      context,
      defaultValue: isSmallScreen
          ? 2.0
          : (isLargeScreen ? 4.0 : 3.0), // تم تقليل المسافة بشكل أكبر
    );

    // تقليل المسافة العمودية بين الأبيات بشكل أكبر جدًا
    final verticalMargin = ResponsiveHelper.getResponsiveSpacing(
      context,
      defaultValue: isSmallScreen
          ? 0.5
          : (isLargeScreen ? 1.5 : 1.0), // تم تقليل المسافة بشكل أكبر جدًا
    );

    final borderRadius = ResponsiveHelper.getResponsiveBorderRadius(
      context,
      defaultValue: isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0),
    );

    final borderWidth = ResponsiveHelper.getResponsiveBorderWidth(
      context,
      defaultValue: isSmallScreen ? 1.5 : (isLargeScreen ? 2.5 : 2.0),
    );

    final smallBorderWidth = ResponsiveHelper.getResponsiveBorderWidth(
      context,
      defaultValue: isSmallScreen ? 0.3 : (isLargeScreen ? 0.7 : 0.5),
    );

    final iconSize = ResponsiveHelper.getResponsiveIconSize(
      context,
      defaultSize: isSmallScreen
          ? _fontSize * 0.9
          : (isLargeScreen ? _fontSize * 1.2 : _fontSize + 2),
    );

    // تم إزالة متغير decorativePadding لأنه لم يعد مستخدمًا بعد استبدال النجوم بالزخرفة الفاخرة

    return LayoutBuilder(
      builder: (context, constraints) {
        return GestureDetector(
          onTap: () {
            widget.onVerseSelected(verseIndex);
            HapticFeedback.selectionClick(); // تأثير اهتزاز خفيف عند التحديد
          },
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            margin: EdgeInsets.symmetric(vertical: verticalMargin),
            padding: EdgeInsets.symmetric(
                horizontal: horizontalPadding, vertical: verticalPadding),
            decoration: BoxDecoration(
              color: isSelected
                  ? (isDarkMode
                      ? ManhalColors.gold500.withValues(alpha: 0.1)
                      : ManhalColors.gold100.withValues(alpha: 0.3))
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(borderRadius),
              border: Border(
                right: BorderSide(
                  color: isSelected
                      ? (isDarkMode
                          ? ManhalColors.gold500
                          : ManhalColors.primary)
                      : Colors.transparent,
                  width: borderWidth,
                ),
              ),
              boxShadow: isSelected
                  ? [
                      BoxShadow(
                        color: isDarkMode
                            ? Colors.black.withValues(alpha: 0.2)
                            : Colors.grey.withValues(alpha: 0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : null,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // رأس البيت (مخفي)
                // تم إخفاء شعار "البيت رقم X" بناءً على طلب المستخدم

                // مؤشر التحديد (يظهر فقط إذا كان البيت محدداً)
                if (isSelected)
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Container(
                      padding: EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: isDarkMode
                            ? ManhalColors.gold500.withValues(alpha: 0.1)
                            : ManhalColors.gold100.withValues(alpha: 0.3),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.bookmark,
                        size: iconSize,
                        color: isDarkMode
                            ? ManhalColors.gold500
                            : ManhalColors.primary,
                      ),
                    ),
                  ),

                // الأشطر الأربعة الأولى بتنسيق كتابي فاخر
                Container(
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? ManhalColors.blue800.withValues(alpha: 0.2)
                        : ManhalColors.gold100.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(borderRadius),
                    border: Border.all(
                      color: isDarkMode
                          ? ManhalColors.gold500.withValues(alpha: 0.2)
                          : ManhalColors.gold300.withValues(alpha: 0.3),
                      width: smallBorderWidth,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: isDarkMode
                            ? Colors.black.withValues(alpha: 0.1)
                            : Colors.grey.withValues(alpha: 0.05),
                        blurRadius: 2,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  padding: EdgeInsets.symmetric(
                    horizontal: horizontalPadding *
                        0.6, // تم تقليل المسافة الأفقية بشكل أكبر
                    vertical: verticalPadding *
                        0.5, // تم تقليل المسافة العمودية بشكل أكبر
                  ),
                  child: Column(
                    children: [
                      // السطر الأول (الشطران الأول والثاني)
                      _buildVerseLine(
                        rightText: verse.firstLine1,
                        leftText: verse.firstLine2,
                        isDarkMode: isDarkMode,
                        isSelected: isSelected,
                      ),

                      Divider(
                        color: isDarkMode
                            ? ManhalColors.gold500.withValues(alpha: 0.1)
                            : ManhalColors.gold300.withValues(alpha: 0.2),
                        height: verticalPadding *
                            0.5, // تم تقليل ارتفاع الفاصل بشكل أكبر
                        thickness: 0.3, // تم تقليل سماكة الفاصل بشكل أكبر
                      ),

                      // السطر الثاني (الشطران الثالث والرابع)
                      _buildVerseLine(
                        rightText: verse.secondLine1,
                        leftText: verse.secondLine2,
                        isDarkMode: isDarkMode,
                        isSelected: isSelected,
                      ),
                    ],
                  ),
                ),

                // تم إزالة الفاصل الزخرفي قبل الشطر الخامس لتقريب الأبيات
                // إضافة مسافة صغيرة جدًا فقط
                SizedBox(height: 2),

                // الشطر الخامس (في المنتصف) بتصميم مميز وفاخر
                Container(
                  alignment: Alignment.center,
                  padding: EdgeInsets.symmetric(
                    vertical: verticalPadding *
                        0.5, // تم تقليل المسافة العمودية بشكل أكبر
                    horizontal: horizontalPadding *
                        0.6, // تم تقليل المسافة الأفقية بشكل أكبر
                  ),
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? ManhalColors.gold500.withValues(alpha: 0.1)
                        : ManhalColors.gold100.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(borderRadius),
                    border: Border.all(
                      color: isDarkMode
                          ? ManhalColors.gold500.withValues(alpha: 0.3)
                          : ManhalColors.gold300.withValues(alpha: 0.4),
                      width: smallBorderWidth * 1.5,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: isDarkMode
                            ? Colors.black.withValues(alpha: 0.15)
                            : Colors.grey.withValues(alpha: 0.1),
                        blurRadius: 3,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: _buildVersePart(
                    text: verse.fifthLine,
                    isDarkMode: isDarkMode,
                    isSelected: isSelected,
                    isFifthLine: true,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // بناء سطر من البيت (شطرين متقابلين)
  Widget _buildVerseLine({
    required String rightText,
    required String leftText,
    required bool isDarkMode,
    required bool isSelected,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      textDirection: TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
      children: [
        // الشطر الأيمن (يظهر على اليمين في اللغة العربية)
        Expanded(
          child: _buildVersePart(
            text: rightText,
            isDarkMode: isDarkMode,
            isSelected: isSelected,
            alignment: TextAlign.start,
          ),
        ),

        // فاصل زخرفي بين الشطرين (تم تقليل المسافة)
        Padding(
          padding: const EdgeInsets.symmetric(
              horizontal: 4), // تم تقليل المسافة من 8 إلى 4
          child: Container(
            height: _fontSize * 1.1, // تم تقليل الارتفاع قليلاً
            width: 1,
            color: isDarkMode
                ? ManhalColors.gold500.withValues(alpha: 0.2)
                : ManhalColors.primary.withValues(alpha: 0.1),
          ),
        ),

        // الشطر الأيسر (يظهر على اليسار في اللغة العربية)
        Expanded(
          child: _buildVersePart(
            text: leftText,
            isDarkMode: isDarkMode,
            isSelected: isSelected,
            alignment: TextAlign.end,
          ),
        ),
      ],
    );
  }
}

// بناء جزء البيت (شطر)
Widget _buildVersePart({
  required String text,
  required bool isDarkMode,
  required bool isSelected,
  bool isFifthLine = false,
  TextAlign alignment = TextAlign.center,
}) {
  return Builder(
    builder: (context) {
      // تحديد حجم الشاشة لتكييف العناصر
      final screenSize = MediaQuery.of(context).size;
      final isSmallScreen = screenSize.width < 360;
      final isLargeScreen = screenSize.width > 600;

      // تعديل الأحجام بناءً على حجم الشاشة (تم تقليل المسافات)
      final verticalPadding = isSmallScreen
          ? 4.0
          : (isLargeScreen ? 7.0 : 5.0); // تم تقليل المسافة العمودية
      final horizontalPadding = isSmallScreen
          ? 6.0
          : (isLargeScreen ? 12.0 : 8.0); // تم تقليل المسافة الأفقية
      final borderRadius = isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0);
      final borderWidth = isSmallScreen ? 0.3 : (isLargeScreen ? 0.7 : 0.5);

      // الحصول على حجم الخط من المتغير _fontSize
      final provider = Provider.of<ManhalRawiProvider>(context, listen: false);
      final baseFontSize = provider.verseDisplaySettings.fontSize;

      // تعديل حجم الخط بناءً على حجم الشاشة ونوع السطر
      final fontSize = isSmallScreen
          ? baseFontSize * (isFifthLine ? 1.05 : 0.95)
          : (isLargeScreen
              ? baseFontSize * (isFifthLine ? 1.15 : 1.05)
              : baseFontSize * (isFifthLine ? 1.1 : 1.0));

      // تعديل العرض الأقصى للنص بناءً على حجم الشاشة
      final maxTextWidth = isSmallScreen
          ? screenSize.width * 0.35
          : (isLargeScreen ? screenSize.width * 0.45 : screenSize.width * 0.4);

      // تعديل نصف قطر حواف رسالة النسخ
      final snackbarBorderRadius =
          isSmallScreen ? 8.0 : (isLargeScreen ? 12.0 : 10.0);

      return GestureDetector(
        onLongPress: () {
          // نسخ النص إلى الحافظة
          Clipboard.setData(ClipboardData(text: text));
          HapticFeedback.mediumImpact(); // تأثير اهتزاز عند النسخ

          // إظهار رسالة تأكيد النسخ
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم نسخ النص إلى الحافظة',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontFamily: context.read<ManhalRawiProvider>().fontFamily,
                  fontSize:
                      isSmallScreen ? 12.0 : (isLargeScreen ? 16.0 : 14.0),
                  color: isDarkMode ? Colors.black : Colors.white,
                ),
              ),
              backgroundColor:
                  isDarkMode ? ManhalColors.gold300 : ManhalColors.blue800,
              duration: const Duration(seconds: 1),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(snackbarBorderRadius),
              ),
              margin: EdgeInsets.all(
                  isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0)),
            ),
          );
        },
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          padding: EdgeInsets.symmetric(
              vertical: verticalPadding, horizontal: horizontalPadding),
          decoration: BoxDecoration(
            color: isFifthLine
                ? (isDarkMode
                    ? ManhalColors.gold500.withValues(alpha: 0.1)
                    : ManhalColors.gold100.withValues(alpha: 0.3))
                : (isSelected
                    ? (isDarkMode
                        ? ManhalColors.blue800.withValues(alpha: 0.1)
                        : ManhalColors.gold100.withValues(alpha: 0.1))
                    : Colors.transparent),
            borderRadius: BorderRadius.circular(borderRadius),
            border: isFifthLine || isSelected
                ? Border.all(
                    color: isDarkMode
                        ? ManhalColors.gold500.withValues(alpha: 0.3)
                        : ManhalColors.gold300,
                    width: borderWidth,
                  )
                : null,
            boxShadow: null,
          ),
          child: FittedBox(
            fit: BoxFit.scaleDown,
            child: Container(
              constraints: BoxConstraints(
                maxWidth: maxTextWidth,
              ),
              child: Text(
                text,
                overflow: TextOverflow.visible,
                softWrap: true,
                style: IslamicTypography.luxuryBody(
                  isDark: isDarkMode,
                  fontSize: fontSize,
                  fontWeight: isSelected && !isFifthLine
                      ? FontWeight.bold
                      : FontWeight
                          .normal, // تأكيد على إزالة التنسيق الغامق للبيت الخامس
                  fontFamily: context.read<ManhalRawiProvider>().fontFamily,
                ),
                textAlign: alignment,
                textDirection:
                    TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
              ),
            ),
          ),
        ),
      );
    },
  );
}
