import 'package:flutter/material.dart';
import '../../../data/models/poem.dart';

/// مشغل القصائد الصوتي - معطل حالياً
///
/// تم تعطيل ميزة تشغيل الصوت مؤقتاً وسيتم تفعيلها في إصدار لاحق
class PoemPlayer extends StatelessWidget {
  final Poem poem;
  final Function(int) onVerseChanged;

  const PoemPlayer({
    super.key,
    required this.poem,
    required this.onVerseChanged,
  });

  @override
  Widget build(BuildContext context) {
    // هذا المكون معطل حالياً
    return const SizedBox.shrink();
  }
}
