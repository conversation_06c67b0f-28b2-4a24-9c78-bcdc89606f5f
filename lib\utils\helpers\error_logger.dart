import 'package:flutter/foundation.dart' show kDebugMode;

/// خدمة تسجيل الأخطاء في التطبيق
class ErrorLogger {
  static final ErrorLogger _instance = ErrorLogger._internal();
  factory ErrorLogger() => _instance;
  ErrorLogger._internal();

  /// قائمة الأخطاء المسجلة
  final List<LogEntry> _logs = [];

  /// الحصول على قائمة الأخطاء المسجلة
  List<LogEntry> get logs => _logs;

  /// تسجيل خطأ
  void logError(String source, String message, [dynamic error, StackTrace? stackTrace]) {
    final entry = LogEntry(
      timestamp: DateTime.now(),
      source: source,
      message: message,
      error: error,
      stackTrace: stackTrace,
    );

    _logs.add(entry);

    // طباعة الخطأ في وضع التصحيح
    if (kDebugMode) {
      print('🔴 ERROR [$source]: $message');
      if (error != null) {
        print('Details: $error');
      }
      if (stackTrace != null) {
        print('StackTrace: $stackTrace');
      }
    }
  }

  /// تسجيل تحذير
  void logWarning(String source, String message) {
    final entry = LogEntry(
      timestamp: DateTime.now(),
      source: source,
      message: message,
      level: LogLevel.warning,
    );

    _logs.add(entry);

    // طباعة التحذير في وضع التصحيح
    if (kDebugMode) {
      print('🟡 WARNING [$source]: $message');
    }
  }

  /// تسجيل معلومة
  void logInfo(String source, String message) {
    final entry = LogEntry(
      timestamp: DateTime.now(),
      source: source,
      message: message,
      level: LogLevel.info,
    );

    _logs.add(entry);

    // طباعة المعلومة في وضع التصحيح
    if (kDebugMode) {
      print('🔵 INFO [$source]: $message');
    }
  }

  /// مسح جميع السجلات
  void clearLogs() {
    _logs.clear();
  }

  /// الحصول على آخر عدد محدد من السجلات
  List<LogEntry> getRecentLogs(int count) {
    if (_logs.length <= count) {
      return _logs;
    }
    return _logs.sublist(_logs.length - count);
  }

  /// الحصول على السجلات حسب المستوى
  List<LogEntry> getLogsByLevel(LogLevel level) {
    return _logs.where((log) => log.level == level).toList();
  }

  /// الحصول على السجلات حسب المصدر
  List<LogEntry> getLogsBySource(String source) {
    return _logs.where((log) => log.source == source).toList();
  }
}

/// مستويات السجلات
enum LogLevel {
  error,
  warning,
  info,
}

/// نموذج سجل الأخطاء
class LogEntry {
  final DateTime timestamp;
  final String source;
  final String message;
  final dynamic error;
  final StackTrace? stackTrace;
  final LogLevel level;

  LogEntry({
    required this.timestamp,
    required this.source,
    required this.message,
    this.error,
    this.stackTrace,
    this.level = LogLevel.error,
  });

  @override
  String toString() {
    final levelStr = level == LogLevel.error
        ? 'ERROR'
        : level == LogLevel.warning
            ? 'WARNING'
            : 'INFO';
    return '[$levelStr] $timestamp - $source: $message ${error != null ? '- $error' : ''}';
  }
}
