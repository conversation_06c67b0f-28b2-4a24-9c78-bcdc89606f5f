import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:flutter/foundation.dart' show kDebugMode;
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'features/manhal_rawi/data/datasources/local_datasource.dart';
import 'features/manhal_rawi/data/datasources/favorites_local_datasource.dart';
import 'features/manhal_rawi/data/repositories/poems_repository.dart';
import 'features/manhal_rawi/data/datasources/database_helper.dart';

import 'features/manhal_rawi/domain/usecases/get_poets.dart';
import 'features/manhal_rawi/domain/usecases/search_poems.dart';
import 'features/manhal_rawi/domain/usecases/filter_poems.dart';
import 'features/manhal_rawi/domain/usecases/get_book_info.dart';
import 'features/manhal_rawi/domain/usecases/manage_favorites.dart';
import 'features/manhal_rawi/presentation/providers/manhal_rawi_provider.dart';
import 'features/manhal_rawi/presentation/screens/enhanced_luxury_splash_screen.dart';
import 'utils/theme/app_theme.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تعيين اتجاه التطبيق من اليمين إلى اليسار
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // تهيئة قاعدة البيانات
  await initializeDatabase();

  runApp(const MyApp());
}

/// تهيئة قاعدة البيانات والتحقق من تحديثات التطبيق بشكل تلقائي تمامًا
Future<void> initializeDatabase() async {
  try {
    // التحقق من إصدار التطبيق الحالي والسابق
    final prefs = await SharedPreferences.getInstance();
    final String currentVersion = '1.0.7'; // يجب تحديث هذا مع كل إصدار جديد
    final String? previousVersion = prefs.getString('app_version');

    // استدعاء قاعدة البيانات لتهيئتها
    final databaseHelper = DatabaseHelper();
    final db = await databaseHelper.database;

    // التحقق من أن قاعدة البيانات تم تهيئتها بشكل صحيح
    final List<Map<String, dynamic>> tables =
        await db.rawQuery("SELECT name FROM sqlite_master WHERE type='table';");

    if (tables.isEmpty) {
      throw Exception('فشل في تهيئة قاعدة البيانات: لا توجد جداول');
    }

    // التحقق من وجود البيانات في الجداول
    final List<Map<String, dynamic>> poemsCount =
        await db.rawQuery("SELECT COUNT(*) as count FROM poems;");

    final int count = poemsCount.first['count'] as int;

    // التحقق مما إذا كان هذا تثبيتًا جديدًا أو تحديثًا أو قاعدة بيانات فارغة
    bool isNewInstall = previousVersion == null;
    bool isUpdate =
        previousVersion != null && previousVersion != currentVersion;
    bool isDatabaseEmpty = count == 0;

    // تهيئة تلقائية في أي من الحالات التالية:
    // 1. تثبيت جديد
    // 2. تحديث للتطبيق
    // 3. قاعدة بيانات فارغة
    if (isNewInstall || isUpdate || isDatabaseEmpty) {
      if (kDebugMode) {
        if (isNewInstall) {
          print('تثبيت جديد للتطبيق. الإصدار: $currentVersion');
        } else if (isUpdate) {
          print(
              'تحديث التطبيق من الإصدار $previousVersion إلى الإصدار $currentVersion');
        } else {
          print('قاعدة بيانات فارغة. جاري التهيئة التلقائية...');
        }
      }

      // مزامنة البيانات من ملفات JSON إلى قاعدة البيانات بشكل تلقائي
      try {
        await databaseHelper.syncDatabaseFromJson();
        if (kDebugMode) {
          print('تم مزامنة البيانات بنجاح بشكل تلقائي');
        }
      } catch (e) {
        if (kDebugMode) {
          print('خطأ في مزامنة البيانات التلقائية: $e');
        }

        // في حالة الفشل، نحاول إعادة تهيئة قاعدة البيانات بالكامل
        try {
          final String path = join(await getDatabasesPath(), 'manhal_rawi.db');

          if (kDebugMode) {
            print('محاولة إعادة تهيئة قاعدة البيانات في المسار: $path');
          }

          // حذف قاعدة البيانات الحالية وإعادة إنشائها
          await deleteDatabase(path);
          await databaseHelper.database;

          // إعادة تحميل البيانات من ملفات JSON
          await databaseHelper.syncDatabaseFromJson();

          if (kDebugMode) {
            print('تم إعادة تهيئة قاعدة البيانات بنجاح');
          }
        } catch (e2) {
          if (kDebugMode) {
            print('فشل في إعادة تهيئة قاعدة البيانات: $e2');
          }
        }
      }

      // حفظ إصدار التطبيق الحالي بعد التهيئة
      await prefs.setString('app_version', currentVersion);
    } else {
      // قاعدة البيانات موجودة وتحتوي على بيانات
      if (kDebugMode) {
        print('قاعدة البيانات موجودة وتحتوي على بيانات. عدد القصائد: $count');
      }
    }
  } catch (e) {
    // استخدام kDebugMode بدلاً من print مباشرة
    if (kDebugMode) {
      print('Error initializing database: $e');
    }

    // محاولة إعادة تهيئة قاعدة البيانات بالكامل
    try {
      final databaseHelper = DatabaseHelper();
      final String path = join(await getDatabasesPath(), 'manhal_rawi.db');

      if (kDebugMode) {
        print('محاولة إعادة تهيئة قاعدة البيانات في المسار: $path');
      }

      // حذف قاعدة البيانات الحالية وإعادة إنشائها
      await deleteDatabase(path);
      await databaseHelper.database; // تهيئة قاعدة البيانات

      // تحميل البيانات من ملفات JSON مباشرة
      await databaseHelper.syncDatabaseFromJson();

      if (kDebugMode) {
        print('تم إعادة تهيئة قاعدة البيانات بنجاح');
      }

      // حفظ إصدار التطبيق الحالي
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('app_version', '1.0.1'); // استخدام القيمة المباشرة
    } catch (e2) {
      if (kDebugMode) {
        print('فشل في إعادة تهيئة قاعدة البيانات: $e2');
      }
    }
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (context) {
            // إعداد طبقة البيانات
            final localDataSource = LocalDataSource();
            final favoritesDataSource = FavoritesLocalDataSource();
            final repository = PoemsRepository(
              localDataSource: localDataSource,
              favoritesDataSource: favoritesDataSource,
            );

            // إعداد حالات الاستخدام
            final getPoets = GetPoets(repository);
            final searchPoems = SearchPoems(repository);
            final filterPoems = FilterPoems(repository);
            final getBookInfo = GetBookInfo(repository);
            final manageFavorites = ManageFavorites(repository);

            // إنشاء مزود الحالة
            final provider = ManhalRawiProvider(
              getPoets: getPoets,
              searchPoems: searchPoems,
              filterPoems: filterPoems,
              getBookInfo: getBookInfo,
              manageFavorites: manageFavorites,
            );

            // تحميل الإعدادات المحفوظة
            provider.loadSettings();

            return provider;
          },
        ),
      ],
      child: Consumer<ManhalRawiProvider>(
        builder: (context, provider, child) {
          return MaterialApp(
            title: 'المنهل الروي',
            debugShowCheckedModeBanner: false,
            theme: AppTheme.lightTheme(),
            darkTheme: AppTheme.darkTheme(),
            themeMode: provider.isDarkMode ? ThemeMode.dark : ThemeMode.light,
            locale: const Locale('ar', 'SA'),
            home: const EnhancedLuxurySplashScreen(),
          );
        },
      ),
    );
  }
}
