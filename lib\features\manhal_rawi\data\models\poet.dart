class Poet {
  final int id;
  final String name;
  final String fullName;
  final String? title;
  final String era;
  final int birthYear;
  final int? deathYear;
  final String bio;
  final List<String>? achievements;
  final String? imageUrl;

  Poet({
    required this.id,
    required this.name,
    required this.fullName,
    this.title,
    required this.era,
    required this.birthYear,
    this.deathYear,
    required this.bio,
    this.achievements,
    this.imageUrl,
  });

  factory Poet.fromJson(Map<String, dynamic> json) {
    return Poet(
      id: json['id'],
      name: json['name'],
      fullName: json['fullName'],
      title: json['title'],
      era: json['era'],
      birthYear: json['birthYear'],
      deathYear: json['deathYear'],
      bio: json['bio'],
      achievements: json['achievements'] != null
          ? List<String>.from(json['achievements'])
          : null,
      imageUrl: json['imageUrl'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'fullName': fullName,
      'title': title,
      'era': era,
      'birthYear': birthYear,
      'deathYear': deathYear,
      'bio': bio,
      'achievements': achievements,
      'imageUrl': imageUrl,
    };
  }
}
