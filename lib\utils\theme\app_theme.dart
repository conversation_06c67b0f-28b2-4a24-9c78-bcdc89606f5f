import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'manhal_colors.dart';
import 'islamic_typography.dart';
import '../helpers/responsive_helper.dart';

class AppTheme {
  static ThemeData lightTheme([BuildContext? context]) {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: ManhalColors.primary,
        primary: ManhalColors.primary,
        secondary: ManhalColors.secondary,
        surface: ManhalColors.backgroundLight,
        surfaceContainer: Colors.white,
        onSurface: ManhalColors.textDark,
      ),
      scaffoldBackgroundColor: ManhalColors.backgroundLight,
      appBarTheme: AppBarTheme(
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: true,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
        ),
        iconTheme: const IconThemeData(
          color: ManhalColors.primary,
        ),
        titleTextStyle: IslamicTypography.luxuryTitle(
          isDark: false,
          fontSize: context != null
              ? ResponsiveHelper.getResponsiveFontSize(context, fontSize: 24.0)
              : 24.0,
        ),
      ),
      textTheme: const TextTheme(
        displayLarge: TextStyle(fontFamily: 'Scheherazade'),
        displayMedium: TextStyle(fontFamily: 'Scheherazade'),
        displaySmall: TextStyle(fontFamily: 'Scheherazade'),
        headlineLarge: TextStyle(fontFamily: 'Scheherazade'),
        headlineMedium: TextStyle(fontFamily: 'Scheherazade'),
        headlineSmall: TextStyle(fontFamily: 'Scheherazade'),
        titleLarge: TextStyle(fontFamily: 'Scheherazade'),
        titleMedium: TextStyle(fontFamily: 'Amiri'),
        titleSmall: TextStyle(fontFamily: 'Amiri'),
        bodyLarge: TextStyle(fontFamily: 'Amiri'),
        bodyMedium: TextStyle(fontFamily: 'Amiri'),
        bodySmall: TextStyle(fontFamily: 'Amiri'),
        labelLarge: TextStyle(fontFamily: 'ArefRuqaa'),
        labelMedium: TextStyle(fontFamily: 'ArefRuqaa'),
        labelSmall: TextStyle(fontFamily: 'ArefRuqaa'),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: ManhalColors.primary,
          foregroundColor: Colors.white,
          textStyle: TextStyle(
            fontFamily: 'Amiri',
            fontSize: context != null
                ? ResponsiveHelper.getResponsiveFontSize(context,
                    fontSize: 16.0)
                : 16.0,
            fontWeight: FontWeight.bold,
          ),
          padding: EdgeInsets.symmetric(
            horizontal: context != null
                ? ResponsiveHelper.getResponsiveSpacing(context,
                    defaultValue: 24.0)
                : 24.0,
            vertical: context != null
                ? ResponsiveHelper.getResponsiveSpacing(context,
                    defaultValue: 12.0)
                : 12.0,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(
              context != null
                  ? ResponsiveHelper.getResponsiveBorderRadius(context,
                      defaultValue: 12.0)
                  : 12.0,
            ),
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: ManhalColors.primary,
          side: BorderSide(
            color: ManhalColors.primary,
            width: context != null
                ? ResponsiveHelper.getResponsiveBorderWidth(context,
                    defaultValue: 1.5)
                : 1.5,
          ),
          textStyle: TextStyle(
            fontFamily: 'Amiri',
            fontSize: context != null
                ? ResponsiveHelper.getResponsiveFontSize(context,
                    fontSize: 16.0)
                : 16.0,
            fontWeight: FontWeight.bold,
          ),
          padding: EdgeInsets.symmetric(
            horizontal: context != null
                ? ResponsiveHelper.getResponsiveSpacing(context,
                    defaultValue: 24.0)
                : 24.0,
            vertical: context != null
                ? ResponsiveHelper.getResponsiveSpacing(context,
                    defaultValue: 12.0)
                : 12.0,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(
              context != null
                  ? ResponsiveHelper.getResponsiveBorderRadius(context,
                      defaultValue: 12.0)
                  : 12.0,
            ),
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: ManhalColors.primary,
          textStyle: TextStyle(
            fontFamily: 'Amiri',
            fontSize: context != null
                ? ResponsiveHelper.getResponsiveFontSize(context,
                    fontSize: 16.0)
                : 16.0,
            fontWeight: FontWeight.bold,
          ),
          padding: EdgeInsets.symmetric(
            horizontal: context != null
                ? ResponsiveHelper.getResponsiveSpacing(context,
                    defaultValue: 16.0)
                : 16.0,
            vertical: context != null
                ? ResponsiveHelper.getResponsiveSpacing(context,
                    defaultValue: 8.0)
                : 8.0,
          ),
        ),
      ),
      cardTheme: CardTheme(
        color: Colors.white,
        elevation: context != null
            ? 4.0 * ResponsiveHelper.getScaleFactor(context)
            : 4.0,
        shadowColor: Colors.black.withAlpha(26),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
            context != null
                ? ResponsiveHelper.getResponsiveBorderRadius(context,
                    defaultValue: 16.0)
                : 16.0,
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: Colors.white,
        contentPadding: EdgeInsets.symmetric(
          horizontal: context != null
              ? ResponsiveHelper.getResponsiveSpacing(context,
                  defaultValue: 16.0)
              : 16.0,
          vertical: context != null
              ? ResponsiveHelper.getResponsiveSpacing(context,
                  defaultValue: 16.0)
              : 16.0,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(
            context != null
                ? ResponsiveHelper.getResponsiveBorderRadius(context,
                    defaultValue: 12.0)
                : 12.0,
          ),
          borderSide: BorderSide(
            color: ManhalColors.gold300,
            width: context != null
                ? ResponsiveHelper.getResponsiveBorderWidth(context,
                    defaultValue: 1.0)
                : 1.0,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(
            context != null
                ? ResponsiveHelper.getResponsiveBorderRadius(context,
                    defaultValue: 12.0)
                : 12.0,
          ),
          borderSide: BorderSide(
            color: ManhalColors.gold300,
            width: context != null
                ? ResponsiveHelper.getResponsiveBorderWidth(context,
                    defaultValue: 1.0)
                : 1.0,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(
            context != null
                ? ResponsiveHelper.getResponsiveBorderRadius(context,
                    defaultValue: 12.0)
                : 12.0,
          ),
          borderSide: BorderSide(
            color: ManhalColors.primary,
            width: context != null
                ? ResponsiveHelper.getResponsiveBorderWidth(context,
                    defaultValue: 2.0)
                : 2.0,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(
            context != null
                ? ResponsiveHelper.getResponsiveBorderRadius(context,
                    defaultValue: 12.0)
                : 12.0,
          ),
          borderSide: BorderSide(
            color: Colors.red.shade300,
            width: context != null
                ? ResponsiveHelper.getResponsiveBorderWidth(context,
                    defaultValue: 1.0)
                : 1.0,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(
            context != null
                ? ResponsiveHelper.getResponsiveBorderRadius(context,
                    defaultValue: 12.0)
                : 12.0,
          ),
          borderSide: BorderSide(
            color: Colors.red.shade700,
            width: context != null
                ? ResponsiveHelper.getResponsiveBorderWidth(context,
                    defaultValue: 2.0)
                : 2.0,
          ),
        ),
        hintStyle: TextStyle(
          fontFamily: 'Amiri',
          fontSize: context != null
              ? ResponsiveHelper.getResponsiveFontSize(context, fontSize: 16.0)
              : 16.0,
          color: Colors.grey.shade400,
        ),
      ),
    );
  }

  static ThemeData darkTheme([BuildContext? context]) {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: ManhalColors.primary,
        brightness: Brightness.dark,
        primary: ManhalColors.gold500,
        secondary: ManhalColors.blue400,
        surface: ManhalColors.backgroundDark,
        surfaceContainer: ManhalColors.blue900,
        onSurface: ManhalColors.textLight,
      ),
      scaffoldBackgroundColor: ManhalColors.backgroundDark,
      appBarTheme: AppBarTheme(
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: true,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
        ),
        iconTheme: const IconThemeData(
          color: ManhalColors.gold500,
        ),
        titleTextStyle: IslamicTypography.luxuryTitle(
          isDark: true,
          fontSize: context != null
              ? ResponsiveHelper.getResponsiveFontSize(context, fontSize: 24.0)
              : 24.0,
        ),
      ),
      textTheme: TextTheme(
        displayLarge: const TextStyle(
          fontFamily: 'Scheherazade',
          color: ManhalColors.textLight,
        ),
        displayMedium: const TextStyle(
          fontFamily: 'Scheherazade',
          color: ManhalColors.textLight,
        ),
        displaySmall: const TextStyle(
          fontFamily: 'Scheherazade',
          color: ManhalColors.textLight,
        ),
        headlineLarge: const TextStyle(
          fontFamily: 'Scheherazade',
          color: ManhalColors.textLight,
        ),
        headlineMedium: const TextStyle(
          fontFamily: 'Scheherazade',
          color: ManhalColors.textLight,
        ),
        headlineSmall: const TextStyle(
          fontFamily: 'Scheherazade',
          color: ManhalColors.textLight,
        ),
        titleLarge: const TextStyle(
          fontFamily: 'Scheherazade',
          color: ManhalColors.textLight,
        ),
        titleMedium: const TextStyle(
          fontFamily: 'Amiri',
          color: ManhalColors.textLight,
        ),
        titleSmall: const TextStyle(
          fontFamily: 'Amiri',
          color: ManhalColors.textLight,
        ),
        bodyLarge: const TextStyle(
          fontFamily: 'Amiri',
          color: ManhalColors.textLight,
        ),
        bodyMedium: const TextStyle(
          fontFamily: 'Amiri',
          color: ManhalColors.textLight,
        ),
        bodySmall: const TextStyle(
          fontFamily: 'Amiri',
          color: ManhalColors.textLight,
        ),
        labelLarge: const TextStyle(
          fontFamily: 'ArefRuqaa',
          color: ManhalColors.textLight,
        ),
        labelMedium: const TextStyle(
          fontFamily: 'ArefRuqaa',
          color: ManhalColors.textLight,
        ),
        labelSmall: const TextStyle(
          fontFamily: 'ArefRuqaa',
          color: ManhalColors.textLight,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: ManhalColors.gold500,
          foregroundColor: ManhalColors.textDark,
          textStyle: TextStyle(
            fontFamily: 'Amiri',
            fontSize: context != null
                ? ResponsiveHelper.getResponsiveFontSize(context,
                    fontSize: 16.0)
                : 16.0,
            fontWeight: FontWeight.bold,
          ),
          padding: EdgeInsets.symmetric(
            horizontal: context != null
                ? ResponsiveHelper.getResponsiveSpacing(context,
                    defaultValue: 24.0)
                : 24.0,
            vertical: context != null
                ? ResponsiveHelper.getResponsiveSpacing(context,
                    defaultValue: 12.0)
                : 12.0,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(
              context != null
                  ? ResponsiveHelper.getResponsiveBorderRadius(context,
                      defaultValue: 12.0)
                  : 12.0,
            ),
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: ManhalColors.gold500,
          side: BorderSide(
            color: ManhalColors.gold500,
            width: context != null
                ? ResponsiveHelper.getResponsiveBorderWidth(context,
                    defaultValue: 1.5)
                : 1.5,
          ),
          textStyle: TextStyle(
            fontFamily: 'Amiri',
            fontSize: context != null
                ? ResponsiveHelper.getResponsiveFontSize(context,
                    fontSize: 16.0)
                : 16.0,
            fontWeight: FontWeight.bold,
          ),
          padding: EdgeInsets.symmetric(
            horizontal: context != null
                ? ResponsiveHelper.getResponsiveSpacing(context,
                    defaultValue: 24.0)
                : 24.0,
            vertical: context != null
                ? ResponsiveHelper.getResponsiveSpacing(context,
                    defaultValue: 12.0)
                : 12.0,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(
              context != null
                  ? ResponsiveHelper.getResponsiveBorderRadius(context,
                      defaultValue: 12.0)
                  : 12.0,
            ),
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: ManhalColors.gold500,
          textStyle: TextStyle(
            fontFamily: 'Amiri',
            fontSize: context != null
                ? ResponsiveHelper.getResponsiveFontSize(context,
                    fontSize: 16.0)
                : 16.0,
            fontWeight: FontWeight.bold,
          ),
          padding: EdgeInsets.symmetric(
            horizontal: context != null
                ? ResponsiveHelper.getResponsiveSpacing(context,
                    defaultValue: 16.0)
                : 16.0,
            vertical: context != null
                ? ResponsiveHelper.getResponsiveSpacing(context,
                    defaultValue: 8.0)
                : 8.0,
          ),
        ),
      ),
      cardTheme: CardTheme(
        color: ManhalColors.blue900,
        elevation: context != null
            ? 4.0 * ResponsiveHelper.getScaleFactor(context)
            : 4.0,
        shadowColor: Colors.black.withAlpha(77),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
            context != null
                ? ResponsiveHelper.getResponsiveBorderRadius(context,
                    defaultValue: 16.0)
                : 16.0,
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: ManhalColors.blue800,
        contentPadding: EdgeInsets.symmetric(
          horizontal: context != null
              ? ResponsiveHelper.getResponsiveSpacing(context,
                  defaultValue: 16.0)
              : 16.0,
          vertical: context != null
              ? ResponsiveHelper.getResponsiveSpacing(context,
                  defaultValue: 16.0)
              : 16.0,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(
            context != null
                ? ResponsiveHelper.getResponsiveBorderRadius(context,
                    defaultValue: 12.0)
                : 12.0,
          ),
          borderSide: BorderSide(
            color: ManhalColors.blue700,
            width: context != null
                ? ResponsiveHelper.getResponsiveBorderWidth(context,
                    defaultValue: 1.0)
                : 1.0,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(
            context != null
                ? ResponsiveHelper.getResponsiveBorderRadius(context,
                    defaultValue: 12.0)
                : 12.0,
          ),
          borderSide: BorderSide(
            color: ManhalColors.blue700,
            width: context != null
                ? ResponsiveHelper.getResponsiveBorderWidth(context,
                    defaultValue: 1.0)
                : 1.0,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(
            context != null
                ? ResponsiveHelper.getResponsiveBorderRadius(context,
                    defaultValue: 12.0)
                : 12.0,
          ),
          borderSide: BorderSide(
            color: ManhalColors.gold500,
            width: context != null
                ? ResponsiveHelper.getResponsiveBorderWidth(context,
                    defaultValue: 2.0)
                : 2.0,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(
            context != null
                ? ResponsiveHelper.getResponsiveBorderRadius(context,
                    defaultValue: 12.0)
                : 12.0,
          ),
          borderSide: BorderSide(
            color: Colors.red.shade300,
            width: context != null
                ? ResponsiveHelper.getResponsiveBorderWidth(context,
                    defaultValue: 1.0)
                : 1.0,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(
            context != null
                ? ResponsiveHelper.getResponsiveBorderRadius(context,
                    defaultValue: 12.0)
                : 12.0,
          ),
          borderSide: BorderSide(
            color: Colors.red.shade700,
            width: context != null
                ? ResponsiveHelper.getResponsiveBorderWidth(context,
                    defaultValue: 2.0)
                : 2.0,
          ),
        ),
        hintStyle: TextStyle(
          fontFamily: 'Amiri',
          fontSize: context != null
              ? ResponsiveHelper.getResponsiveFontSize(context, fontSize: 16.0)
              : 16.0,
          color: Colors.grey.shade500,
        ),
      ),
    );
  }
}
