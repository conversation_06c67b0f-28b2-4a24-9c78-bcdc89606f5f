import 'package:flutter/material.dart';

/// مساعد للتصميم المتجاوب
/// يوفر دوال وثوابت للتكيف مع أحجام الشاشات المختلفة
class ResponsiveHelper {
  // ثوابت لتحديد أحجام الشاشات
  static const double _smallScreenWidth = 360.0;
  static const double _mediumScreenWidth = 600.0;
  static const double _largeScreenWidth = 900.0;
  
  /// التحقق مما إذا كانت الشاشة صغيرة (أقل من 360 بكسل)
  static bool isSmallScreen(BuildContext context) {
    return MediaQuery.of(context).size.width < _smallScreenWidth;
  }
  
  /// التحقق مما إذا كانت الشاشة متوسطة (بين 360 و 600 بكسل)
  static bool isMediumScreen(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= _smallScreenWidth && width < _mediumScreenWidth;
  }
  
  /// التحقق مما إذا كانت الشاشة كبيرة (بين 600 و 900 بكسل)
  static bool isLargeScreen(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= _mediumScreenWidth && width < _largeScreenWidth;
  }
  
  /// التحقق مما إذا كانت الشاشة كبيرة جدًا (أكبر من 900 بكسل)
  static bool isExtraLargeScreen(BuildContext context) {
    return MediaQuery.of(context).size.width >= _largeScreenWidth;
  }
  
  /// الحصول على معامل القياس المناسب لحجم الشاشة
  /// يستخدم لضبط الأحجام بناءً على حجم الشاشة
  static double getScaleFactor(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    
    if (width < _smallScreenWidth) {
      return 0.85; // تصغير بنسبة 15% للشاشات الصغيرة
    } else if (width < _mediumScreenWidth) {
      return 1.0; // الحجم الافتراضي للشاشات المتوسطة
    } else if (width < _largeScreenWidth) {
      return 1.15; // تكبير بنسبة 15% للشاشات الكبيرة
    } else {
      return 1.25; // تكبير بنسبة 25% للشاشات الكبيرة جدًا
    }
  }
  
  /// الحصول على حجم الخط المتجاوب
  /// @param context سياق البناء
  /// @param fontSize الحجم الافتراضي للخط
  /// @param minSize الحد الأدنى لحجم الخط (اختياري)
  /// @param maxSize الحد الأقصى لحجم الخط (اختياري)
  static double getResponsiveFontSize(
    BuildContext context, {
    required double fontSize,
    double? minSize,
    double? maxSize,
  }) {
    final scaleFactor = getScaleFactor(context);
    final calculatedSize = fontSize * scaleFactor;
    
    // التأكد من أن حجم الخط ضمن الحدود المسموح بها
    if (minSize != null && calculatedSize < minSize) {
      return minSize;
    }
    
    if (maxSize != null && calculatedSize > maxSize) {
      return maxSize;
    }
    
    return calculatedSize;
  }
  
  /// الحصول على الهوامش المتجاوبة
  /// @param context سياق البناء
  /// @param defaultValue القيمة الافتراضية للهامش
  static EdgeInsets getResponsiveMargin(
    BuildContext context, {
    double defaultValue = 16.0,
  }) {
    final scaleFactor = getScaleFactor(context);
    final value = defaultValue * scaleFactor;
    
    return EdgeInsets.all(value);
  }
  
  /// الحصول على الهوامش المتجاوبة الأفقية
  /// @param context سياق البناء
  /// @param defaultValue القيمة الافتراضية للهامش
  static EdgeInsets getResponsiveHorizontalMargin(
    BuildContext context, {
    double defaultValue = 16.0,
  }) {
    final scaleFactor = getScaleFactor(context);
    final value = defaultValue * scaleFactor;
    
    return EdgeInsets.symmetric(horizontal: value);
  }
  
  /// الحصول على الهوامش المتجاوبة العمودية
  /// @param context سياق البناء
  /// @param defaultValue القيمة الافتراضية للهامش
  static EdgeInsets getResponsiveVerticalMargin(
    BuildContext context, {
    double defaultValue = 16.0,
  }) {
    final scaleFactor = getScaleFactor(context);
    final value = defaultValue * scaleFactor;
    
    return EdgeInsets.symmetric(vertical: value);
  }
  
  /// الحصول على الحشوات المتجاوبة
  /// @param context سياق البناء
  /// @param defaultValue القيمة الافتراضية للحشوة
  static EdgeInsets getResponsivePadding(
    BuildContext context, {
    double defaultValue = 16.0,
  }) {
    final scaleFactor = getScaleFactor(context);
    final value = defaultValue * scaleFactor;
    
    return EdgeInsets.all(value);
  }
  
  /// الحصول على الحشوات المتجاوبة الأفقية
  /// @param context سياق البناء
  /// @param defaultValue القيمة الافتراضية للحشوة
  static EdgeInsets getResponsiveHorizontalPadding(
    BuildContext context, {
    double defaultValue = 16.0,
  }) {
    final scaleFactor = getScaleFactor(context);
    final value = defaultValue * scaleFactor;
    
    return EdgeInsets.symmetric(horizontal: value);
  }
  
  /// الحصول على الحشوات المتجاوبة العمودية
  /// @param context سياق البناء
  /// @param defaultValue القيمة الافتراضية للحشوة
  static EdgeInsets getResponsiveVerticalPadding(
    BuildContext context, {
    double defaultValue = 16.0,
  }) {
    final scaleFactor = getScaleFactor(context);
    final value = defaultValue * scaleFactor;
    
    return EdgeInsets.symmetric(vertical: value);
  }
  
  /// الحصول على نصف قطر الحدود المتجاوب
  /// @param context سياق البناء
  /// @param defaultValue القيمة الافتراضية لنصف القطر
  static double getResponsiveBorderRadius(
    BuildContext context, {
    double defaultValue = 16.0,
  }) {
    final scaleFactor = getScaleFactor(context);
    return defaultValue * scaleFactor;
  }
  
  /// الحصول على سماكة الحدود المتجاوبة
  /// @param context سياق البناء
  /// @param defaultValue القيمة الافتراضية للسماكة
  static double getResponsiveBorderWidth(
    BuildContext context, {
    double defaultValue = 1.0,
  }) {
    final scaleFactor = getScaleFactor(context);
    return defaultValue * scaleFactor;
  }
  
  /// الحصول على حجم الأيقونات المتجاوب
  /// @param context سياق البناء
  /// @param defaultSize الحجم الافتراضي للأيقونة
  static double getResponsiveIconSize(
    BuildContext context, {
    double defaultSize = 24.0,
  }) {
    final scaleFactor = getScaleFactor(context);
    return defaultSize * scaleFactor;
  }
  
  /// الحصول على المسافة المتجاوبة بين العناصر
  /// @param context سياق البناء
  /// @param defaultValue القيمة الافتراضية للمسافة
  static double getResponsiveSpacing(
    BuildContext context, {
    double defaultValue = 8.0,
  }) {
    final scaleFactor = getScaleFactor(context);
    return defaultValue * scaleFactor;
  }
  
  /// الحصول على ارتفاع الزر المتجاوب
  /// @param context سياق البناء
  /// @param defaultHeight الارتفاع الافتراضي للزر
  static double getResponsiveButtonHeight(
    BuildContext context, {
    double defaultHeight = 48.0,
  }) {
    final scaleFactor = getScaleFactor(context);
    return defaultHeight * scaleFactor;
  }
  
  /// الحصول على عرض الزر المتجاوب
  /// @param context سياق البناء
  /// @param defaultWidth العرض الافتراضي للزر
  static double getResponsiveButtonWidth(
    BuildContext context, {
    double defaultWidth = 120.0,
  }) {
    final scaleFactor = getScaleFactor(context);
    return defaultWidth * scaleFactor;
  }
  
  /// الحصول على حجم الظل المتجاوب
  /// @param context سياق البناء
  /// @param defaultBlurRadius نصف قطر التمويه الافتراضي
  /// @param defaultSpreadRadius نصف قطر الانتشار الافتراضي
  /// @param defaultYOffset الإزاحة العمودية الافتراضية
  static List<double> getResponsiveShadow(
    BuildContext context, {
    double defaultBlurRadius = 10.0,
    double defaultSpreadRadius = 0.0,
    double defaultYOffset = 5.0,
  }) {
    final scaleFactor = getScaleFactor(context);
    return [
      defaultBlurRadius * scaleFactor,
      defaultSpreadRadius * scaleFactor,
      defaultYOffset * scaleFactor,
    ];
  }
  
  /// الحصول على حجم الصورة المتجاوب
  /// @param context سياق البناء
  /// @param defaultSize الحجم الافتراضي للصورة
  static double getResponsiveImageSize(
    BuildContext context, {
    double defaultSize = 100.0,
  }) {
    final scaleFactor = getScaleFactor(context);
    return defaultSize * scaleFactor;
  }
  
  /// الحصول على عرض البطاقة المتجاوب
  /// @param context سياق البناء
  /// @param defaultWidth العرض الافتراضي للبطاقة
  /// @param screenWidthPercentage نسبة عرض الشاشة (اختياري)
  static double getResponsiveCardWidth(
    BuildContext context, {
    double defaultWidth = 300.0,
    double? screenWidthPercentage,
  }) {
    if (screenWidthPercentage != null) {
      return MediaQuery.of(context).size.width * screenWidthPercentage;
    }
    
    final scaleFactor = getScaleFactor(context);
    return defaultWidth * scaleFactor;
  }
  
  /// الحصول على ارتفاع البطاقة المتجاوب
  /// @param context سياق البناء
  /// @param defaultHeight الارتفاع الافتراضي للبطاقة
  static double getResponsiveCardHeight(
    BuildContext context, {
    double defaultHeight = 200.0,
  }) {
    final scaleFactor = getScaleFactor(context);
    return defaultHeight * scaleFactor;
  }
  
  /// الحصول على عدد الأعمدة المناسب لحجم الشاشة
  /// @param context سياق البناء
  static int getResponsiveGridColumnCount(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    
    if (width < _smallScreenWidth) {
      return 1; // عمود واحد للشاشات الصغيرة
    } else if (width < _mediumScreenWidth) {
      return 2; // عمودان للشاشات المتوسطة
    } else if (width < _largeScreenWidth) {
      return 3; // ثلاثة أعمدة للشاشات الكبيرة
    } else {
      return 4; // أربعة أعمدة للشاشات الكبيرة جدًا
    }
  }
  
  /// الحصول على نسبة العرض إلى الارتفاع المناسبة لحجم الشاشة
  /// @param context سياق البناء
  /// @param defaultAspectRatio النسبة الافتراضية
  static double getResponsiveAspectRatio(
    BuildContext context, {
    double defaultAspectRatio = 16 / 9,
  }) {
    if (isSmallScreen(context)) {
      return defaultAspectRatio * 0.8; // نسبة أكثر ارتفاعًا للشاشات الصغيرة
    } else if (isLargeScreen(context) || isExtraLargeScreen(context)) {
      return defaultAspectRatio * 1.2; // نسبة أكثر عرضًا للشاشات الكبيرة
    }
    
    return defaultAspectRatio;
  }
  
  /// الحصول على حجم الخط المناسب للعنوان حسب حجم الشاشة
  /// @param context سياق البناء
  static double getTitleFontSize(BuildContext context) {
    if (isSmallScreen(context)) {
      return 24.0;
    } else if (isMediumScreen(context)) {
      return 28.0;
    } else if (isLargeScreen(context)) {
      return 32.0;
    } else {
      return 36.0;
    }
  }
  
  /// الحصول على حجم الخط المناسب للعنوان الفرعي حسب حجم الشاشة
  /// @param context سياق البناء
  static double getSubtitleFontSize(BuildContext context) {
    if (isSmallScreen(context)) {
      return 18.0;
    } else if (isMediumScreen(context)) {
      return 20.0;
    } else if (isLargeScreen(context)) {
      return 22.0;
    } else {
      return 24.0;
    }
  }
  
  /// الحصول على حجم الخط المناسب للنص العادي حسب حجم الشاشة
  /// @param context سياق البناء
  static double getBodyFontSize(BuildContext context) {
    if (isSmallScreen(context)) {
      return 14.0;
    } else if (isMediumScreen(context)) {
      return 16.0;
    } else if (isLargeScreen(context)) {
      return 18.0;
    } else {
      return 20.0;
    }
  }
  
  /// الحصول على حجم الخط المناسب للنص الصغير حسب حجم الشاشة
  /// @param context سياق البناء
  static double getCaptionFontSize(BuildContext context) {
    if (isSmallScreen(context)) {
      return 12.0;
    } else if (isMediumScreen(context)) {
      return 14.0;
    } else if (isLargeScreen(context)) {
      return 16.0;
    } else {
      return 18.0;
    }
  }
  
  /// الحصول على حجم الخط المناسب لعنوان القصيدة حسب حجم الشاشة
  /// @param context سياق البناء
  static double getPoemTitleFontSize(BuildContext context) {
    if (isSmallScreen(context)) {
      return 22.0;
    } else if (isMediumScreen(context)) {
      return 26.0;
    } else if (isLargeScreen(context)) {
      return 30.0;
    } else {
      return 34.0;
    }
  }
  
  /// الحصول على حجم الخط المناسب للأبيات الشعرية حسب حجم الشاشة
  /// @param context سياق البناء
  static double getVerseFontSize(BuildContext context) {
    if (isSmallScreen(context)) {
      return 16.0;
    } else if (isMediumScreen(context)) {
      return 18.0;
    } else if (isLargeScreen(context)) {
      return 20.0;
    } else {
      return 22.0;
    }
  }
  
  /// الحصول على حجم الخط المناسب لاسم المؤلف حسب حجم الشاشة
  /// @param context سياق البناء
  static double getAuthorNameFontSize(BuildContext context) {
    if (isSmallScreen(context)) {
      return 20.0;
    } else if (isMediumScreen(context)) {
      return 22.0;
    } else if (isLargeScreen(context)) {
      return 24.0;
    } else {
      return 26.0;
    }
  }
  
  /// الحصول على حجم الخط المناسب للتصنيفات حسب حجم الشاشة
  /// @param context سياق البناء
  static double getCategoryFontSize(BuildContext context) {
    if (isSmallScreen(context)) {
      return 14.0;
    } else if (isMediumScreen(context)) {
      return 16.0;
    } else if (isLargeScreen(context)) {
      return 18.0;
    } else {
      return 20.0;
    }
  }
  
  /// الحصول على حجم الخط المناسب للاقتباسات حسب حجم الشاشة
  /// @param context سياق البناء
  static double getQuoteFontSize(BuildContext context) {
    if (isSmallScreen(context)) {
      return 16.0;
    } else if (isMediumScreen(context)) {
      return 18.0;
    } else if (isLargeScreen(context)) {
      return 20.0;
    } else {
      return 22.0;
    }
  }
  
  /// الحصول على حجم الخط المناسب للأزرار حسب حجم الشاشة
  /// @param context سياق البناء
  static double getButtonFontSize(BuildContext context) {
    if (isSmallScreen(context)) {
      return 14.0;
    } else if (isMediumScreen(context)) {
      return 16.0;
    } else if (isLargeScreen(context)) {
      return 18.0;
    } else {
      return 20.0;
    }
  }
  
  /// الحصول على حجم الخط المناسب لعنوان الكتاب حسب حجم الشاشة
  /// @param context سياق البناء
  static double getBookTitleFontSize(BuildContext context) {
    if (isSmallScreen(context)) {
      return 28.0;
    } else if (isMediumScreen(context)) {
      return 32.0;
    } else if (isLargeScreen(context)) {
      return 36.0;
    } else {
      return 40.0;
    }
  }
}
