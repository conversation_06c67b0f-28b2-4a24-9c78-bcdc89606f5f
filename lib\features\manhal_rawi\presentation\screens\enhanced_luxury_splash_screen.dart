import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/manhal_rawi_provider.dart';
import 'luxury_home_screen.dart';
import '../../../../utils/theme/manhal_colors.dart';
import '../../../../utils/theme/islamic_typography.dart';
import '../../../../utils/theme/islamic_patterns.dart';
import '../../../../utils/animations/luxury_animations.dart';
import '../../../../utils/helpers/responsive_helper.dart';

/// شاشة البداية الفاخرة المحسنة للتطبيق
class EnhancedLuxurySplashScreen extends StatefulWidget {
  const EnhancedLuxurySplashScreen({super.key});

  @override
  State<EnhancedLuxurySplashScreen> createState() =>
      _EnhancedLuxurySplashScreenState();
}

class _EnhancedLuxurySplashScreenState extends State<EnhancedLuxurySplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  // مؤقت للانتقال إلى الشاشة الرئيسية
  Timer? _navigationTimer;

  // حالة تحميل البيانات
  bool _isDataLoaded = false;

  // مؤشر التقدم
  double _loadingProgress = 0.0;

  @override
  void initState() {
    super.initState();

    // إنشاء متحكم الرسوم المتحركة
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 5000),
    );

    // تعيين منحنى الحركة
    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        // يمكن إضافة أي إجراء بعد اكتمال الرسوم المتحركة
      }
    });

    // بدء الرسوم المتحركة
    _controller.forward();

    // محاكاة تقدم التحميل
    _simulateLoading();

    // تحميل البيانات الأولية
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadInitialData();
    });
  }

  /// تحميل البيانات الأولية
  Future<void> _loadInitialData() async {
    try {
      await context.read<ManhalRawiProvider>().loadInitialData();

      if (mounted) {
        setState(() {
          _isDataLoaded = true;
        });

        // ضبط مؤقت للانتقال إلى الشاشة الرئيسية بعد اكتمال التحميل
        _navigationTimer = Timer(const Duration(milliseconds: 1500), () {
          if (mounted) {
            _navigateToHome();
          }
        });
      }
    } catch (e) {
      // في حالة حدوث خطأ، ننتقل إلى الشاشة الرئيسية بعد فترة
      if (mounted) {
        _navigationTimer = Timer(const Duration(milliseconds: 3000), () {
          if (mounted) {
            _navigateToHome();
          }
        });
      }
    }
  }

  /// محاكاة تقدم التحميل
  void _simulateLoading() {
    const totalSteps = 20;
    const stepDuration = Duration(milliseconds: 150);

    int currentStep = 0;

    Timer.periodic(stepDuration, (timer) {
      if (!mounted || _isDataLoaded) {
        timer.cancel();
        setState(() {
          _loadingProgress = 1.0;
        });
        return;
      }

      currentStep++;

      if (currentStep <= totalSteps) {
        setState(() {
          // زيادة تدريجية في التقدم مع تباطؤ في النهاية
          _loadingProgress = math.pow(currentStep / totalSteps, 0.8).toDouble();
        });
      } else {
        timer.cancel();
      }
    });
  }

  /// الانتقال إلى الشاشة الرئيسية
  void _navigateToHome() {
    Navigator.of(context).pushReplacement(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const LuxuryHomeScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation,
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 800),
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    _navigationTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = context.watch<ManhalRawiProvider>().isDarkMode;
    final size = MediaQuery.of(context).size;

    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: isDarkMode
                ? [
                    ManhalColors.blue900,
                    ManhalColors.blue800,
                  ]
                : [
                    Colors.white,
                    ManhalColors.gold100.withValues(alpha: 0.3),
                  ],
          ),
        ),
        child: Stack(
          children: [
            // جسيمات ذهبية متحركة
            LuxuryAnimations.goldenParticles(
              isDark: isDarkMode,
              particleCount: 30,
              child: const SizedBox.expand(),
            ),

            // زخارف الخلفية
            LuxuryAnimations.islamicPatternReveal(
              isDark: isDarkMode,
              child: const SizedBox.expand(),
            ),

            // الهلال العلوي
            Positioned(
              top: size.height * 0.05,
              right: size.width * 0.1,
              child: LuxuryAnimations.rotate3D(
                isDark: isDarkMode,
                axis: 'z',
                maxRotation: 0.05,
                child: LuxuryAnimations.goldenReveal(
                  isDark: isDarkMode,
                  child: Transform.rotate(
                    angle: -0.3,
                    child: IslamicPatterns.getCrescentDecoration(
                      isDark: isDarkMode,
                      size: ResponsiveHelper.getResponsiveIconSize(
                        context,
                        defaultSize: 70.0,
                      ),
                      opacity: 0.8,
                    ),
                  ),
                ),
              ),
            ),

            // الهلال السفلي
            Positioned(
              bottom: size.height * 0.05,
              left: size.width * 0.1,
              child: LuxuryAnimations.rotate3D(
                isDark: isDarkMode,
                axis: 'z',
                maxRotation: 0.05,
                durationMs: 3000,
                child: LuxuryAnimations.goldenReveal(
                  isDark: isDarkMode,
                  delayMs: 500,
                  child: Transform.rotate(
                    angle: 0.3,
                    child: IslamicPatterns.getCrescentDecoration(
                      isDark: isDarkMode,
                      size: ResponsiveHelper.getResponsiveIconSize(
                        context,
                        defaultSize: 50.0,
                      ),
                      opacity: 0.8,
                    ),
                  ),
                ),
              ),
            ),

            // المحتوى الرئيسي
            Center(
              child: LuxuryAnimations.staggeredFadeIn(
                isDark: isDarkMode,
                baseDurationMs: 800,
                staggerMs: 300,
                children: [
                  // الشعار
                  _buildEnhancedLogo(isDarkMode),

                  SizedBox(
                    height: ResponsiveHelper.getResponsiveSpacing(
                      context,
                      defaultValue: 40.0,
                    ),
                  ),

                  // عنوان التطبيق
                  _buildAppTitle(isDarkMode),

                  SizedBox(
                    height: ResponsiveHelper.getResponsiveSpacing(
                      context,
                      defaultValue: 16.0,
                    ),
                  ),

                  // اسم المؤلف
                  _buildAuthorName(isDarkMode),

                  SizedBox(
                    height: ResponsiveHelper.getResponsiveSpacing(
                      context,
                      defaultValue: 60.0,
                    ),
                  ),

                  // مؤشر التحميل
                  _buildEnhancedLoadingIndicator(isDarkMode),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء شعار التطبيق المحسن
  Widget _buildEnhancedLogo(bool isDarkMode) {
    final logoSize = 130.0 * ResponsiveHelper.getScaleFactor(context);

    return LuxuryAnimations.pulseWithGlow(
      isDark: isDarkMode,
      child: Container(
        width: logoSize,
        height: logoSize,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: RadialGradient(
            colors: isDarkMode
                ? [
                    ManhalColors.blue800,
                    ManhalColors.blue900,
                  ]
                : [
                    Colors.white,
                    ManhalColors.gold100.withValues(alpha: 0.5),
                  ],
            radius: 0.8,
          ),
          boxShadow: [
            BoxShadow(
              color: (isDarkMode ? ManhalColors.gold500 : ManhalColors.primary)
                  .withValues(alpha: 0.3),
              blurRadius: ResponsiveHelper.getResponsiveShadow(
                context,
                defaultBlurRadius: 20.0,
              )[0],
              spreadRadius: ResponsiveHelper.getResponsiveShadow(
                context,
                defaultSpreadRadius: 2.0,
              )[1],
              offset: Offset(
                  0,
                  ResponsiveHelper.getResponsiveShadow(
                    context,
                    defaultYOffset: 0.0,
                  )[2]),
            ),
          ],
          border: Border.all(
            color: isDarkMode
                ? ManhalColors.gold500.withValues(alpha: 0.5)
                : ManhalColors.primary.withValues(alpha: 0.3),
            width: ResponsiveHelper.getResponsiveBorderWidth(
              context,
              defaultValue: 2.0,
            ),
          ),
        ),
        child: Center(
          child: Icon(
            Icons.auto_stories,
            size: ResponsiveHelper.getResponsiveIconSize(
              context,
              defaultSize: 70.0,
            ),
            color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
          ),
        ),
      ),
    );
  }

  /// بناء عنوان التطبيق
  Widget _buildAppTitle(bool isDarkMode) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: ResponsiveHelper.getResponsiveSpacing(
          context,
          defaultValue: 24.0,
        ),
        vertical: ResponsiveHelper.getResponsiveSpacing(
          context,
          defaultValue: 12.0,
        ),
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(
          ResponsiveHelper.getResponsiveBorderRadius(
            context,
            defaultValue: 24.0,
          ),
        ),
        border: Border.all(
          color: isDarkMode
              ? ManhalColors.gold500.withValues(alpha: 0.3)
              : ManhalColors.primary.withValues(alpha: 0.2),
          width: ResponsiveHelper.getResponsiveBorderWidth(
            context,
            defaultValue: 2.0,
          ),
        ),
        gradient: LinearGradient(
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
          colors: isDarkMode
              ? [
                  ManhalColors.blue800.withValues(alpha: 0.5),
                  ManhalColors.blue900.withValues(alpha: 0.3),
                ]
              : [
                  Colors.white.withValues(alpha: 0.7),
                  ManhalColors.gold100.withValues(alpha: 0.3),
                ],
        ),
        boxShadow: [
          BoxShadow(
            color: (isDarkMode ? ManhalColors.gold500 : ManhalColors.primary)
                .withValues(alpha: 0.2),
            blurRadius: ResponsiveHelper.getResponsiveShadow(
              context,
              defaultBlurRadius: 15.0,
            )[0],
            spreadRadius: ResponsiveHelper.getResponsiveShadow(
              context,
              defaultSpreadRadius: 1.0,
            )[1],
            offset: Offset(
                0,
                ResponsiveHelper.getResponsiveShadow(
                  context,
                  defaultYOffset: 0.0,
                )[2]),
          ),
        ],
      ),
      child: LuxuryAnimations.luxuryTextReveal(
        text: 'المنهل الروي',
        style: IslamicTypography.luxuryBookTitle(
          isDark: isDarkMode,
          fontSize: ResponsiveHelper.getResponsiveFontSize(
            context,
            fontSize: 44.0,
          ),
        ),
        isDark: isDarkMode,
        durationMs: 2000,
        delayMs: 300,
      ),
    );
  }

  /// بناء اسم المؤلف
  Widget _buildAuthorName(bool isDarkMode) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: ResponsiveHelper.getResponsiveSpacing(
          context,
          defaultValue: 20.0,
        ),
        vertical: ResponsiveHelper.getResponsiveSpacing(
          context,
          defaultValue: 10.0,
        ),
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(
          ResponsiveHelper.getResponsiveBorderRadius(
            context,
            defaultValue: 20.0,
          ),
        ),
        color: isDarkMode
            ? ManhalColors.blue900.withValues(alpha: 0.3)
            : Colors.white.withValues(alpha: 0.3),
        border: Border.all(
          color: isDarkMode
              ? ManhalColors.gold500.withValues(alpha: 0.2)
              : ManhalColors.primary.withValues(alpha: 0.1),
          width: ResponsiveHelper.getResponsiveBorderWidth(
            context,
            defaultValue: 1.5,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: (isDarkMode ? ManhalColors.gold500 : ManhalColors.primary)
                .withValues(alpha: 0.1),
            blurRadius: ResponsiveHelper.getResponsiveShadow(
              context,
              defaultBlurRadius: 10.0,
            )[0],
            spreadRadius: ResponsiveHelper.getResponsiveShadow(
              context,
              defaultSpreadRadius: 1.0,
            )[1],
            offset: Offset(
                0,
                ResponsiveHelper.getResponsiveShadow(
                  context,
                  defaultYOffset: 0.0,
                )[2]),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.person,
            size: ResponsiveHelper.getResponsiveIconSize(
              context,
              defaultSize: 18.0,
            ),
            color: isDarkMode ? ManhalColors.gold300 : ManhalColors.primary,
          ),
          SizedBox(
            width: ResponsiveHelper.getResponsiveSpacing(
              context,
              defaultValue: 10.0,
            ),
          ),
          Text(
            'محمد هزاع باعلوي الحضرمي',
            style: IslamicTypography.luxurySubtitle(
              isDark: isDarkMode,
              fontSize: ResponsiveHelper.getResponsiveFontSize(
                context,
                fontSize: 20.0,
              ),
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء مؤشر التحميل المحسن
  Widget _buildEnhancedLoadingIndicator(bool isDarkMode) {
    final indicatorSize = 80.0 * ResponsiveHelper.getScaleFactor(context);
    final progressSize = 60.0 * ResponsiveHelper.getScaleFactor(context);

    return Column(
      children: [
        // مؤشر التحميل المخصص
        Container(
          width: indicatorSize,
          height: indicatorSize,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isDarkMode
                ? ManhalColors.blue800.withValues(alpha: 0.3)
                : Colors.white.withValues(alpha: 0.5),
            border: Border.all(
              color: isDarkMode
                  ? ManhalColors.gold500.withValues(alpha: 0.5)
                  : ManhalColors.primary.withValues(alpha: 0.3),
              width: ResponsiveHelper.getResponsiveBorderWidth(
                context,
                defaultValue: 1.5,
              ),
            ),
            boxShadow: [
              BoxShadow(
                color:
                    (isDarkMode ? ManhalColors.gold500 : ManhalColors.primary)
                        .withValues(alpha: 0.2),
                blurRadius: ResponsiveHelper.getResponsiveShadow(
                  context,
                  defaultBlurRadius: 15.0,
                )[0],
                spreadRadius: ResponsiveHelper.getResponsiveShadow(
                  context,
                  defaultSpreadRadius: 1.0,
                )[1],
                offset: Offset(
                    0,
                    ResponsiveHelper.getResponsiveShadow(
                      context,
                      defaultYOffset: 0.0,
                    )[2]),
              ),
            ],
          ),
          child: Center(
            child: Stack(
              alignment: Alignment.center,
              children: [
                // مؤشر التقدم الدائري
                SizedBox(
                  width: progressSize,
                  height: progressSize,
                  child: CircularProgressIndicator(
                    value: _loadingProgress,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
                    ),
                    strokeWidth: 4 * ResponsiveHelper.getScaleFactor(context),
                    backgroundColor: isDarkMode
                        ? ManhalColors.blue900.withValues(alpha: 0.3)
                        : ManhalColors.gold100.withValues(alpha: 0.3),
                  ),
                ),

                // نسبة التقدم
                Text(
                  '${(_loadingProgress * 100).toInt()}%',
                  style: TextStyle(
                    fontFamily: 'Amiri',
                    fontSize: ResponsiveHelper.getResponsiveFontSize(
                      context,
                      fontSize: 16.0,
                    ),
                    fontWeight: FontWeight.bold,
                    color: isDarkMode
                        ? ManhalColors.gold500
                        : ManhalColors.primary,
                  ),
                ),
              ],
            ),
          ),
        ),

        SizedBox(
          height: ResponsiveHelper.getResponsiveSpacing(
            context,
            defaultValue: 20.0,
          ),
        ),

        // نص التحميل
        AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          padding: EdgeInsets.symmetric(
            horizontal: ResponsiveHelper.getResponsiveSpacing(
              context,
              defaultValue: 20.0,
            ),
            vertical: ResponsiveHelper.getResponsiveSpacing(
              context,
              defaultValue: 10.0,
            ),
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(
              ResponsiveHelper.getResponsiveBorderRadius(
                context,
                defaultValue: 20.0,
              ),
            ),
            color: isDarkMode
                ? ManhalColors.blue900.withValues(alpha: 0.5)
                : Colors.white.withValues(alpha: 0.5),
            border: Border.all(
              color: isDarkMode
                  ? ManhalColors.gold500.withValues(alpha: 0.2)
                  : ManhalColors.primary.withValues(alpha: 0.1),
              width: ResponsiveHelper.getResponsiveBorderWidth(
                context,
                defaultValue: 1.5,
              ),
            ),
            boxShadow: [
              BoxShadow(
                color:
                    (isDarkMode ? ManhalColors.gold500 : ManhalColors.primary)
                        .withValues(alpha: 0.1),
                blurRadius: ResponsiveHelper.getResponsiveShadow(
                  context,
                  defaultBlurRadius: 10.0,
                )[0],
                spreadRadius: ResponsiveHelper.getResponsiveShadow(
                  context,
                  defaultSpreadRadius: 1.0,
                )[1],
                offset: Offset(
                    0,
                    ResponsiveHelper.getResponsiveShadow(
                      context,
                      defaultYOffset: 0.0,
                    )[2]),
              ),
            ],
          ),
          child: Text(
            _isDataLoaded ? 'تم تحميل الديوان بنجاح' : 'جاري تحميل الديوان...',
            style: IslamicTypography.luxuryCaption(
              isDark: isDarkMode,
              fontSize: ResponsiveHelper.getResponsiveFontSize(
                context,
                fontSize: 18.0,
              ),
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }
}
