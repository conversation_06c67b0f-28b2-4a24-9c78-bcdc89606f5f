name: manhal_rawi
description: "تطبيق المنهل الروي - ديوان محمد هزاع باعلوي الحضرمي"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.6.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # State management
  provider: ^6.1.1
  flutter_riverpod: ^2.4.9

  # UI components
  flutter_svg: ^2.0.9
  animated_text_kit: ^4.2.2
  lottie: ^2.7.0

  # Utilities
  path_provider: ^2.1.1
  shared_preferences: ^2.5.3
  intl: ^0.19.0
  sqflite: ^2.3.0
  path: ^1.8.3

  # Animation
  flutter_animate: ^4.5.0

  # Sharing
  share_plus: ^7.2.1

  # URL Launcher
  url_launcher: ^6.2.5

  # Logging
  logging: ^1.2.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

  # App icon generator
  flutter_launcher_icons: ^0.13.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Assets for the application
  assets:
    - assets/images/
    - assets/images/poets/
    - assets/icons/
    - assets/animations/
    - assets/data/
    - assets/fonts/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # يمكن استخدام Google Fonts بالإضافة إلى الخطوط المخصصة
  # لتوفير تجربة مستخدم أفضل وضمان توافق الخطوط

  # تعريف الخطوط المخصصة
  fonts:
    # 1. Amiri
    - family: Amiri
      fonts:
        - asset: assets/fonts/Amiri-Regular.ttf
          weight: 400
        - asset: assets/fonts/Amiri-Bold.ttf
          weight: 700
        - asset: assets/fonts/Amiri-Italic.ttf
          weight: 400
          style: italic
        - asset: assets/fonts/Amiri-BoldItalic.ttf
          weight: 700
          style: italic

    # 2. Cairo
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
          weight: 400
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
        - asset: assets/fonts/Cairo-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Cairo-Light.ttf
          weight: 300

    # 3. Scheherazade
    - family: Scheherazade
      fonts:
        - asset: assets/fonts/ScheherazadeNew-Regular.ttf
          weight: 400
        - asset: assets/fonts/ScheherazadeNew-Bold.ttf
          weight: 700

    # 4. Naskh
    - family: Naskh
      fonts:
        - asset: assets/fonts/NotoNaskhArabic-Regular.ttf
          weight: 400
        - asset: assets/fonts/NotoNaskhArabic-Bold.ttf
          weight: 700

    # 5. ReemKufi
    - family: ReemKufi
      fonts:
        - asset: assets/fonts/ReemKufi-Regular.ttf
          weight: 400
        - asset: assets/fonts/ReemKufi-Bold.ttf
          weight: 700

    # 6. ArefRuqaa
    - family: ArefRuqaa
      fonts:
        - asset: assets/fonts/ArefRuqaa-Regular.ttf
          weight: 400
        - asset: assets/fonts/ArefRuqaa-Bold.ttf
          weight: 700

    # 7. Tajawal
    - family: Tajawal
      fonts:
        - asset: assets/fonts/Tajawal-Regular.ttf
          weight: 400
        - asset: assets/fonts/Tajawal-Bold.ttf
          weight: 700
        - asset: assets/fonts/Tajawal-Medium.ttf
          weight: 500

    # 8. Lateef
    - family: Lateef
      fonts:
        - asset: assets/fonts/Lateef-Regular.ttf
          weight: 400
        - asset: assets/fonts/Lateef-Bold.ttf
          weight: 700

    # 9. Markazi
    - family: Markazi
      fonts:
        - asset: assets/fonts/MarkaziText-Regular.ttf
          weight: 400
        - asset: assets/fonts/MarkaziText-Bold.ttf
          weight: 700

  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
