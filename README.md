# المنهل الراوي

تطبيق لعرض ديوان الشعر للشاعر محمد هزاع باعلوي الحضرمي.

## نظرة عامة

تطبيق المنهل الراوي هو تطبيق فلاتر يعرض ديوان الشعر للشاعر محمد هزاع باعلوي الحضرمي. يتميز التطبيق بتصميم إسلامي فاخر وواجهة مستخدم سهلة الاستخدام تتيح للمستخدمين استعراض القصائد وقراءتها بطريقة مريحة.

## الميزات الرئيسية

- **تصميم إسلامي فاخر**: واجهة مستخدم أنيقة مستوحاة من التصاميم الإسلامية التقليدية.
- **عرض القصائد بأنماط متعددة**:
  - نمط الكتاب المفتوح: يعرض الأبيات بتنسيق كتاب مفتوح مع صفحات متقابلة.
  - النمط الاحترافي: يعرض الأبيات بشكل احترافي متقابل.
- **تخصيص تجربة القراءة**:
  - تغيير حجم الخط لتسهيل القراءة.
  - اختيار نوع الخط المناسب.
  - وضع القراءة المريح (الوضع الداكن/الفاتح).
- **تصفح القصائد**: تصفح القصائد حسب الحرف الأول أو الشاعر أو التصنيف.
- **البحث**: البحث في القصائد حسب العنوان أو المحتوى.
- **المفضلة**: إضافة القصائد المفضلة للوصول إليها بسهولة.
- **المشاركة**: مشاركة القصائد مع الآخرين.

## التقنيات المستخدمة

- **Flutter**: إطار عمل تطوير واجهات المستخدم.
- **Provider**: لإدارة حالة التطبيق.
- **SQLite**: لتخزين البيانات محلياً.
- **Google Fonts**: لاستخدام خطوط متنوعة.
- **Feature-first with Clean Architecture**: نمط هيكلة التطبيق.

## تحديثات الإصدار الأخير

### الإصدار 1.1.0

- إضافة نمط عرض الكتاب المفتوح للأبيات.
- إضافة خيارات تخصيص القراءة (حجم الخط، نوع الخط).
- تحسين واجهة المستخدم وتجربة القراءة.
- إصلاح مشكلات متعلقة بعرض الأبيات.

## البدء

1. تأكد من تثبيت Flutter على جهازك.
2. استنسخ المستودع:
   ```
   git clone https://github.com/yourusername/manhal_rawi.git
   ```
3. انتقل إلى دليل المشروع:
   ```
   cd manhal_rawi
   ```
4. قم بتثبيت التبعيات:
   ```
   flutter pub get
   ```
5. قم بتشغيل التطبيق:
   ```
   flutter run
   ```

## المساهمة

نرحب بالمساهمات! إذا كنت ترغب في المساهمة في هذا المشروع، يرجى اتباع الخطوات التالية:

1. قم بعمل fork للمستودع.
2. قم بإنشاء فرع جديد للميزة التي ترغب في إضافتها.
3. قم بإجراء التغييرات.
4. قم بإرسال طلب سحب (Pull Request).

## الترخيص

هذا المشروع مرخص بموجب رخصة MIT - انظر ملف LICENSE للحصول على التفاصيل.
#   m a n h a l _ r a w i 
 
 