import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../data/models/poem.dart';
import '../../data/models/poet.dart';
import '../../data/models/category.dart';
import '../../data/models/book_info.dart';

import '../../domain/usecases/get_poets.dart';
import '../../domain/usecases/search_poems.dart';
import '../../domain/usecases/filter_poems.dart';
import '../../domain/usecases/get_book_info.dart';
import '../../domain/usecases/manage_favorites.dart';

import '../../../../utils/helpers/error_logger.dart';
import '../../../../utils/helpers/arabic_text_helper.dart';

enum ManhalRawiStatus { initial, loading, loaded, error }

/// أنواع عرض القصائد المخمسة
enum MukhammasDisplayType {
  /// النمط القياسي (الافتراضي)
  standard,

  /// النمط الفاخر
  luxury,

  /// النمط الكلاسيكي
  classic,
}

/// أنواع عرض القصائد العادية
enum VerseDisplayType {
  /// النمط الاحترافي
  professional,

  /// نمط الكتاب المفتوح
  bookStyle,

  /// النمط الكلاسيكي
  classic,
}

/// إعدادات عرض الأبيات
class VerseDisplaySettings {
  final double fontSize;
  final String fontFamily;
  final bool useBookStyle;
  final int versesPerPage;

  const VerseDisplaySettings({
    required this.fontSize,
    required this.fontFamily,
    required this.useBookStyle,
    required this.versesPerPage,
  });

  VerseDisplaySettings copyWith({
    double? fontSize,
    String? fontFamily,
    bool? useBookStyle,
    int? versesPerPage,
  }) {
    return VerseDisplaySettings(
      fontSize: fontSize ?? this.fontSize,
      fontFamily: fontFamily ?? this.fontFamily,
      useBookStyle: useBookStyle ?? this.useBookStyle,
      versesPerPage: versesPerPage ?? this.versesPerPage,
    );
  }
}

class ManhalRawiProvider extends ChangeNotifier {
  final GetPoets _getPoets;
  final SearchPoems _searchPoems;
  final FilterPoems _filterPoems;
  final GetBookInfo _getBookInfo;
  final ManageFavorites _manageFavorites;

  // مسجل الأخطاء
  final ErrorLogger _errorLogger = ErrorLogger();

  ManhalRawiStatus _status = ManhalRawiStatus.initial;
  List<Poem> _poems = [];
  List<Poet> _poets = [];
  List<Category> _categories = [];
  List<Poem> _filteredPoems = [];
  BookInfo? _bookInfo;
  String _errorMessage = '';
  bool _isDarkMode = false;
  int? _selectedCategoryId;
  int? _selectedPoetId;
  String _searchQuery = '';
  String? _selectedLetter; // الحرف المختار للفهرس الهجائي

  // إعدادات عرض القصائد
  double _fontSize = 12.0; // حجم الخط الافتراضي
  String _fontFamily = 'Amiri'; // نوع الخط الافتراضي
  bool _useBookStyle = true; // استخدام نمط الكتاب المفتوح
  int _versesPerPage = 20; // عدد الأبيات في الصفحة الواحدة (الافتراضي: 20)
  MukhammasDisplayType _mukhammasDisplayType = MukhammasDisplayType
      .standard; // نوع عرض القصائد المخمسة (تم تغييره إلى النمط القياسي)
  VerseDisplayType _verseDisplayType = VerseDisplayType
      .bookStyle; // نوع عرض القصائد العادية (تم تغييره إلى نمط الكتاب المفتوح)

  // متغير للتحكم في ظهور رسالة الترحيب
  bool _hasSeenWelcomeMessage = false; // هل شاهد المستخدم رسالة الترحيب

  // قائمة القصائد المفضلة
  List<Poem> _favoritePoems = [];
  bool _isFavoritesLoading = false;

  ManhalRawiProvider({
    required GetPoets getPoets,
    required SearchPoems searchPoems,
    required FilterPoems filterPoems,
    required GetBookInfo getBookInfo,
    required ManageFavorites manageFavorites,
  })  : _getPoets = getPoets,
        _searchPoems = searchPoems,
        _filterPoems = filterPoems,
        _getBookInfo = getBookInfo,
        _manageFavorites = manageFavorites;

  ManhalRawiStatus get status => _status;
  List<Poem> get poems => _poems;
  List<Poet> get poets => _poets;
  List<Category> get categories => _categories;
  List<Poem> get filteredPoems {
    // إذا كان هناك بحث نشط وكانت النتائج فارغة، نعيد قائمة فارغة
    if (_searchQuery.isNotEmpty && _filteredPoems.isEmpty) {
      return [];
    }

    // إذا كان هناك تصفية نشطة (حرف، تصنيف، شاعر، بحث)، نعيد القصائد المصفاة
    if (_selectedLetter != null ||
        _selectedCategoryId != null ||
        _selectedPoetId != null ||
        _searchQuery.isNotEmpty) {
      return _filteredPoems;
    }

    // في حالة عدم وجود تصفية نشطة، نعيد جميع القصائد
    return _poems;
  }

  BookInfo? get bookInfo => _bookInfo;
  String get errorMessage => _errorMessage;
  bool get isDarkMode => _isDarkMode;
  int? get selectedCategoryId => _selectedCategoryId;
  int? get selectedPoetId => _selectedPoetId;
  String get searchQuery => _searchQuery;
  String? get selectedLetter => _selectedLetter;

  // خصائص إعدادات عرض القصائد
  double get fontSize => _fontSize;
  String get fontFamily => _fontFamily;
  bool get useBookStyle => _useBookStyle;
  int get versesPerPage => _versesPerPage;
  MukhammasDisplayType get mukhammasDisplayType => _mukhammasDisplayType;
  VerseDisplayType get verseDisplayType => _verseDisplayType;

  // خاصية للتحقق من ظهور رسالة الترحيب
  bool get hasSeenWelcomeMessage => _hasSeenWelcomeMessage;

  /// الحصول على إعدادات عرض الأبيات
  VerseDisplaySettings get verseDisplaySettings => VerseDisplaySettings(
        fontSize: _fontSize,
        fontFamily: _fontFamily,
        useBookStyle: _useBookStyle,
        versesPerPage: _versesPerPage,
      );

  // خصائص المفضلات
  List<Poem> get favoritePoems => _favoritePoems;
  bool get isFavoritesLoading => _isFavoritesLoading;

  /// تبديل وضع القراءة المريح (الوضع الداكن/الفاتح)
  ///
  /// يقوم بتبديل حالة الوضع المظلم وحفظها في الإعدادات وإشعار المستمعين بالتغيير
  Future<void> toggleDarkMode() async {
    _isDarkMode = !_isDarkMode;

    // حفظ الإعداد في SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isDarkMode', _isDarkMode);

    notifyListeners();
  }

  /// تعيين وضع القراءة المريح (الوضع الداكن/الفاتح)
  ///
  /// يقوم بتعيين حالة الوضع المظلم وإشعار المستمعين بالتغيير
  void setDarkMode(bool value) {
    if (_isDarkMode != value) {
      _isDarkMode = value;
      notifyListeners();
    }
  }

  /// تحميل الإعدادات المحفوظة
  ///
  /// يقوم بتحميل الإعدادات المحفوظة من SharedPreferences
  Future<void> loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // تحميل إعداد الوضع المظلم
      final isDarkMode = prefs.getBool('isDarkMode');
      if (isDarkMode != null) {
        _isDarkMode = isDarkMode;
      }

      // تحميل إعداد حجم الخط
      final fontSize = prefs.getDouble('fontSize');
      if (fontSize != null) {
        _fontSize = fontSize;
      }

      // تحميل إعداد نوع الخط
      final fontFamily = prefs.getString('fontFamily');
      if (fontFamily != null) {
        _fontFamily = fontFamily;
      }

      // تحميل إعداد نمط العرض
      final useBookStyle = prefs.getBool('useBookStyle');
      if (useBookStyle != null) {
        _useBookStyle = useBookStyle;
      }

      // تحميل إعداد عدد الأبيات في الصفحة الواحدة
      final versesPerPage = prefs.getInt('versesPerPage');
      if (versesPerPage != null) {
        _versesPerPage = versesPerPage;
      }

      // تحميل إعداد نوع عرض القصائد المخمسة
      final mukhammasDisplayTypeIndex = prefs.getInt('mukhammasDisplayType');
      if (mukhammasDisplayTypeIndex != null) {
        _mukhammasDisplayType =
            MukhammasDisplayType.values[mukhammasDisplayTypeIndex];
      }

      // تحميل إعداد نوع عرض القصائد العادية
      final verseDisplayTypeIndex = prefs.getInt('verseDisplayType');
      if (verseDisplayTypeIndex != null) {
        _verseDisplayType = VerseDisplayType.values[verseDisplayTypeIndex];
      }

      // تحميل حالة رسالة الترحيب
      final hasSeenWelcomeMessage = prefs.getBool('hasSeenWelcomeMessage');
      if (hasSeenWelcomeMessage != null) {
        _hasSeenWelcomeMessage = hasSeenWelcomeMessage;
      }

      notifyListeners();
    } catch (e, stackTrace) {
      // تسجيل الخطأ
      _errorLogger.logError(
        'ManhalRawiProvider',
        'فشل في تحميل الإعدادات',
        e,
        stackTrace,
      );
    }
  }

  /// تعيين حالة رسالة الترحيب كمشاهدة
  ///
  /// يقوم بتعيين حالة رسالة الترحيب كمشاهدة وحفظها في الإعدادات
  Future<void> markWelcomeMessageAsSeen() async {
    try {
      _hasSeenWelcomeMessage = true;

      // حفظ الإعداد في SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('hasSeenWelcomeMessage', true);

      notifyListeners();
    } catch (e, stackTrace) {
      // تسجيل الخطأ
      _errorLogger.logError(
        'ManhalRawiProvider',
        'فشل في حفظ حالة رسالة الترحيب',
        e,
        stackTrace,
      );
    }
  }

  /// تعيين حجم الخط
  ///
  /// يقوم بتعيين حجم الخط وحفظه في الإعدادات وإشعار المستمعين بالتغيير
  Future<void> setFontSize(double size) async {
    if (_fontSize != size) {
      _fontSize = size;

      // حفظ الإعداد في SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble('fontSize', _fontSize);

      notifyListeners();
    }
  }

  /// تعيين نوع الخط
  ///
  /// يقوم بتعيين نوع الخط وحفظه في الإعدادات وإشعار المستمعين بالتغيير
  Future<void> setFontFamily(String fontFamily) async {
    if (_fontFamily != fontFamily) {
      _fontFamily = fontFamily;

      // حفظ الإعداد في SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('fontFamily', _fontFamily);

      notifyListeners();
    }
  }

  /// تبديل نمط عرض الأبيات
  ///
  /// يقوم بتبديل بين نمط الكتاب المفتوح والنمط العادي وحفظ الإعداد في الإعدادات وإشعار المستمعين بالتغيير
  Future<void> toggleBookStyle() async {
    _useBookStyle = !_useBookStyle;

    // حفظ الإعداد في SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('useBookStyle', _useBookStyle);

    notifyListeners();
  }

  /// تعيين عدد الأبيات في الصفحة الواحدة
  ///
  /// يقوم بتعيين عدد الأبيات في الصفحة الواحدة وحفظه في الإعدادات وإشعار المستمعين بالتغيير
  Future<void> setVersesPerPage(int count) async {
    if (_versesPerPage != count) {
      _versesPerPage = count;

      // حفظ الإعداد في SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('versesPerPage', _versesPerPage);

      notifyListeners();
    }
  }

  /// تحديث حجم الخط
  ///
  /// يقوم بتحديث حجم الخط وحفظه في الإعدادات وإشعار المستمعين بالتغيير
  Future<void> updateFontSize(double size) async {
    if (_fontSize != size) {
      _fontSize = size;

      // حفظ الإعداد في SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble('fontSize', _fontSize);

      notifyListeners();
    }
  }

  /// تحديث عدد الأبيات في الصفحة الواحدة
  ///
  /// يقوم بتحديث عدد الأبيات في الصفحة الواحدة وحفظه في الإعدادات وإشعار المستمعين بالتغيير
  Future<void> updateVersesPerPage(int count) async {
    if (_versesPerPage != count) {
      _versesPerPage = count;

      // حفظ الإعداد في SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('versesPerPage', _versesPerPage);

      notifyListeners();
    }
  }

  /// تحديث نوع عرض القصائد المخمسة
  ///
  /// يقوم بتحديث نوع عرض القصائد المخمسة وحفظه في الإعدادات وإشعار المستمعين بالتغيير
  Future<void> updateMukhammasDisplayType(MukhammasDisplayType type) async {
    if (_mukhammasDisplayType != type) {
      _mukhammasDisplayType = type;

      // حفظ الإعداد في SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('mukhammasDisplayType', _mukhammasDisplayType.index);

      notifyListeners();
    }
  }

  /// تحديث نوع عرض القصائد العادية
  ///
  /// يقوم بتحديث نوع عرض القصائد العادية وحفظه في الإعدادات وإشعار المستمعين بالتغيير
  Future<void> updateVerseDisplayType(VerseDisplayType type) async {
    if (_verseDisplayType != type) {
      _verseDisplayType = type;

      // حفظ الإعداد في SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('verseDisplayType', _verseDisplayType.index);

      notifyListeners();
    }
  }

  /// تحميل البيانات الأولية للتطبيق
  ///
  /// يقوم بتحميل القصائد والشعراء والتصنيفات ومعلومات الكتاب
  /// ويقوم بتحديث حالة التطبيق وإشعار المستمعين بالتغيير
  Future<void> loadInitialData() async {
    _status = ManhalRawiStatus.loading;
    notifyListeners();

    try {
      // تحميل البيانات الأساسية
      final poets = await _getPoets();
      final categories = await _getPoets.repository.getCategories();
      final bookInfo = await _getBookInfo();

      // تحميل القصائد مع حالة المفضلة
      final poems = await _manageFavorites.getPoemsWithFavoriteStatus();

      // تحميل القصائد المفضلة
      final favoritePoems = await _manageFavorites.getFavorites();

      // التأكد من أن جميع البيانات غير فارغة
      if (poets.isEmpty || categories.isEmpty || poems.isEmpty) {
        throw Exception("فشل في تحميل البيانات الأساسية");
      }

      _poems = poems;
      _poets = poets;
      _categories = categories;
      _bookInfo = bookInfo;
      _favoritePoems = favoritePoems;
      _filteredPoems = [];
      _status = ManhalRawiStatus.loaded;
    } catch (e, stackTrace) {
      _status = ManhalRawiStatus.error;
      _errorMessage = e.toString();

      // تسجيل الخطأ
      _errorLogger.logError(
        'ManhalRawiProvider',
        'فشل في تحميل البيانات الأولية',
        e,
        stackTrace,
      );

      // تهيئة البيانات الافتراضية لتجنب الأخطاء
      _poems = [];
      _poets = [];
      _categories = [];
      _bookInfo = BookInfo(
        title: "المنهل الراوي",
        subtitle: "ديوان شعر",
        description: "مجموعة من القصائد العربية",
        publishYear: 2023,
        coverImageUrl: null,
      );
      _favoritePoems = [];
      _filteredPoems = [];
    }

    notifyListeners();
  }

  Future<void> searchPoems(String query) async {
    // إذا كان النص فارغًا، نعيد تعيين التصفيات
    if (query.isEmpty) {
      clearFilters();
      return;
    }

    _status = ManhalRawiStatus.loading;
    _searchQuery = query;
    notifyListeners();

    try {
      final results = await _searchPoems(query);
      _filteredPoems = results;
      _status = ManhalRawiStatus.loaded;
    } catch (e, stackTrace) {
      _status = ManhalRawiStatus.error;
      _errorMessage = e.toString();

      // تسجيل الخطأ
      _errorLogger.logError(
        'ManhalRawiProvider',
        'فشل في البحث عن القصائد',
        e,
        stackTrace,
      );
    }

    notifyListeners();
  }

  Future<void> filterByCategory(int? categoryId) async {
    // إذا كان التصنيف فارغًا أو هو نفس التصنيف المحدد حاليًا، نعيد تعيين التصفيات
    if (categoryId == null || categoryId == _selectedCategoryId) {
      clearFilters();
      return;
    }

    _status = ManhalRawiStatus.loading;
    _selectedCategoryId = categoryId;
    _selectedPoetId = null;
    _searchQuery = '';
    _selectedLetter = null;
    notifyListeners();

    try {
      final results = await _filterPoems.byCategory(categoryId);
      _filteredPoems = results;
      _status = ManhalRawiStatus.loaded;
    } catch (e, stackTrace) {
      _status = ManhalRawiStatus.error;
      _errorMessage = e.toString();

      // تسجيل الخطأ
      _errorLogger.logError(
        'ManhalRawiProvider',
        'فشل في تصفية القصائد حسب التصنيف',
        e,
        stackTrace,
      );
    }

    notifyListeners();
  }

  Future<void> filterByPoet(int? poetId) async {
    // إذا كان الشاعر فارغًا، نعيد تعيين التصفيات
    if (poetId == null) {
      clearFilters();
      return;
    }

    _status = ManhalRawiStatus.loading;
    _selectedPoetId = poetId;
    _selectedCategoryId = null;
    _searchQuery = '';
    _selectedLetter = null;
    notifyListeners();

    try {
      final results = await _filterPoems.byPoet(poetId);
      _filteredPoems = results;
      _status = ManhalRawiStatus.loaded;
    } catch (e, stackTrace) {
      _status = ManhalRawiStatus.error;
      _errorMessage = e.toString();

      // تسجيل الخطأ
      _errorLogger.logError(
        'ManhalRawiProvider',
        'فشل في تصفية القصائد حسب الشاعر',
        e,
        stackTrace,
      );
    }

    notifyListeners();
  }

  /// إعادة تعيين جميع التصفيات
  ///
  /// يقوم بإعادة تعيين جميع التصفيات وإظهار جميع القصائد
  /// ويقوم بإشعار المستمعين بالتغيير
  void clearFilters() {
    // إعادة تعيين جميع المتغيرات المتعلقة بالبحث والتصفية
    _filteredPoems = [];
    _selectedCategoryId = null;
    _selectedPoetId = null;
    _searchQuery = '';
    _selectedLetter = null;

    // إعادة تعيين حالة التطبيق إلى "loaded" إذا كانت في حالة "loading" أو "error"
    if (_status == ManhalRawiStatus.loading ||
        _status == ManhalRawiStatus.error) {
      _status = ManhalRawiStatus.loaded;
    }

    // تأكد من أن التغييرات تنعكس على واجهة المستخدم
    // استخدام SchedulerBinding لضمان تنفيذ notifyListeners في الإطار التالي
    SchedulerBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });

    // استدعاء notifyListeners مرة أخرى للتأكد من تحديث واجهة المستخدم
    notifyListeners();
  }

  /// تصفية القصائد حسب الحرف الأول
  ///
  /// [letter] الحرف الذي سيتم تصفية القصائد حسبه
  ///
  /// يقوم بتصفية القصائد التي تبدأ بالحرف المحدد ويعيد تعيين التصفيات الأخرى
  /// ويقوم بإشعار المستمعين بالتغيير
  void filterByFirstLetter(String letter) {
    // إذا كان الحرف هو نفس الحرف المحدد حاليًا، نعيد تعيين التصفيات
    if (_selectedLetter == letter) {
      clearFilters();
      return;
    }

    _selectedLetter = letter;
    _selectedCategoryId = null;
    _selectedPoetId = null;
    _searchQuery = '';

    // تصفية القصائد التي تبدأ بالحرف المحدد
    _filteredPoems =
        _poems.where((poem) => poem.firstLetter == letter).toList();

    notifyListeners();
  }

  // دوال المفضلات

  /// تحميل القصائد المفضلة
  Future<void> loadFavorites() async {
    _isFavoritesLoading = true;
    notifyListeners();

    try {
      _favoritePoems = await _manageFavorites.getFavorites();
    } catch (e, stackTrace) {
      // في حالة حدوث خطأ، نعيد قائمة فارغة
      _favoritePoems = [];

      // تسجيل الخطأ
      _errorLogger.logError(
        'ManhalRawiProvider',
        'فشل في تحميل القصائد المفضلة',
        e,
        stackTrace,
      );
    }

    _isFavoritesLoading = false;
    notifyListeners();
  }

  /// تحديث حالة المفضلة لجميع القصائد
  Future<void> updatePoemsWithFavoriteStatus() async {
    try {
      _poems = await _manageFavorites.getPoemsWithFavoriteStatus();

      // تحديث القصائد المصفاة أيضاً إذا كانت موجودة
      if (_filteredPoems.isNotEmpty) {
        final List<int> filteredIds = _filteredPoems.map((p) => p.id).toList();
        _filteredPoems =
            _poems.where((p) => filteredIds.contains(p.id)).toList();
      }

      notifyListeners();
    } catch (e, stackTrace) {
      // تسجيل الخطأ
      _errorLogger.logError(
        'ManhalRawiProvider',
        'فشل في تحديث حالة المفضلة للقصائد',
        e,
        stackTrace,
      );
    }
  }

  /// تبديل حالة المفضلة للقصيدة
  Future<void> toggleFavorite(int poemId) async {
    try {
      // تبديل حالة المفضلة في قاعدة البيانات
      final bool newStatus = await _manageFavorites.toggleFavorite(poemId);

      // تحديث القصيدة في قائمة القصائد
      _poems = _poems.map((poem) {
        if (poem.id == poemId) {
          return poem.copyWith(isFavorite: newStatus);
        }
        return poem;
      }).toList();

      // تحديث القصيدة في قائمة القصائد المصفاة
      if (_filteredPoems.isNotEmpty) {
        _filteredPoems = _filteredPoems.map((poem) {
          if (poem.id == poemId) {
            return poem.copyWith(isFavorite: newStatus);
          }
          return poem;
        }).toList();
      }

      // تحديث قائمة المفضلات
      if (newStatus) {
        // إضافة القصيدة إلى المفضلات إذا لم تكن موجودة
        final poem = _poems.firstWhere((p) => p.id == poemId);
        if (!_favoritePoems.any((p) => p.id == poemId)) {
          _favoritePoems.add(poem.copyWith(isFavorite: true));
        }
      } else {
        // إزالة القصيدة من المفضلات
        _favoritePoems.removeWhere((p) => p.id == poemId);
      }

      notifyListeners();
    } catch (e, stackTrace) {
      // تسجيل الخطأ
      _errorLogger.logError(
        'ManhalRawiProvider',
        'فشل في تبديل حالة المفضلة للقصيدة',
        e,
        stackTrace,
      );
    }
  }

  /// تصفية القصائد المفضلة فقط
  void filterFavorites() {
    // إذا كانت هناك تصفية نشطة للمفضلات، نعيد تعيين التصفيات
    if (_filteredPoems.isNotEmpty &&
        _filteredPoems.every((poem) => poem.isFavorite)) {
      clearFilters();
      return;
    }

    _selectedLetter = null;
    _selectedCategoryId = null;
    _selectedPoetId = null;
    _searchQuery = '';

    // تصفية القصائد المفضلة فقط
    _filteredPoems = _poems.where((poem) => poem.isFavorite).toList();

    notifyListeners();
  }

  /// الحصول على اقتراحات البحث بناءً على النص المدخل
  List<String> getSearchSuggestions(String query) {
    if (query.isEmpty) {
      return [];
    }

    // تطبيع نص البحث لإزالة التشكيل وتوحيد أشكال الحروف
    final String normalizedQuery = ArabicTextHelper.normalizeArabicText(query);

    // قائمة لتخزين الاقتراحات
    final Set<String> suggestions = {};

    // البحث في عناوين القصائد
    for (var poem in _poems) {
      // البحث باستخدام النص الأصلي
      if (poem.title.contains(query) && !suggestions.contains(poem.title)) {
        suggestions.add(poem.title);
      }
      // البحث باستخدام النص المطبع (بدون تشكيل)
      else if (ArabicTextHelper.containsTextIgnoringDiacritics(
              poem.title, query) &&
          !suggestions.contains(poem.title)) {
        suggestions.add(poem.title);
      }
    }

    // البحث في أسماء الشعراء
    for (var poet in _poets) {
      // البحث باستخدام النص الأصلي
      if (poet.name.contains(query) && !suggestions.contains(poet.name)) {
        suggestions.add(poet.name);
      }
      // البحث باستخدام النص المطبع (بدون تشكيل)
      else if (ArabicTextHelper.containsTextIgnoringDiacritics(
              poet.name, query) &&
          !suggestions.contains(poet.name)) {
        suggestions.add(poet.name);
      }
    }

    // البحث في محتوى الأبيات (نضيف عدد محدود من الاقتراحات من الأبيات)
    int verseSuggestionsCount = 0;
    for (var poem in _poems) {
      if (verseSuggestionsCount >= 5) break; // نكتفي بـ 5 اقتراحات من الأبيات

      // البحث في الأبيات العادية
      if (poem.type == PoemType.regular) {
        for (var verse in poem.verses) {
          if (verseSuggestionsCount >= 5) break;

          // البحث في الشطر الأول
          bool foundInFirst = verse.first.contains(query);
          // البحث باستخدام النص المطبع (بدون تشكيل)
          if (!foundInFirst) {
            foundInFirst = ArabicTextHelper.containsTextIgnoringDiacritics(
                verse.first, query);
          }

          if (foundInFirst) {
            final suggestion = verse.first.length > 40
                ? '${verse.first.substring(0, 40)}...'
                : verse.first;
            if (!suggestions.contains(suggestion)) {
              suggestions.add(suggestion);
              verseSuggestionsCount++;
            }
          }

          if (verseSuggestionsCount >= 5) break;

          // البحث في الشطر الثاني
          bool foundInSecond = verse.second.contains(query);
          // البحث باستخدام النص المطبع (بدون تشكيل)
          if (!foundInSecond) {
            foundInSecond = ArabicTextHelper.containsTextIgnoringDiacritics(
                verse.second, query);
          }

          if (foundInSecond) {
            final suggestion = verse.second.length > 40
                ? '${verse.second.substring(0, 40)}...'
                : verse.second;
            if (!suggestions.contains(suggestion)) {
              suggestions.add(suggestion);
              verseSuggestionsCount++;
            }
          }
        }
      }

      // البحث في الأبيات المخمسة
      if (poem.type == PoemType.mukhammas && poem.mukhammasVerses != null) {
        for (var verse in poem.mukhammasVerses!) {
          if (verseSuggestionsCount >= 5) break;

          final parts = [
            verse.firstLine1,
            verse.firstLine2,
            verse.secondLine1,
            verse.secondLine2,
            verse.fifthLine
          ];

          for (var part in parts) {
            if (verseSuggestionsCount >= 5) break;

            // البحث باستخدام النص الأصلي
            bool foundInPart = part.contains(query);
            // البحث باستخدام النص المطبع (بدون تشكيل)
            if (!foundInPart) {
              foundInPart =
                  ArabicTextHelper.containsTextIgnoringDiacritics(part, query);
            }

            if (foundInPart) {
              final suggestion =
                  part.length > 40 ? '${part.substring(0, 40)}...' : part;
              if (!suggestions.contains(suggestion)) {
                suggestions.add(suggestion);
                verseSuggestionsCount++;
              }
            }
          }
        }
      }
    }

    // تحويل المجموعة إلى قائمة وترتيبها
    // استخدام النص الأصلي أو المطبع للترتيب حسب الأقرب للبحث
    final List<String> sortedSuggestions = suggestions.toList()
      ..sort((a, b) {
        // محاولة العثور على النص الأصلي أولاً
        int aIndexOriginal = a.indexOf(query);
        int bIndexOriginal = b.indexOf(query);

        // إذا لم يتم العثور على النص الأصلي، نبحث في النص المطبع
        if (aIndexOriginal == -1) {
          String normalizedA = ArabicTextHelper.normalizeArabicText(a);
          aIndexOriginal = normalizedA.indexOf(normalizedQuery);
        }

        if (bIndexOriginal == -1) {
          String normalizedB = ArabicTextHelper.normalizeArabicText(b);
          bIndexOriginal = normalizedB.indexOf(normalizedQuery);
        }

        return aIndexOriginal.compareTo(bIndexOriginal);
      });

    // إعادة أول 10 اقتراحات كحد أقصى
    return sortedSuggestions.take(10).toList();
  }
}
