import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/manhal_rawi_provider.dart';
import '../../../../utils/theme/manhal_colors.dart';
import '../../../../utils/theme/islamic_typography.dart';
import '../../../../utils/animations/islamic_animations.dart';

/// رسالة ترحيبية فاخرة تظهر عند تثبيت التطبيق لأول مرة
class LuxuryWelcomeMessage extends StatefulWidget {
  final VoidCallback onDismiss;

  const LuxuryWelcomeMessage({
    super.key,
    required this.onDismiss,
  });

  @override
  State<LuxuryWelcomeMessage> createState() => _LuxuryWelcomeMessageState();
}

class _LuxuryWelcomeMessageState extends State<LuxuryWelcomeMessage>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _patternAnimation;

  @override
  void initState() {
    super.initState();

    // إنشاء متحكم الرسوم المتحركة
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    // رسوم متحركة للتلاشي
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
      ),
    );

    // رسوم متحركة للتكبير
    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.2, 0.7, curve: Curves.easeOutBack),
      ),
    );

    // رسوم متحركة للزخارف
    _patternAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.5, 1.0, curve: Curves.easeOut),
      ),
    );

    // بدء الرسوم المتحركة
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = context.watch<ManhalRawiProvider>().isDarkMode;
    final size = MediaQuery.of(context).size;

    return Dialog(
      backgroundColor: Colors.transparent,
      elevation: 0,
      insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Opacity(
            opacity: _fadeAnimation.value,
            child: Transform.scale(
              scale: _scaleAnimation.value,
              child: child,
            ),
          );
        },
        child: Container(
          width: double.infinity,
          constraints: BoxConstraints(
            maxWidth: 500,
            maxHeight: size.height * 0.8,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24),
            gradient: LinearGradient(
              begin: Alignment.topRight,
              end: Alignment.bottomLeft,
              colors: isDarkMode
                  ? [
                      ManhalColors.blue900,
                      ManhalColors.blue800,
                    ]
                  : [
                      Colors.white,
                      ManhalColors.gold100.withValues(alpha: 0.3),
                    ],
            ),
            boxShadow: [
              BoxShadow(
                color:
                    (isDarkMode ? ManhalColors.gold500 : ManhalColors.primary)
                        .withValues(alpha: 0.3),
                blurRadius: 20,
                spreadRadius: 2,
              ),
            ],
            border: Border.all(
              color: isDarkMode
                  ? ManhalColors.gold500.withValues(alpha: 0.5)
                  : ManhalColors.primary.withValues(alpha: 0.3),
              width: 2,
            ),
          ),
          child: Stack(
            children: [
              // زخارف الخلفية
              Positioned.fill(
                child: AnimatedBuilder(
                  animation: _patternAnimation,
                  builder: (context, child) {
                    return Opacity(
                      opacity: _patternAnimation.value * 0.2,
                      child: CustomPaint(
                        painter: _WelcomePatternPainter(
                          isDark: isDarkMode,
                          progress: _patternAnimation.value,
                        ),
                        size: Size.infinite,
                      ),
                    );
                  },
                ),
              ),

              // المحتوى الرئيسي
              SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // الشعار والعنوان
                    _buildHeader(isDarkMode),

                    const SizedBox(height: 24),

                    // رسالة الترحيب
                    _buildWelcomeMessage(isDarkMode),

                    const SizedBox(height: 32),

                    // زر الإغلاق
                    _buildDismissButton(isDarkMode),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(bool isDarkMode) {
    return Column(
      children: [
        // الشعار
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: isDarkMode
                  ? [
                      ManhalColors.blue800,
                      ManhalColors.blue900,
                    ]
                  : [
                      Colors.white,
                      ManhalColors.gold100.withValues(alpha: 0.5),
                    ],
              radius: 0.8,
            ),
            boxShadow: [
              BoxShadow(
                color:
                    (isDarkMode ? ManhalColors.gold500 : ManhalColors.primary)
                        .withValues(alpha: 0.3),
                blurRadius: 15,
                spreadRadius: 1,
              ),
            ],
            border: Border.all(
              color: isDarkMode
                  ? ManhalColors.gold500.withValues(alpha: 0.5)
                  : ManhalColors.primary.withValues(alpha: 0.3),
              width: 2,
            ),
          ),
          child: Center(
            child: Icon(
              Icons.auto_stories,
              size: 40,
              color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
            ),
          ),
        ),

        const SizedBox(height: 16),

        // عنوان التطبيق
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: isDarkMode
                  ? ManhalColors.gold500.withValues(alpha: 0.3)
                  : ManhalColors.primary.withValues(alpha: 0.2),
              width: 1.5,
            ),
            gradient: LinearGradient(
              begin: Alignment.topRight,
              end: Alignment.bottomLeft,
              colors: isDarkMode
                  ? [
                      ManhalColors.blue800.withValues(alpha: 0.5),
                      ManhalColors.blue900.withValues(alpha: 0.3),
                    ]
                  : [
                      Colors.white.withValues(alpha: 0.7),
                      ManhalColors.gold100.withValues(alpha: 0.3),
                    ],
            ),
            boxShadow: [
              BoxShadow(
                color:
                    (isDarkMode ? ManhalColors.gold500 : ManhalColors.primary)
                        .withValues(alpha: 0.2),
                blurRadius: 8,
                spreadRadius: 1,
              ),
            ],
          ),
          child: Text(
            'المنهل الروي',
            style: IslamicTypography.luxuryBookTitle(
              isDark: isDarkMode,
              fontSize: 32,
            ),
            textAlign: TextAlign.center,
          ),
        ),

        const SizedBox(height: 8),

        // اسم المؤلف
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: isDarkMode
                ? ManhalColors.blue900.withValues(alpha: 0.3)
                : Colors.white.withValues(alpha: 0.3),
            border: Border.all(
              color: isDarkMode
                  ? ManhalColors.gold500.withValues(alpha: 0.2)
                  : ManhalColors.primary.withValues(alpha: 0.1),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.person,
                size: 14,
                color: isDarkMode ? ManhalColors.gold300 : ManhalColors.primary,
              ),
              const SizedBox(width: 6),
              Text(
                'محمد هزاع باعلوي الحضرمي',
                style: IslamicTypography.luxurySubtitle(
                  isDark: isDarkMode,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildWelcomeMessage(bool isDarkMode) {
    return IslamicAnimations.fadeSlideIn(
      isDark: isDarkMode,
      durationMs: 1000,
      delayMs: 300,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: isDarkMode
              ? ManhalColors.blue800.withValues(alpha: 0.3)
              : Colors.white.withValues(alpha: 0.5),
          border: Border.all(
            color: isDarkMode
                ? ManhalColors.gold500.withValues(alpha: 0.3)
                : ManhalColors.primary.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            // عنوان الترحيب
            Text(
              'أهلاً بك في ديوان المنهل الراوي',
              style: IslamicTypography.luxurySubtitle(
                isDark: isDarkMode,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 16),

            // نص الترحيب
            Text(
              'يسرنا أن نقدم لك هذا الديوان الشعري الفريد الذي يحتوي على مجموعة من القصائد المرتبة هجائياً من الهمزة إلى الياء.\n\n'
              'يمكنك تصفح القصائد حسب الحروف الهجائية، أو البحث عن قصيدة معينة، أو تصفح القصائد حسب نوعها.\n\n'
              'كما يمكنك إضافة القصائد المفضلة لديك للرجوع إليها لاحقاً، وتخصيص إعدادات العرض بما يناسب ذوقك.',
              style: IslamicTypography.luxuryBody(
                isDark: isDarkMode,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 16),

            // توقيع
            Text(
              'نتمنى لك تجربة ممتعة ومفيدة',
              style: IslamicTypography.luxuryCaption(
                isDark: isDarkMode,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDismissButton(bool isDarkMode) {
    return IslamicAnimations.fadeSlideIn(
      isDark: isDarkMode,
      durationMs: 800,
      delayMs: 500,
      beginOffset: const Offset(0, 20),
      child: ElevatedButton(
        onPressed: () {
          // تعيين حالة رسالة الترحيب كمشاهدة
          context.read<ManhalRawiProvider>().markWelcomeMessageAsSeen();

          // إغلاق الرسالة
          widget.onDismiss();
        },
        style: ElevatedButton.styleFrom(
          backgroundColor:
              isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 4,
          shadowColor:
              (isDarkMode ? ManhalColors.gold500 : ManhalColors.primary)
                  .withValues(alpha: 0.5),
        ),
        child: Text(
          'ابدأ الرحلة',
          style: IslamicTypography.luxuryButton(
            isDark: false,
            fontSize: 18,
          ),
        ),
      ),
    );
  }
}

/// رسام الزخارف الإسلامية لرسالة الترحيب
class _WelcomePatternPainter extends CustomPainter {
  final bool isDark;
  final double progress;

  _WelcomePatternPainter({
    required this.isDark,
    required this.progress,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final baseColor = isDark ? ManhalColors.gold500 : ManhalColors.primary;
    final paint = Paint()
      ..color = baseColor.withValues(alpha: 0.1)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    final width = size.width;
    final height = size.height;

    // رسم الزخارف في الزوايا
    _drawCornerPattern(canvas, paint, Offset(0, 0), size, progress);
    _drawCornerPattern(canvas, paint, Offset(width, 0), size, progress);
    _drawCornerPattern(canvas, paint, Offset(0, height), size, progress);
    _drawCornerPattern(canvas, paint, Offset(width, height), size, progress);

    // رسم إطار زخرفي
    final borderRect = Rect.fromLTWH(
      width * 0.05,
      height * 0.05,
      width * 0.9,
      height * 0.9,
    );

    final borderPath = Path();
    borderPath.addRRect(RRect.fromRectAndRadius(
      borderRect,
      Radius.circular(16),
    ));

    canvas.drawPath(borderPath, paint);
  }

  void _drawCornerPattern(
      Canvas canvas, Paint paint, Offset corner, Size size, double progress) {
    final patternSize = size.width * 0.15 * progress;
    final center = Offset(
      corner.dx == 0
          ? corner.dx + patternSize / 2
          : corner.dx - patternSize / 2,
      corner.dy == 0
          ? corner.dy + patternSize / 2
          : corner.dy - patternSize / 2,
    );

    // رسم الزخرفة الإسلامية الهندسية
    final path = Path();
    final sides = 8;
    final radius = patternSize / 2;

    for (int i = 0; i < sides; i++) {
      final angle = (i * 2 * 3.14159) / sides;
      final point = Offset(
        center.dx + radius * 0.8 * math.cos(angle),
        center.dy + radius * 0.8 * math.sin(angle),
      );

      if (i == 0) {
        path.moveTo(point.dx, point.dy);
      } else {
        path.lineTo(point.dx, point.dy);
      }
    }

    path.close();
    canvas.drawPath(path, paint);

    // رسم الزخرفة الداخلية
    final innerPath = Path();
    final innerRadius = radius * 0.6;

    for (int i = 0; i < sides; i++) {
      final angle = (i * 2 * 3.14159) / sides + 3.14159 / sides;
      final point = Offset(
        center.dx + innerRadius * math.cos(angle),
        center.dy + innerRadius * math.sin(angle),
      );

      if (i == 0) {
        innerPath.moveTo(point.dx, point.dy);
      } else {
        innerPath.lineTo(point.dx, point.dy);
      }
    }

    innerPath.close();
    canvas.drawPath(innerPath, paint);

    // ربط النقاط
    for (int i = 0; i < sides; i++) {
      final outerAngle = (i * 2 * 3.14159) / sides;
      final innerAngle = (i * 2 * 3.14159) / sides + 3.14159 / sides;

      final outerPoint = Offset(
        center.dx + radius * 0.8 * math.cos(outerAngle),
        center.dy + radius * 0.8 * math.sin(outerAngle),
      );

      final innerPoint = Offset(
        center.dx + innerRadius * math.cos(innerAngle),
        center.dy + innerRadius * math.sin(innerAngle),
      );

      canvas.drawLine(outerPoint, innerPoint, paint);
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}
