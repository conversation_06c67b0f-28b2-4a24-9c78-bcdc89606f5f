#!/bin/bash

# Crear directorio de fuentes si no existe
mkdir -p assets/fonts

# Descargar fuentes Amiri
curl -L "https://github.com/alif-type/amiri/releases/download/0.113/Amiri-0.113.zip" -o amiri.zip
unzip -j amiri.zip "Amiri-0.113/Amiri-Regular.ttf" "Amiri-0.113/Amiri-Bold.ttf" "Amiri-0.113/Amiri-Italic.ttf" "Amiri-0.113/Amiri-BoldItalic.ttf" -d assets/fonts/
rm amiri.zip

# Descargar fuentes Cairo
curl -L "https://fonts.google.com/download?family=Cairo" -o cairo.zip
unzip -j cairo.zip "static/Cairo-Regular.ttf" "static/Cairo-Bold.ttf" -d assets/fonts/
rm cairo.zip

# Descargar fuentes Markazi Text
curl -L "https://fonts.google.com/download?family=Markazi%20Text" -o markazi.zip
unzip -j markazi.zip "static/MarkaziText-Regular.ttf" "static/MarkaziText-Medium.ttf" "static/MarkaziText-Bold.ttf" -d assets/fonts/
rm markazi.zip

# Crear archivos de fuente de marcador de posición para Raqq y Naskh
# (Estas fuentes no están disponibles públicamente, así que usaremos Amiri como marcador de posición)
cp assets/fonts/Amiri-Regular.ttf assets/fonts/Raqq-Regular.ttf
cp assets/fonts/Amiri-Regular.ttf assets/fonts/Naskh-Regular.ttf

echo "Todas las fuentes han sido descargadas y colocadas en assets/fonts/"
