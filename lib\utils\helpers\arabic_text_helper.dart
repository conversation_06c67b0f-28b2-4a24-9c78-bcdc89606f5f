import 'package:flutter/foundation.dart';

/// مساعد للتعامل مع النصوص العربية
class ArabicTextHelper {
  /// قائمة بالحركات والتشكيل في اللغة العربية
  static const List<String> _diacritics = [
    '\u064B', // فتحتان (تنوين فتح)
    '\u064C', // ضمتان (تنوين ضم)
    '\u064D', // كسرتان (تنوين كسر)
    '\u064E', // فتحة
    '\u064F', // ضمة
    '\u0650', // كسرة
    '\u0651', // شدة
    '\u0652', // سكون
    '\u0653', // مدة
    '\u0654', // همزة فوق الألف
    '\u0655', // همزة تحت الألف
    '\u0656', // علامة الإمالة
    '\u0657', // علامة الضم المدورة
    '\u0658', // علامة الكسرة المدورة
    '\u0659', // زركشة
    '\u065A', // علامة الفتحة الصغيرة
    '\u065B', // علامة الضمة الصغيرة
    '\u065C', // علامة الكسرة الصغيرة
    '\u065D', // علامة الفتحتين الصغيرتين
    '\u065E', // علامة الضمتين الصغيرتين
    '\u065F', // علامة الكسرتين الصغيرتين
    '\u0670', // ألف خنجرية
  ];

  /// إزالة التشكيل من النص العربي
  ///
  /// يقوم بإزالة جميع علامات التشكيل من النص العربي
  /// مثال: "مُحَمَّدٌ" تصبح "محمد"
  static String removeDiacritics(String text) {
    if (text.isEmpty) return text;

    try {
      String result = text;
      for (String diacritic in _diacritics) {
        result = result.replaceAll(diacritic, '');
      }
      return result;
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('خطأ في إزالة التشكيل: $e');
        print(stackTrace);
      }
      return text; // إرجاع النص الأصلي في حالة حدوث خطأ
    }
  }

  /// تطبيع النص العربي
  ///
  /// يقوم بإزالة التشكيل وتوحيد أشكال الحروف المتشابهة
  /// مثل توحيد الألف والهمزة وغيرها
  static String normalizeArabicText(String text) {
    if (text.isEmpty) return text;

    try {
      // إزالة التشكيل أولاً
      String result = removeDiacritics(text);

      // توحيد أشكال الألف
      result = result
          .replaceAll('أ', 'ا')
          .replaceAll('إ', 'ا')
          .replaceAll('آ', 'ا')
          .replaceAll('ٱ', 'ا');

      // توحيد أشكال الياء
      result = result.replaceAll('ى', 'ي').replaceAll('ئ', 'ي');

      // توحيد أشكال الهاء
      result = result.replaceAll('ة', 'ه');

      // توحيد أشكال الواو
      result = result.replaceAll('ؤ', 'و');

      return result;
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('خطأ في تطبيع النص العربي: $e');
        print(stackTrace);
      }
      return text; // إرجاع النص الأصلي في حالة حدوث خطأ
    }
  }

  /// مقارنة نصين عربيين بغض النظر عن التشكيل
  ///
  /// تقوم بمقارنة نصين عربيين بعد إزالة التشكيل منهما
  /// وتعيد true إذا كانا متطابقين بعد إزالة التشكيل
  static bool compareTextsIgnoringDiacritics(String text1, String text2) {
    if (text1 == text2) return true;
    
    final normalized1 = normalizeArabicText(text1);
    final normalized2 = normalizeArabicText(text2);
    
    return normalized1 == normalized2;
  }

  /// التحقق من وجود نص في نص آخر بغض النظر عن التشكيل
  ///
  /// تقوم بالتحقق من وجود نص في نص آخر بعد إزالة التشكيل منهما
  /// وتعيد true إذا كان النص موجوداً بعد إزالة التشكيل
  static bool containsTextIgnoringDiacritics(String source, String query) {
    if (source.isEmpty || query.isEmpty) return false;
    if (source.contains(query)) return true;
    
    final normalizedSource = normalizeArabicText(source);
    final normalizedQuery = normalizeArabicText(query);
    
    return normalizedSource.contains(normalizedQuery);
  }
}
