import 'dart:io';
import 'package:logging/logging.dart';

final _logger = Logger('ReplaceWithOpacity');

void main() async {
  // إعداد نظام التسجيل
  Logger.root.level = Level.INFO;
  Logger.root.onRecord.listen((record) {
    stderr.writeln('${record.level.name}: ${record.time}: ${record.message}');
  });

  // قائمة بالملفات التي تحتاج إلى تعديل
  final filesToProcess = await findDartFiles('lib');

  int totalReplacements = 0;
  int totalFilesModified = 0;

  for (final file in filesToProcess) {
    final content = await File(file).readAsString();

    // البحث عن استخدامات withOpacity
    final regex = RegExp(r'\.withOpacity\(([0-9.]+)\)');
    final matches = regex.allMatches(content);

    if (matches.isNotEmpty) {
      String newContent = content;

      // استبدال كل استخدام لـ withOpacity بـ withValues(alpha: x)
      for (final match in matches) {
        final opacity = match.group(1);
        final oldText = '.withOpacity($opacity)';
        final newText = '.withValues(alpha: $opacity)';

        newContent = newContent.replaceAll(oldText, newText);
      }

      // حفظ الملف المعدل
      await File(file).writeAsString(newContent);

      totalReplacements += matches.length;
      totalFilesModified++;

      _logger.info('تم تعديل الملف: $file (${matches.length} تعديلات)');
    }
  }

  _logger.info('\nتم الانتهاء من التعديلات:');
  _logger.info('- عدد الملفات المعدلة: $totalFilesModified');
  _logger.info('- إجمالي التعديلات: $totalReplacements');
}

Future<List<String>> findDartFiles(String directory) async {
  final result = <String>[];
  final dir = Directory(directory);

  await for (final entity in dir.list(recursive: true)) {
    if (entity is File && entity.path.endsWith('.dart')) {
      result.add(entity.path);
    }
  }

  return result;
}
