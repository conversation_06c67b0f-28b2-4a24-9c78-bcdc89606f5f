import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../data/models/poet.dart';
import '../../data/models/poem.dart';
import '../providers/manhal_rawi_provider.dart';
import '../widgets/poem_card.dart';
import 'luxury_poem_details_screen.dart';
import 'settings_screen.dart';
import '../../../../utils/theme/manhal_colors.dart';
import '../../../../utils/theme/typography.dart';
import '../../../../utils/theme/decorations.dart';

class PoetDetailsScreen extends StatefulWidget {
  final Poet poet;

  const PoetDetailsScreen({
    Key? key,
    required this.poet,
  }) : super(key: key);

  @override
  State<PoetDetailsScreen> createState() => _PoetDetailsScreenState();
}

class _PoetDetailsScreenState extends State<PoetDetailsScreen> {
  List<Poem> _poetPoems = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPoetPoems();
  }

  Future<void> _loadPoetPoems() async {
    setState(() {
      _isLoading = true;
    });

    final provider = context.read<ManhalRawiProvider>();
    await provider.filterByPoet(widget.poet.id);

    setState(() {
      _poetPoems = provider.filteredPoems;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = context.watch<ManhalRawiProvider>().isDarkMode;

    return Scaffold(
      body: Container(
        decoration: ManhalDecorations.backgroundDecoration(isDark: isDarkMode),
        child: SafeArea(
          child: Column(
            children: [
              _buildAppBar(context),
              Expanded(
                child: _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : ListView(
                        children: [
                          const SizedBox(height: 16),

                          // بطاقة معلومات الشاعر
                          _buildPoetInfoCard(context),

                          const SizedBox(height: 24),

                          // قائمة قصائد الشاعر
                          _buildPoetPoemsList(context),

                          const SizedBox(height: 32),
                        ],
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar(BuildContext context) {
    final isDarkMode = context.watch<ManhalRawiProvider>().isDarkMode;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      decoration: BoxDecoration(
        color: isDarkMode
            ? ManhalColors.blue900.withValues(alpha: 0.7)
            : Colors.white.withValues(alpha: 0.9),
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withValues(alpha: 0.2)
                : Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // زر الرجوع
          IconButton(
            icon: Icon(
              Icons.arrow_back_ios,
              color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
              size: 20,
            ),
            onPressed: () {
              Navigator.pop(context);
            },
          ),

          Expanded(
            child: Text(
              widget.poet.name,
              style: ManhalTypography.headingSmall.copyWith(
                color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // زر الإعدادات
          IconButton(
            icon: Icon(
              Icons.settings,
              color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
            ),
            onPressed: () {
              Navigator.push(
                context,
                PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      const SettingsScreen(),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return FadeTransition(
                      opacity: animation,
                      child: child,
                    );
                  },
                  transitionDuration: const Duration(milliseconds: 500),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPoetInfoCard(BuildContext context) {
    final isDarkMode = context.watch<ManhalRawiProvider>().isDarkMode;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: ManhalDecorations.luxuryCardDecoration(isDark: isDarkMode),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // صورة الشاعر أو رمز بديل
            Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
                border: Border.all(
                  color:
                      isDarkMode ? ManhalColors.gold300 : ManhalColors.gold600,
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: isDarkMode
                        ? ManhalColors.gold600.withValues(alpha: 0.5)
                        : ManhalColors.gold500.withValues(alpha: 0.3),
                    blurRadius: 8,
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: Center(
                child: widget.poet.imageUrl != null
                    ? ClipOval(
                        child: Image.asset(
                          widget.poet.imageUrl!,
                          width: 96,
                          height: 96,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => Icon(
                            Icons.person,
                            size: 50,
                            color: Colors.white,
                          ),
                        ),
                      )
                    : Icon(
                        Icons.person,
                        size: 50,
                        color: Colors.white,
                      ),
              ),
            ),

            const SizedBox(height: 16),

            // الاسم الكامل
            Text(
              widget.poet.fullName,
              style: ManhalTypography.headingSmall.copyWith(
                color:
                    isDarkMode ? ManhalColors.textLight : ManhalColors.textDark,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 8),

            // العصر
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
              decoration: BoxDecoration(
                color: isDarkMode
                    ? ManhalColors.blue800.withValues(alpha: 0.7)
                    : ManhalColors.gold200.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                "مؤلف كتاب المنهل الراوي",
                style: ManhalTypography.bodyMedium.copyWith(
                  color: isDarkMode
                      ? ManhalColors.textLight
                      : ManhalColors.textDark,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

            const SizedBox(height: 16),

            // سنوات الحياة
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildYearItem(
                  context: context,
                  label: 'الولادة',
                  year: widget.poet.birthYear,
                ),
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  height: 1,
                  width: 40,
                  color: isDarkMode
                      ? ManhalColors.gold600.withValues(alpha: 0.3)
                      : ManhalColors.gold400.withValues(alpha: 0.5),
                ),
                _buildYearItem(
                  context: context,
                  label: 'الوفاة',
                  year: widget.poet.deathYear,
                ),
              ],
            ),

            const SizedBox(height: 16),

            // نبذة عن حياته
            Text(
              widget.poet.bio,
              style: ManhalTypography.bodyMedium.copyWith(
                color:
                    isDarkMode ? ManhalColors.textLight : ManhalColors.textDark,
              ),
              textAlign: TextAlign.justify,
            ),

            const SizedBox(height: 24),

            // إنجازات المؤلف
            if (widget.poet.achievements != null &&
                widget.poet.achievements!.isNotEmpty)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'إنجازات المؤلف',
                    style: ManhalTypography.headingSmall.copyWith(
                      color: isDarkMode
                          ? ManhalColors.gold500
                          : ManhalColors.primary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ...widget.poet.achievements!.map((achievement) => Padding(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Icon(
                              Icons.star,
                              color: isDarkMode
                                  ? ManhalColors.gold500
                                  : ManhalColors.primary,
                              size: 18,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                achievement,
                                style: ManhalTypography.bodyMedium.copyWith(
                                  color: isDarkMode
                                      ? ManhalColors.textLight
                                      : ManhalColors.textDark,
                                ),
                              ),
                            ),
                          ],
                        ),
                      )),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildYearItem({
    required BuildContext context,
    required String label,
    required int? year,
  }) {
    final isDarkMode = context.watch<ManhalRawiProvider>().isDarkMode;

    return Column(
      children: [
        Text(
          label,
          style: ManhalTypography.bodySmall.copyWith(
            color: isDarkMode ? ManhalColors.textMuted : ManhalColors.textMuted,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          year != null ? '$year' : '-',
          style: ManhalTypography.bodyMedium.copyWith(
            color: isDarkMode ? ManhalColors.textLight : ManhalColors.textDark,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildPoetPoemsList(BuildContext context) {
    final isDarkMode = context.watch<ManhalRawiProvider>().isDarkMode;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            'قصائد ${widget.poet.name}',
            style: ManhalTypography.headingSmall.copyWith(
              color:
                  isDarkMode ? ManhalColors.textLight : ManhalColors.textDark,
            ),
          ),
        ),
        const SizedBox(height: 8),
        if (_poetPoems.isEmpty)
          Center(
            child: Padding(
              padding: const EdgeInsets.all(32.0),
              child: Text(
                'لا توجد قصائد متاحة لهذا الشاعر',
                style: ManhalTypography.bodyMedium.copyWith(
                  color: isDarkMode
                      ? ManhalColors.textMuted
                      : ManhalColors.textMuted,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          )
        else
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: const EdgeInsets.only(bottom: 16),
            itemCount: _poetPoems.length,
            itemBuilder: (context, index) {
              final poem = _poetPoems[index];

              return PoemCard(
                poem: poem,
                poet: widget.poet,
                onTap: () {
                  Navigator.push(
                    context,
                    PageRouteBuilder(
                      pageBuilder: (context, animation, secondaryAnimation) =>
                          LuxuryPoemDetailsScreen(
                        poem: poem,
                        poet: widget.poet,
                      ),
                      transitionsBuilder:
                          (context, animation, secondaryAnimation, child) {
                        return FadeTransition(
                          opacity: animation,
                          child: child,
                        );
                      },
                      transitionDuration: const Duration(milliseconds: 500),
                    ),
                  );
                },
              );
            },
          ),
      ],
    );
  }
}
