import 'package:flutter/material.dart';
import '../../../../utils/theme/islamic_typography.dart';
import '../../../../utils/theme/manhal_colors.dart';
import '../../../../utils/helpers/error_handler.dart';
import '../../../../utils/helpers/responsive_helper.dart';

/// مكون لعرض رسائل الخطأ للمستخدم
class ErrorDisplay extends StatelessWidget {
  final String message;
  final VoidCallback? onRetry;
  final String? retryText;
  final IconData? icon;

  const ErrorDisplay({
    Key? key,
    required this.message,
    this.onRetry,
    this.retryText,
    this.icon,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // أيقونة الخطأ
            Icon(
              icon ?? Icons.error_outline_rounded,
              size: 64,
              color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
            ),
            const SizedBox(height: 16),

            // رسالة الخطأ
            Text(
              message,
              style: IslamicTypography.luxuryBody(
                isDark: isDarkMode,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),

            // زر إعادة المحاولة
            if (onRetry != null) ...[
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: Icon(Icons.refresh_rounded),
                label: Text(retryText ?? 'إعادة المحاولة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// مكون لعرض حالة التحميل أو الخطأ
class LoadingOrErrorDisplay extends StatelessWidget {
  final bool isLoading;
  final String? errorMessage;
  final VoidCallback? onRetry;
  final Widget child;

  const LoadingOrErrorDisplay({
    Key? key,
    required this.isLoading,
    this.errorMessage,
    this.onRetry,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // عرض مؤشر التحميل
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    // عرض رسالة الخطأ
    if (errorMessage != null && errorMessage!.isNotEmpty) {
      return ErrorDisplay(
        message: errorMessage!,
        onRetry: onRetry,
      );
    }

    // عرض المحتوى
    return child;
  }
}

/// دالة مساعدة لعرض رسالة خطأ
void showErrorMessage(BuildContext context, String message) {
  ErrorHandler().showErrorSnackBar(context, message);
}

/// دالة مساعدة لعرض حوار خطأ
Future<void> showErrorDialog(
  BuildContext context,
  String title,
  String message, {
  VoidCallback? onPressed,
}) async {
  await ErrorHandler().showErrorDialog(
    context,
    title,
    message,
    onPressed: onPressed,
  );
}

/// دالة مساعدة لعرض حوار إعادة المحاولة
Future<void> showRetryDialog(
  BuildContext context,
  String title,
  String message,
  VoidCallback onRetry,
) async {
  await ErrorHandler().showRetryDialog(
    context,
    title,
    message,
    onRetry,
  );
}
