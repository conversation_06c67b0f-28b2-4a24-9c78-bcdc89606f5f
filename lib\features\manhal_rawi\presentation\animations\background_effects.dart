import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../../../../utils/theme/manhal_colors.dart';

/// تأثيرات الخلفية المخصصة للتطبيق
class BackgroundEffects {
  /// خلفية متموجة
  static Widget wavyBackground({
    required BuildContext context,
    bool isDark = false,
    int numberOfWaves = 3,
  }) {
    final size = MediaQuery.of(context).size;

    return SizedBox(
      width: size.width,
      height: size.height,
      child: CustomPaint(
        painter: WavyBackgroundPainter(
          isDark: isDark,
          numberOfWaves: numberOfWaves,
        ),
      ),
    );
  }

  /// خلفية بنقوش إسلامية
  static Widget islamicPatternBackground({
    required BuildContext context,
    bool isDark = false,
    double opacity = 0.05,
  }) {
    final size = MediaQuery.of(context).size;

    return Container(
      width: size.width,
      height: size.height,
      decoration: BoxDecoration(
        color:
            isDark ? ManhalColors.backgroundDark : ManhalColors.backgroundLight,
        image: DecorationImage(
          image: AssetImage(
            isDark
                ? 'assets/images/background_pattern_dark.png'
                : 'assets/images/background_pattern_light.png',
          ),
          opacity: opacity,
          repeat: ImageRepeat.repeat,
        ),
      ),
    );
  }

  /// خلفية بتدرج لوني
  static Widget gradientBackground({
    required BuildContext context,
    bool isDark = false,
  }) {
    final size = MediaQuery.of(context).size;

    return Container(
      width: size.width,
      height: size.height,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
          colors: isDark
              ? [
                  ManhalColors.backgroundDark,
                  ManhalColors.blue900,
                  ManhalColors.blue800,
                ]
              : [
                  ManhalColors.backgroundLight,
                  ManhalColors.gold100,
                  Colors.white,
                ],
        ),
      ),
    );
  }

  /// خلفية بتأثير الضوء
  static Widget radialLightEffect({
    required BuildContext context,
    bool isDark = false,
    Offset? center,
  }) {
    final size = MediaQuery.of(context).size;
    final centerPoint = center ?? Offset(size.width * 0.7, size.height * 0.3);

    return Container(
      width: size.width,
      height: size.height,
      decoration: BoxDecoration(
        color:
            isDark ? ManhalColors.backgroundDark : ManhalColors.backgroundLight,
        gradient: RadialGradient(
          center: Alignment(
            (centerPoint.dx / size.width) * 2 - 1,
            (centerPoint.dy / size.height) * 2 - 1,
          ),
          radius: 0.8,
          colors: isDark
              ? [
                  ManhalColors.blue800.withValues(alpha: 0.6),
                  ManhalColors.backgroundDark,
                ]
              : [
                  ManhalColors.gold200.withValues(alpha: 0.5),
                  ManhalColors.backgroundLight,
                ],
          stops: const [0.0, 0.7],
        ),
      ),
    );
  }
}

/// رسام الخلفية المتموجة
class WavyBackgroundPainter extends CustomPainter {
  final bool isDark;
  final int numberOfWaves;

  WavyBackgroundPainter({
    this.isDark = false,
    this.numberOfWaves = 3,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final baseColor =
        isDark ? ManhalColors.backgroundDark : ManhalColors.backgroundLight;
    final accentColor = isDark ? ManhalColors.blue800 : ManhalColors.gold100;

    // رسم الخلفية الأساسية
    final backgroundPaint = Paint()
      ..color = baseColor
      ..style = PaintingStyle.fill;

    canvas.drawRect(
        Rect.fromLTWH(0, 0, size.width, size.height), backgroundPaint);

    // رسم الموجات
    for (int i = 0; i < numberOfWaves; i++) {
      final wavePaint = Paint()
        ..color = accentColor.withValues(alpha: 0.1 - (i * 0.02))
        ..style = PaintingStyle.fill;

      final path = Path();

      // نقطة البداية
      path.moveTo(0, size.height);

      // عدد النقاط في المنحنى
      const pointCount = 5;

      // ارتفاع الموجة
      final waveHeight =
          size.height * 0.1 * (numberOfWaves - i) / numberOfWaves;

      // تردد الموجة
      final frequency = 1.0 + (i * 0.5);

      // إزاحة الموجة
      final offset = i * (size.height * 0.05);

      // رسم المنحنى
      for (int j = 0; j <= pointCount; j++) {
        final x = size.width * j / pointCount;
        final normalizedX = x / size.width;
        final y = size.height -
            offset -
            waveHeight * math.sin(normalizedX * math.pi * frequency);

        if (j == 0) {
          path.lineTo(x, y);
        } else {
          final prevX = size.width * (j - 1) / pointCount;
          final controlX = (prevX + x) / 2;
          final prevY = size.height -
              offset -
              waveHeight * math.sin((j - 1) / pointCount * math.pi * frequency);

          path.quadraticBezierTo(controlX, prevY, x, y);
        }
      }

      // إغلاق المسار
      path.lineTo(size.width, size.height);
      path.lineTo(0, size.height);
      path.close();

      canvas.drawPath(path, wavePaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
