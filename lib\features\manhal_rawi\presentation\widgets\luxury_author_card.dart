import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../data/models/poet.dart';
import '../providers/manhal_rawi_provider.dart';
import '../screens/luxury_author_details_screen.dart';
import '../../../../utils/theme/manhal_colors.dart';
import '../../../../utils/theme/islamic_typography.dart';
import '../../../../utils/animations/islamic_animations.dart';
import '../../../../utils/theme/islamic_patterns.dart';

/// بطاقة عرض المؤلف بتصميم فاخر
class LuxuryAuthorCard extends StatefulWidget {
  final Poet poet;
  final bool isSelected;
  final VoidCallback? onTap;
  final bool showAnimation;

  const LuxuryAuthorCard({
    super.key,
    required this.poet,
    this.isSelected = false,
    this.onTap,
    this.showAnimation = true,
  });

  @override
  State<LuxuryAuthorCard> createState() => _LuxuryAuthorCardState();
}

class _LuxuryAuthorCardState extends State<LuxuryAuthorCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.05).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onHover(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
    });

    if (isHovered) {
      _controller.forward();
    } else {
      _controller.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<ManhalRawiProvider>();
    final isDarkMode = provider.isDarkMode;

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: child,
        );
      },
      child: GestureDetector(
        onTap: widget.onTap ??
            () {
              Navigator.push(
                context,
                PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      LuxuryAuthorDetailsScreen(poet: widget.poet),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return FadeTransition(
                      opacity: animation,
                      child: child,
                    );
                  },
                  transitionDuration: const Duration(milliseconds: 500),
                ),
              );
            },
        child: MouseRegion(
          onEnter: (_) => _onHover(true),
          onExit: (_) => _onHover(false),
          child: Container(
            width: 250,
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topRight,
                end: Alignment.bottomLeft,
                colors: widget.isSelected || _isHovered
                    ? (isDarkMode
                        ? [
                            ManhalColors.gold600.withValues(alpha: 0.3),
                            ManhalColors.blue800,
                          ]
                        : [
                            ManhalColors.primary.withValues(alpha: 0.1),
                            Colors.white,
                          ])
                    : (isDarkMode
                        ? [
                            ManhalColors.blue800,
                            ManhalColors.blue900,
                          ]
                        : [
                            Colors.white,
                            ManhalColors.gold100.withValues(alpha: 0.3),
                          ]),
              ),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: widget.isSelected || _isHovered
                    ? (isDarkMode ? ManhalColors.gold500 : ManhalColors.primary)
                    : (isDarkMode
                        ? ManhalColors.blue700
                        : ManhalColors.gold200),
                width: widget.isSelected || _isHovered ? 2 : 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: widget.isSelected || _isHovered
                      ? (isDarkMode
                          ? ManhalColors.gold500.withValues(alpha: 0.3)
                          : ManhalColors.primary.withValues(alpha: 0.2))
                      : Colors.black.withValues(alpha: 0.05),
                  blurRadius: widget.isSelected || _isHovered ? 15 : 5,
                  spreadRadius: widget.isSelected || _isHovered ? 1 : 0,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(height: 20),

                // صورة المؤلف
                widget.showAnimation
                    ? IslamicAnimations.goldenGlow(
                        child: _buildAuthorImage(isDarkMode),
                        isDark: isDarkMode,
                        durationMs: 3000,
                        maxGlowOpacity:
                            widget.isSelected || _isHovered ? 0.5 : 0.3,
                      )
                    : _buildAuthorImage(isDarkMode),

                const SizedBox(height: 16),

                // اسم المؤلف
                widget.showAnimation
                    ? IslamicAnimations.fadeSlideIn(
                        child: Text(
                          widget.poet.name,
                          style: IslamicTypography.luxuryAuthorName(
                            isDark: isDarkMode,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        isDark: isDarkMode,
                        durationMs: 800,
                      )
                    : Text(
                        widget.poet.name,
                        style: IslamicTypography.luxuryAuthorName(
                          isDark: isDarkMode,
                        ),
                        textAlign: TextAlign.center,
                      ),

                if (widget.poet.title != null) ...[
                  const SizedBox(height: 4),

                  // لقب المؤلف
                  Text(
                    widget.poet.title!,
                    style: IslamicTypography.luxurySubtitle(
                      isDark: isDarkMode,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],

                const SizedBox(height: 12),

                // العصر الأدبي
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? ManhalColors.blue900.withValues(alpha: 0.7)
                        : ManhalColors.gold100.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: isDarkMode
                          ? ManhalColors.gold600.withValues(alpha: 0.3)
                          : ManhalColors.gold300,
                      width: 1,
                    ),
                  ),
                  child: Text(
                    widget.poet.era,
                    style: IslamicTypography.luxuryCategory(
                      isDark: isDarkMode,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

                const SizedBox(height: 20),

                // نبذة مختصرة
                if (widget.isSelected || _isHovered)
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Text(
                      _getShortBio(widget.poet.bio),
                      style: IslamicTypography.luxuryBody(
                        isDark: isDarkMode,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),

                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAuthorImage(bool isDarkMode) {
    return Stack(
      alignment: Alignment.center,
      children: [
        // الزخرفة الخلفية
        CustomPaint(
          painter: IslamicPatterns.getGeometricPattern(
            isDark: isDarkMode,
            opacity: 0.2,
            complexity: 2,
          ),
          size: const Size(120, 120),
        ),

        // إطار الصورة
        Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
              width: 3,
            ),
            boxShadow: [
              BoxShadow(
                color:
                    (isDarkMode ? ManhalColors.gold500 : ManhalColors.primary)
                        .withValues(alpha: 0.3),
                blurRadius: 10,
                spreadRadius: 2,
              ),
            ],
          ),
          child: ClipOval(
            child: widget.poet.imageUrl != null
                ? Image.asset(
                    widget.poet.imageUrl!,
                    width: 120,
                    height: 120,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      width: 120,
                      height: 120,
                      color: isDarkMode
                          ? ManhalColors.blue700
                          : ManhalColors.gold100,
                      child: Icon(
                        Icons.person,
                        size: 60,
                        color: isDarkMode
                            ? ManhalColors.gold500
                            : ManhalColors.primary,
                      ),
                    ),
                  )
                : Container(
                    width: 120,
                    height: 120,
                    color: isDarkMode
                        ? ManhalColors.blue700
                        : ManhalColors.gold100,
                    child: Icon(
                      Icons.person,
                      size: 60,
                      color: isDarkMode
                          ? ManhalColors.gold500
                          : ManhalColors.primary,
                    ),
                  ),
          ),
        ),
      ],
    );
  }

  String _getShortBio(String bio) {
    if (bio.length <= 100) {
      return bio;
    }
    return '${bio.substring(0, 100)}...';
  }
}
