import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../data/models/poem.dart';
import '../../data/models/poet.dart';
import '../providers/manhal_rawi_provider.dart';
import '../widgets/components/animated_verse.dart';
import 'poet_details_screen.dart';
import '../../../../utils/theme/manhal_colors.dart';
import '../../../../utils/theme/typography.dart';
import '../../../../utils/theme/decorations.dart';

class PoemDetailsScreen extends StatefulWidget {
  final Poem poem;
  final Poet poet;

  const PoemDetailsScreen({
    Key? key,
    required this.poem,
    required this.poet,
  }) : super(key: key);

  @override
  State<PoemDetailsScreen> createState() => _PoemDetailsScreenState();
}

class _PoemDetailsScreenState extends State<PoemDetailsScreen> {
  int _activeVerseIndex = 0;
  final PageController _pageController = PageController();

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _copyVerseToClipboard(int index) {
    final verse = widget.poem.verses[index];
    final text = '${verse.first}\n${verse.second}';

    Clipboard.setData(ClipboardData(text: text)).then((_) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'تم نسخ البيت إلى الحافظة',
            style: ManhalTypography.bodySmall.copyWith(
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          margin: const EdgeInsets.all(16),
        ),
      );
    });
  }

  // تم إزالة دالة مشاركة القصيدة

  @override
  Widget build(BuildContext context) {
    final isDarkMode = context.watch<ManhalRawiProvider>().isDarkMode;

    return Scaffold(
      body: Container(
        decoration: ManhalDecorations.backgroundDecoration(isDark: isDarkMode),
        child: SafeArea(
          child: Column(
            children: [
              _buildAppBar(context),
              Expanded(
                child: ListView(
                  children: [
                    const SizedBox(height: 16),

                    // بطاقة معلومات القصيدة
                    _buildPoemInfoCard(context),

                    const SizedBox(height: 24),

                    // عارض الأبيات
                    _buildVersesViewer(context),

                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      // No floating action button for now
    );
  }

  Widget _buildAppBar(BuildContext context) {
    final isDarkMode = context.watch<ManhalRawiProvider>().isDarkMode;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      decoration: BoxDecoration(
        color: isDarkMode
            ? ManhalColors.blue900.withValues(alpha: 0.7)
            : Colors.white.withValues(alpha: 0.9),
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withValues(alpha: 0.2)
                : Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // زر الرجوع
          IconButton(
            icon: Icon(
              Icons.arrow_back_ios,
              color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
              size: 20,
            ),
            onPressed: () {
              Navigator.pop(context);
            },
          ),

          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // عنوان القصيدة
                Text(
                  widget.poem.title,
                  style: ManhalTypography.headingSmall.copyWith(
                    color: isDarkMode
                        ? ManhalColors.gold500
                        : ManhalColors.primary,
                  ),
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                ),

                // اسم الشاعر
                GestureDetector(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            PoetDetailsScreen(poet: widget.poet),
                      ),
                    );
                  },
                  child: Text(
                    widget.poet.name,
                    style: ManhalTypography.poetName.copyWith(
                      color: isDarkMode
                          ? ManhalColors.textLight
                          : ManhalColors.textDark,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),

          // زر تبديل الوضع المظلم
          IconButton(
            icon: Icon(
              isDarkMode ? Icons.light_mode : Icons.dark_mode,
              color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
            ),
            onPressed: () {
              context.read<ManhalRawiProvider>().toggleDarkMode();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPoemInfoCard(BuildContext context) {
    final isDarkMode = context.watch<ManhalRawiProvider>().isDarkMode;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: ManhalDecorations.luxuryCardDecoration(isDark: isDarkMode),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // وصف القصيدة
            Text(
              widget.poem.description,
              style: ManhalTypography.bodyMedium.copyWith(
                color:
                    isDarkMode ? ManhalColors.textLight : ManhalColors.textDark,
              ),
              textAlign: TextAlign.justify,
            ),

            const SizedBox(height: 16),

            // معلومات إضافية
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                // البحر الشعري
                _buildInfoItem(
                  context: context,
                  icon: Icons.music_note,
                  label: 'البحر',
                  value: widget.poem.meter,
                ),

                // عدد الأبيات
                _buildInfoItem(
                  context: context,
                  icon: Icons.format_list_numbered,
                  label: 'عدد الأبيات',
                  value: '${widget.poem.verses.length}',
                ),

                // العصر الأدبي
                _buildInfoItem(
                  context: context,
                  icon: Icons.history_edu,
                  label: 'العصر',
                  value: widget.poet.era,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem({
    required BuildContext context,
    required IconData icon,
    required String label,
    required String value,
  }) {
    final isDarkMode = context.watch<ManhalRawiProvider>().isDarkMode;

    return Column(
      children: [
        Icon(
          icon,
          color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
          size: 24,
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: ManhalTypography.bodySmall.copyWith(
            color: isDarkMode ? ManhalColors.textMuted : ManhalColors.textMuted,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: ManhalTypography.bodyMedium.copyWith(
            color: isDarkMode ? ManhalColors.textLight : ManhalColors.textDark,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildVersesViewer(BuildContext context) {
    return Column(
      children: [
        // عنوان القسم
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'أبيات القصيدة',
                style: ManhalTypography.headingSmall.copyWith(
                  color: context.watch<ManhalRawiProvider>().isDarkMode
                      ? ManhalColors.textLight
                      : ManhalColors.textDark,
                ),
              ),

              // أزرار التنقل
              Row(
                children: [
                  IconButton(
                    icon: Icon(
                      Icons.arrow_back_ios,
                      color: context.watch<ManhalRawiProvider>().isDarkMode
                          ? ManhalColors.gold500
                          : ManhalColors.primary,
                      size: 18,
                    ),
                    onPressed: _activeVerseIndex > 0
                        ? () {
                            setState(() {
                              _activeVerseIndex--;
                            });
                            _pageController.previousPage(
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeInOut,
                            );
                          }
                        : null,
                  ),
                  Text(
                    '${_activeVerseIndex + 1}/${widget.poem.verses.length}',
                    style: ManhalTypography.bodyMedium.copyWith(
                      color: context.watch<ManhalRawiProvider>().isDarkMode
                          ? ManhalColors.textLight
                          : ManhalColors.textDark,
                    ),
                  ),
                  IconButton(
                    icon: Icon(
                      Icons.arrow_forward_ios,
                      color: context.watch<ManhalRawiProvider>().isDarkMode
                          ? ManhalColors.gold500
                          : ManhalColors.primary,
                      size: 18,
                    ),
                    onPressed: _activeVerseIndex < widget.poem.verses.length - 1
                        ? () {
                            setState(() {
                              _activeVerseIndex++;
                            });
                            _pageController.nextPage(
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeInOut,
                            );
                          }
                        : null,
                  ),
                ],
              ),
            ],
          ),
        ),

        const SizedBox(height: 8),

        // عارض الأبيات
        SizedBox(
          height: 180,
          child: PageView.builder(
            controller: _pageController,
            itemCount: widget.poem.verses.length,
            onPageChanged: (index) {
              setState(() {
                _activeVerseIndex = index;
              });
            },
            itemBuilder: (context, index) {
              return AnimatedVerse(
                verse: widget.poem.verses[index],
                index: index,
                isActive: index == _activeVerseIndex,
                onTap: () => _copyVerseToClipboard(index),
              );
            },
          ),
        ),

        const SizedBox(height: 8),

        // مؤشر الصفحات
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(
            widget.poem.verses.length > 10 ? 10 : widget.poem.verses.length,
            (index) {
              // إذا كان عدد الأبيات أكثر من 10، نعرض فقط المؤشرات القريبة من البيت الحالي
              if (widget.poem.verses.length > 10) {
                final start = _activeVerseIndex - 4;
                final end = _activeVerseIndex + 5;

                if (start > 0 && index == 0) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4),
                    child: Text(
                      '...',
                      style: TextStyle(
                        color: context.watch<ManhalRawiProvider>().isDarkMode
                            ? ManhalColors.textMuted
                            : ManhalColors.textMuted,
                      ),
                    ),
                  );
                }

                if (end < widget.poem.verses.length - 1 && index == 9) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4),
                    child: Text(
                      '...',
                      style: TextStyle(
                        color: context.watch<ManhalRawiProvider>().isDarkMode
                            ? ManhalColors.textMuted
                            : ManhalColors.textMuted,
                      ),
                    ),
                  );
                }

                final actualIndex = start + index;
                if (actualIndex < 0 ||
                    actualIndex >= widget.poem.verses.length) {
                  return const SizedBox.shrink();
                }

                return _buildPageIndicator(actualIndex == _activeVerseIndex);
              }

              return _buildPageIndicator(index == _activeVerseIndex);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildPageIndicator(bool isActive) {
    final isDarkMode = context.watch<ManhalRawiProvider>().isDarkMode;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      height: 8,
      width: isActive ? 24 : 8,
      decoration: BoxDecoration(
        color: isActive
            ? (isDarkMode ? ManhalColors.gold500 : ManhalColors.primary)
            : (isDarkMode ? ManhalColors.blue700 : ManhalColors.gold200),
        borderRadius: BorderRadius.circular(4),
      ),
      curve: Curves.easeInOut,
    );
  }
}
