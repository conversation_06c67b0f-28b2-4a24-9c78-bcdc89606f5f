import 'package:flutter_test/flutter_test.dart';
import 'package:manhal_rawi/features/manhal_rawi/data/models/poem.dart';
import 'package:manhal_rawi/features/manhal_rawi/data/models/verse.dart';

void main() {
  group('اختبارات التصنيفات المتعددة', () {
    test('إنشاء قصيدة بتصنيف واحد', () {
      final poem = Poem(
        id: 1,
        title: 'قصيدة اختبار',
        poetId: 1,
        categoryId: 2,
        categoryIds: [2],
        verses: [
          Verse(first: 'البيت الأول', second: 'الشطر الثاني'),
        ],
        description: 'وصف القصيدة',
        meter: 'الطويل',
        firstLetter: 'ق',
      );

      expect(poem.categoryId, equals(2));
      expect(poem.categoryIds, equals([2]));
      expect(poem.categoryIds.length, equals(1));
    });

    test('إنشاء قصيدة بتصنيفات متعددة', () {
      final poem = Poem(
        id: 2,
        title: 'قصيدة متعددة التصنيفات',
        poetId: 1,
        categoryId: 2,
        categoryIds: [2, 4, 5],
        verses: [
          Verse(first: 'البيت الأول', second: 'الشطر الثاني'),
        ],
        description: 'قصيدة تجمع بين المدح والحكمة والمخمس',
        meter: 'الطويل',
        firstLetter: 'ق',
      );

      expect(poem.categoryId, equals(2));
      expect(poem.categoryIds, equals([2, 4, 5]));
      expect(poem.categoryIds.length, equals(3));
      expect(poem.categoryIds.contains(2), isTrue);
      expect(poem.categoryIds.contains(4), isTrue);
      expect(poem.categoryIds.contains(5), isTrue);
    });

    test('تحويل قصيدة إلى JSON مع التصنيفات المتعددة', () {
      final poem = Poem(
        id: 3,
        title: 'قصيدة JSON',
        poetId: 1,
        categoryId: 2,
        categoryIds: [2, 4],
        verses: [
          Verse(first: 'البيت الأول', second: 'الشطر الثاني'),
        ],
        description: 'وصف القصيدة',
        meter: 'الطويل',
        firstLetter: 'ق',
      );

      final json = poem.toJson();

      expect(json['id'], equals(3));
      expect(json['categoryId'], equals(2));
      expect(json['categoryIds'], equals([2, 4]));
      expect(json['categoryIds'] is List, isTrue);
    });

    test('إنشاء قصيدة من JSON مع التصنيفات المتعددة', () {
      final json = {
        'id': 4,
        'title': 'قصيدة من JSON',
        'poetId': 1,
        'categoryId': 2,
        'categoryIds': [2, 4, 5],
        'verses': [
          {'first': 'البيت الأول', 'second': 'الشطر الثاني'}
        ],
        'description': 'وصف القصيدة',
        'meter': 'الطويل',
        'firstLetter': 'ق',
        'type': 'regular',
      };

      final poem = Poem.fromJson(json);

      expect(poem.id, equals(4));
      expect(poem.categoryId, equals(2));
      expect(poem.categoryIds, equals([2, 4, 5]));
      expect(poem.categoryIds.length, equals(3));
    });

    test('إنشاء قصيدة من JSON بدون categoryIds (التوافق مع النظام القديم)', () {
      final json = {
        'id': 5,
        'title': 'قصيدة قديمة',
        'poetId': 1,
        'categoryId': 3,
        'verses': [
          {'first': 'البيت الأول', 'second': 'الشطر الثاني'}
        ],
        'description': 'وصف القصيدة',
        'meter': 'الطويل',
        'firstLetter': 'ق',
        'type': 'regular',
      };

      final poem = Poem.fromJson(json);

      expect(poem.id, equals(5));
      expect(poem.categoryId, equals(3));
      expect(poem.categoryIds, equals([3])); // يجب أن يتم إنشاؤها تلقائياً
      expect(poem.categoryIds.length, equals(1));
    });

    test('copyWith مع تحديث التصنيفات المتعددة', () {
      final originalPoem = Poem(
        id: 6,
        title: 'قصيدة أصلية',
        poetId: 1,
        categoryId: 2,
        categoryIds: [2],
        verses: [
          Verse(first: 'البيت الأول', second: 'الشطر الثاني'),
        ],
        description: 'وصف القصيدة',
        meter: 'الطويل',
        firstLetter: 'ق',
      );

      final updatedPoem = originalPoem.copyWith(
        categoryIds: [2, 4, 5],
        isFavorite: true,
      );

      expect(updatedPoem.id, equals(6));
      expect(updatedPoem.categoryId, equals(2)); // لم يتغير
      expect(updatedPoem.categoryIds, equals([2, 4, 5])); // تم التحديث
      expect(updatedPoem.isFavorite, isTrue); // تم التحديث
      expect(updatedPoem.title, equals('قصيدة أصلية')); // لم يتغير
    });

    test('التحقق من أن القصيدة تنتمي لتصنيف معين', () {
      final poem = Poem(
        id: 7,
        title: 'قصيدة للاختبار',
        poetId: 1,
        categoryId: 2,
        categoryIds: [2, 4, 5],
        verses: [
          Verse(first: 'البيت الأول', second: 'الشطر الثاني'),
        ],
        description: 'وصف القصيدة',
        meter: 'الطويل',
        firstLetter: 'ق',
      );

      // التحقق من انتماء القصيدة للتصنيفات
      expect(poem.categoryIds.contains(2), isTrue); // مدح
      expect(poem.categoryIds.contains(4), isTrue); // حكمة
      expect(poem.categoryIds.contains(5), isTrue); // مخمس
      expect(poem.categoryIds.contains(1), isFalse); // متنوعة
      expect(poem.categoryIds.contains(3), isFalse); // وطنية
    });

    test('التحقق من القيم الافتراضية للتصنيفات', () {
      final poem = Poem(
        id: 8,
        title: 'قصيدة بدون تصنيفات محددة',
        poetId: 1,
        categoryId: 1,
        // لا نحدد categoryIds
        verses: [
          Verse(first: 'البيت الأول', second: 'الشطر الثاني'),
        ],
        description: 'وصف القصيدة',
        meter: 'الطويل',
        firstLetter: 'ق',
      );

      expect(poem.categoryIds, equals([])); // القيمة الافتراضية
      expect(poem.categoryIds.isEmpty, isTrue);
    });
  });
}
