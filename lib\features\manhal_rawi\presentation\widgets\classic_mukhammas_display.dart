import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:math' as math;

import '../../data/models/mukhammas_verse.dart';
import '../providers/manhal_rawi_provider.dart';
import '../../../../utils/theme/manhal_colors.dart';
import '../../../../utils/theme/islamic_typography.dart';
//import '../../../../utils/theme/islamic_patterns.dart';

class ClassicMukhammasDisplay extends StatefulWidget {
  final List<MukhammasVerse> verses;
  final int initialPage;
  final Function(int) onPageChanged;
  final bool showPageIndicator;
  final bool isFullScreen;
  final VoidCallback? onToggleFullScreen;
  final Function(bool)? onInteractionStateChanged;

  const ClassicMukhammasDisplay({
    super.key,
    required this.verses,
    this.initialPage = 0,
    required this.onPageChanged,
    this.showPageIndicator = true,
    this.isFullScreen = false,
    this.onToggleFullScreen,
    this.onInteractionStateChanged,
  });

  @override
  State<ClassicMukhammasDisplay> createState() =>
      _ClassicMukhammasDisplayState();
}

class _ClassicMukhammasDisplayState extends State<ClassicMukhammasDisplay>
    with SingleTickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _animationController;
  late Animation<double> _pageAnimation;
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _currentPage = widget.initialPage;
    _pageController = PageController(initialPage: widget.initialPage);

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    _pageAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    _animationController.forward();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // تحديث عند تغيير نوع الخط أو حجمه في المزود
    // استخدام Provider.of لإعادة بناء الواجهة عند تغيير المزود
    Provider.of<ManhalRawiProvider>(context);

    // إعادة بناء الواجهة عند تغيير نوع الخط أو حجمه
    setState(() {});
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page;
    });

    widget.onPageChanged(page);

    _animationController.reset();
    _animationController.forward();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = context.watch<ManhalRawiProvider>().isDarkMode;
    final verseDisplaySettings =
        context.watch<ManhalRawiProvider>().verseDisplaySettings;
    final screenSize = MediaQuery.of(context).size;
    final isLandscape = screenSize.width > screenSize.height;

    // تحديد حجم الشاشة لتكييف العناصر
    final isSmallScreen = screenSize.width < 360;
    final isLargeScreen = screenSize.width > 600;

    // تحديد عدد الأبيات في الصفحة الواحدة
    int versesPerPage = verseDisplaySettings.versesPerPage;

    // تعديل عدد الأبيات بناءً على حجم الشاشة ووضع ملء الشاشة
    if (widget.isFullScreen) {
      // زيادة عدد الأبيات في وضع ملء الشاشة
      if (isLargeScreen) {
        // للشاشات الكبيرة، زيادة عدد الأبيات بنسبة 30%
        versesPerPage = (versesPerPage * 1.3).round();
      } else if (isLandscape) {
        // للشاشات في وضع أفقي، زيادة عدد الأبيات بنسبة 20%
        versesPerPage = (versesPerPage * 1.2).round();
      } else if (!isSmallScreen) {
        // للشاشات المتوسطة، زيادة عدد الأبيات بنسبة 15%
        versesPerPage = (versesPerPage * 1.15).round();
      }
    } else {
      // تعديل عدد الأبيات في الوضع العادي
      if (isSmallScreen) {
        // تقليل عدد الأبيات للشاشات الصغيرة
        versesPerPage = versesPerPage > 2 ? versesPerPage - 1 : versesPerPage;
      }
    }

    // التأكد من أن عدد الأبيات لا يقل عن 1
    versesPerPage = versesPerPage.clamp(1, 50);

    // حساب عدد الصفحات
    final int pageCount = (widget.verses.length / versesPerPage).ceil();

    return Container(
      decoration: BoxDecoration(
        color: isDarkMode
            ? ManhalColors.blue900
                .withValues(alpha: widget.isFullScreen ? 1.0 : 0.7)
            : Colors.white.withValues(alpha: widget.isFullScreen ? 1.0 : 0.9),
        borderRadius: BorderRadius.circular(widget.isFullScreen
            ? 0
            : (isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0))),
        border: Border.all(
          color: isDarkMode
              ? ManhalColors.gold500.withValues(alpha: 0.3)
              : ManhalColors.gold300,
          width: isSmallScreen ? 0.5 : (isLargeScreen ? 1.5 : 1.0),
        ),
        boxShadow: widget.isFullScreen
            ? []
            : [
                BoxShadow(
                  color: isDarkMode
                      ? Colors.black.withValues(alpha: 0.3)
                      : Colors.black.withValues(alpha: 0.1),
                  blurRadius:
                      isSmallScreen ? 8.0 : (isLargeScreen ? 12.0 : 10.0),
                  spreadRadius:
                      isSmallScreen ? 0.5 : (isLargeScreen ? 1.5 : 1.0),
                ),
              ],
        image: DecorationImage(
          image: AssetImage(
            isDarkMode
                ? 'assets/images/paper_texture_dark.png'
                : 'assets/images/paper_texture_light.png',
          ),
          opacity: 0.1,
          fit: BoxFit.cover,
        ),
      ),
      child: Column(
        children: [
          // شريط العنوان
          if (widget.isFullScreen) _buildFullScreenHeader(isDarkMode),

          // عرض الأبيات
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: _onPageChanged,
              itemCount: pageCount,
              physics: const BouncingScrollPhysics(),
              itemBuilder: (context, pageIndex) {
                final int pageStartIndex = pageIndex * versesPerPage;
                final int pageEndIndex = math.min(
                    pageStartIndex + versesPerPage, widget.verses.length);
                final pageVerses =
                    widget.verses.sublist(pageStartIndex, pageEndIndex);

                return AnimatedBuilder(
                  animation: _pageAnimation,
                  builder: (context, child) {
                    final animationValue =
                        pageIndex == _currentPage ? _pageAnimation.value : 1.0;

                    return Opacity(
                      opacity: animationValue,
                      child: Transform.scale(
                        scale: 0.9 + (0.1 * animationValue),
                        child: child,
                      ),
                    );
                  },
                  child: _buildClassicMukhammasPage(
                    context,
                    pageVerses,
                    isDarkMode,
                    isLandscape,
                    verseDisplaySettings.fontSize,
                  ),
                );
              },
            ),
          ),

          // مؤشر الصفحات
          if (widget.showPageIndicator)
            _buildPageIndicator(pageCount, isDarkMode),
        ],
      ),
    );
  }

  Widget _buildFullScreenHeader(bool isDarkMode) {
    // تحديد حجم الشاشة لتكييف العناصر
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isLargeScreen = screenSize.width > 600;
    final isLandscape = screenSize.width > screenSize.height;

    // تعديل الأحجام بناءً على حجم الشاشة
    final horizontalPadding =
        isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0);
    final verticalPadding = isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0);
    final borderWidth = isSmallScreen ? 0.5 : (isLargeScreen ? 1.5 : 1.0);
    final iconSize = isSmallScreen ? 20.0 : (isLargeScreen ? 28.0 : 24.0);
    final fontSize = isSmallScreen ? 12.0 : (isLargeScreen ? 16.0 : 14.0);

    // تحديد عدد الأبيات في الصفحة الواحدة
    final verseDisplaySettings =
        context.read<ManhalRawiProvider>().verseDisplaySettings;
    int versesPerPage = verseDisplaySettings.versesPerPage;

    // تعديل عدد الأبيات بناءً على حجم الشاشة ووضع ملء الشاشة
    if (widget.isFullScreen) {
      if (isLargeScreen) {
        versesPerPage = (versesPerPage * 1.3).round();
      } else if (isLandscape) {
        versesPerPage = (versesPerPage * 1.2).round();
      } else if (!isSmallScreen) {
        versesPerPage = (versesPerPage * 1.15).round();
      }
    } else {
      if (isSmallScreen) {
        versesPerPage = versesPerPage > 2 ? versesPerPage - 1 : versesPerPage;
      }
    }

    versesPerPage = versesPerPage.clamp(1, 50);
    final int pageCount = (widget.verses.length / versesPerPage).ceil();

    return Container(
      padding: EdgeInsets.symmetric(
          horizontal: horizontalPadding, vertical: verticalPadding),
      decoration: BoxDecoration(
        color: isDarkMode
            ? ManhalColors.blue900.withValues(alpha: 0.8)
            : Colors.white.withValues(alpha: 0.8),
        border: Border(
          bottom: BorderSide(
            color: isDarkMode
                ? ManhalColors.gold500.withValues(alpha: 0.3)
                : ManhalColors.gold300,
            width: borderWidth,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        textDirection:
            TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
        children: [
          IconButton(
            icon: Icon(
              Icons.text_fields,
              color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
              size: iconSize,
            ),
            onPressed: () {
              // فتح نافذة لتغيير حجم الخط
              _showFontSizeDialog(context);
            },
            padding: EdgeInsets.all(
                isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0)),
            constraints: BoxConstraints(
              minWidth: isSmallScreen ? 32.0 : (isLargeScreen ? 48.0 : 40.0),
              minHeight: isSmallScreen ? 32.0 : (isLargeScreen ? 48.0 : 40.0),
            ),
          ),
          Text(
            'صفحة ${_currentPage + 1} من $pageCount',
            style: IslamicTypography.luxuryCaption(
              isDark: isDarkMode,
              fontSize: fontSize,
              fontFamily: context.read<ManhalRawiProvider>().fontFamily,
            ),
            textDirection:
                TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
          ),
          IconButton(
            icon: Icon(
              Icons.fullscreen_exit,
              color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
              size: iconSize,
            ),
            onPressed: widget.onToggleFullScreen,
            padding: EdgeInsets.all(
                isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0)),
            constraints: BoxConstraints(
              minWidth: isSmallScreen ? 32.0 : (isLargeScreen ? 48.0 : 40.0),
              minHeight: isSmallScreen ? 32.0 : (isLargeScreen ? 48.0 : 40.0),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClassicMukhammasPage(
    BuildContext context,
    List<MukhammasVerse> verses,
    bool isDarkMode,
    bool isLandscape,
    double fontSize,
  ) {
    // تحديد حجم الشاشة لتكييف العناصر
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isLargeScreen = screenSize.width > 600;

    // تعديل الأحجام بناءً على حجم الشاشة
    // تقليل المسافات لجعل الأبيات أكثر تقاربًا
    final margin =
        isSmallScreen ? 8.0 : (isLargeScreen ? 14.0 : 10.0); // تم تقليل المسافة
    final borderRadius = isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0);
    final borderWidth = isSmallScreen ? 0.5 : (isLargeScreen ? 1.5 : 1.0);
    final padding =
        isSmallScreen ? 8.0 : (isLargeScreen ? 14.0 : 10.0); // تم تقليل المسافة

    return Container(
      margin: EdgeInsets.all(margin),
      decoration: BoxDecoration(
        color: isDarkMode
            ? ManhalColors.blue900.withValues(alpha: 0.5)
            : Colors.white.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(borderRadius),
        border: Border.all(
          color: isDarkMode
              ? ManhalColors.gold500.withValues(alpha: 0.2)
              : ManhalColors.gold300.withValues(alpha: 0.5),
          width: borderWidth,
        ),
        image: DecorationImage(
          image: AssetImage(
            isDarkMode
                ? 'assets/images/classic_paper_dark.png'
                : 'assets/images/classic_paper_light.png',
          ),
          opacity: 0.2,
          fit: BoxFit.cover,
        ),
      ),
      child: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        padding: EdgeInsets.symmetric(
          horizontal: padding,
          vertical: padding / 2, // تقليل المسافة العمودية
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min, // تقليل الحجم العمودي للعمود
          mainAxisAlignment:
              MainAxisAlignment.start, // محاذاة العناصر من الأعلى
          crossAxisAlignment:
              CrossAxisAlignment.stretch, // امتداد العناصر أفقيًا
          children: verses
              .map((verse) => _buildClassicMukhammasVerse(
                    context,
                    verse,
                    isDarkMode,
                    isLandscape,
                    fontSize,
                    isSmallScreen,
                    isLargeScreen,
                  ))
              .toList(),
        ),
      ),
    );
  }

  Widget _buildClassicMukhammasVerse(BuildContext context, MukhammasVerse verse,
      bool isDarkMode, bool isLandscape, double fontSize,
      [bool isSmallScreen = false, bool isLargeScreen = false]) {
    // إذا لم يتم تمرير قيم لـ isSmallScreen و isLargeScreen، نقوم بحسابها
    if (isSmallScreen == false && isLargeScreen == false) {
      final screenSize = MediaQuery.of(context).size;
      isSmallScreen = screenSize.width < 360;
      isLargeScreen = screenSize.width > 600;
    }

    // تعديل الأحجام بناءً على حجم الشاشة
    // تقليل المسافة بين الأبيات لجعلها أكثر تقاربًا
    final bottomMargin =
        isSmallScreen ? 8.0 : (isLargeScreen ? 16.0 : 12.0); // تم تقليل المسافة
    final contentPadding =
        isSmallScreen ? 8.0 : (isLargeScreen ? 14.0 : 10.0); // تم تقليل المسافة
    final borderWidth = isSmallScreen ? 0.5 : (isLargeScreen ? 1.5 : 1.0);
    final dividerHeight = isSmallScreen
        ? 30.0
        : (isLargeScreen ? 45.0 : 35.0); // تم تقليل ارتفاع الفاصل
    final dividerMargin =
        isSmallScreen ? 8.0 : (isLargeScreen ? 14.0 : 10.0); // تم تقليل المسافة
    final spacingHeight =
        isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0); // تم تقليل المسافة

    // تعديل حجم الخط بناءً على حجم الشاشة
    final adjustedFontSize = isSmallScreen
        ? fontSize * 0.9
        : (isLargeScreen ? fontSize * 1.1 : fontSize);

    final textStyle = TextStyle(
      fontFamily: context.read<ManhalRawiProvider>().fontFamily,
      fontSize: adjustedFontSize,
      height: 1.5, // تم تقليل ارتفاع الخط لجعل الأبيات أكثر تقاربًا
      color: isDarkMode ? Colors.white : Colors.black87,
    );

    final decorationColor = isDarkMode
        ? ManhalColors.gold500.withValues(alpha: 0.3)
        : ManhalColors.gold300;

    return Container(
      margin: EdgeInsets.only(bottom: bottomMargin),
      child: Column(
        children: [
          // الأربعة أبيات الأولى
          Container(
            padding: EdgeInsets.all(contentPadding),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: decorationColor,
                  width: borderWidth,
                  style: BorderStyle.solid,
                ),
              ),
            ),
            child: Column(
              children: [
                // البيت الأول
                Row(
                  textDirection: TextDirection
                      .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                  children: [
                    Expanded(
                      child: Text(
                        verse.firstLine1,
                        style: textStyle,
                        textAlign: TextAlign.center,
                        textDirection: TextDirection.rtl,
                      ),
                    ),
                    Container(
                      height: dividerHeight,
                      width: borderWidth,
                      color: decorationColor,
                      margin: EdgeInsets.symmetric(horizontal: dividerMargin),
                    ),
                    Expanded(
                      child: Text(
                        verse.firstLine2,
                        style: textStyle,
                        textAlign: TextAlign.center,
                        textDirection: TextDirection.rtl,
                      ),
                    ),
                  ],
                ),

                SizedBox(height: spacingHeight),

                // البيت الثاني
                Row(
                  textDirection: TextDirection
                      .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                  children: [
                    Expanded(
                      child: Text(
                        verse.secondLine1,
                        style: textStyle,
                        textAlign: TextAlign.center,
                        textDirection: TextDirection.rtl,
                      ),
                    ),
                    Container(
                      height: dividerHeight,
                      width: borderWidth,
                      color: decorationColor,
                      margin: EdgeInsets.symmetric(horizontal: dividerMargin),
                    ),
                    Expanded(
                      child: Text(
                        verse.secondLine2,
                        style: textStyle,
                        textAlign: TextAlign.center,
                        textDirection: TextDirection.rtl,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // فاصل زخرفي فاخر قبل الشطر الخامس
          // Padding(
          //   padding: EdgeInsets.symmetric(vertical: spacingHeight),
          //   child: IslamicPatterns.getLuxuryVerseDivider(
          //     isDark: isDarkMode,
          //     height: dividerHeight * 0.8,
          //     opacity: isDarkMode ? 0.7 : 0.6,
          //     fontSize: context
          //         .read<ManhalRawiProvider>()
          //         .verseDisplaySettings
          //         .fontSize,
          //     textScale: MediaQuery.of(context).textScaler.scale(1.0),
          //   ),
          // ),

          // البيت الخامس
          Container(
            padding: EdgeInsets.only(
              top: contentPadding / 2, // تقليل المسافة العلوية
              left: contentPadding,
              right: contentPadding,
              bottom: contentPadding,
            ),
            child: Text(
              verse.fifthLine,
              style: textStyle, // تم إزالة التنسيق الغامق
              textAlign: TextAlign.center,
              textDirection: TextDirection.rtl,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPageIndicator(int pageCount, bool isDarkMode) {
    // تحديد حجم الشاشة لتكييف العناصر
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isLargeScreen = screenSize.width > 600;

    // تعديل الأحجام بناءً على حجم الشاشة
    final verticalPadding = isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0);
    final iconSize = isSmallScreen ? 16.0 : (isLargeScreen ? 24.0 : 20.0);
    final fullscreenIconSize =
        isSmallScreen ? 20.0 : (isLargeScreen ? 28.0 : 24.0);
    final fontSize = isSmallScreen ? 12.0 : (isLargeScreen ? 16.0 : 14.0);
    final buttonPadding = isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0);
    final minButtonSize = isSmallScreen ? 32.0 : (isLargeScreen ? 48.0 : 40.0);

    return Container(
      padding: EdgeInsets.symmetric(vertical: verticalPadding),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        textDirection:
            TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
        children: [
          // زر الصفحة السابقة (في اللغة العربية يكون على اليسار)
          IconButton(
            icon: Icon(
              Icons.arrow_back_ios,
              color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
              size: iconSize,
            ),
            onPressed: _currentPage > 0
                ? () {
                    _pageController.previousPage(
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                  }
                : null,
            padding: EdgeInsets.all(buttonPadding),
            constraints: BoxConstraints(
              minWidth: minButtonSize,
              minHeight: minButtonSize,
            ),
          ),

          // مؤشر الصفحة الحالية
          Container(
            padding: EdgeInsets.symmetric(
                horizontal: isSmallScreen ? 8.0 : (isLargeScreen ? 16.0 : 12.0),
                vertical: isSmallScreen ? 4.0 : (isLargeScreen ? 8.0 : 6.0)),
            decoration: BoxDecoration(
              color: isDarkMode
                  ? ManhalColors.blue800.withValues(alpha: 0.3)
                  : ManhalColors.gold100.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(
                  isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0)),
              border: Border.all(
                color: isDarkMode
                    ? ManhalColors.gold500.withValues(alpha: 0.2)
                    : ManhalColors.gold300.withValues(alpha: 0.5),
                width: isSmallScreen ? 0.5 : (isLargeScreen ? 1.5 : 1.0),
              ),
            ),
            child: Text(
              '${_currentPage + 1} / $pageCount',
              style: IslamicTypography.luxuryCaption(
                isDark: isDarkMode,
                fontSize: fontSize,
                fontFamily: context.read<ManhalRawiProvider>().fontFamily,
              ),
              textDirection:
                  TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
            ),
          ),

          // زر الصفحة التالية (في اللغة العربية يكون على اليمين)
          IconButton(
            icon: Icon(
              Icons.arrow_forward_ios,
              color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
              size: iconSize,
            ),
            onPressed: _currentPage < pageCount - 1
                ? () {
                    _pageController.nextPage(
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                  }
                : null,
            padding: EdgeInsets.all(buttonPadding),
            constraints: BoxConstraints(
              minWidth: minButtonSize,
              minHeight: minButtonSize,
            ),
          ),

          // زر ملء الشاشة (في اللغة العربية يكون على اليمين)
          if (widget.onToggleFullScreen != null)
            IconButton(
              icon: Icon(
                widget.isFullScreen ? Icons.fullscreen_exit : Icons.fullscreen,
                color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
                size: fullscreenIconSize,
              ),
              onPressed: widget.onToggleFullScreen,
              padding: EdgeInsets.all(buttonPadding),
              constraints: BoxConstraints(
                minWidth: minButtonSize,
                minHeight: minButtonSize,
              ),
            ),
        ],
      ),
    );
  }

  void _showFontSizeDialog(BuildContext context) {
    // إخطار بأن المستخدم يتفاعل مع المكون
    widget.onInteractionStateChanged?.call(true);

    final isDarkMode = context.read<ManhalRawiProvider>().isDarkMode;
    final provider = context.read<ManhalRawiProvider>();
    double currentFontSize = provider.verseDisplaySettings.fontSize;

    // تحديد حجم الشاشة لتكييف العناصر
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isLargeScreen = screenSize.width > 600;

    // تعديل الأحجام بناءً على حجم الشاشة
    final borderRadius = isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0);
    final borderWidth = isSmallScreen ? 0.5 : (isLargeScreen ? 1.5 : 1.0);
    final titleFontSize = isSmallScreen ? 16.0 : (isLargeScreen ? 20.0 : 18.0);
    final bodyFontSize = isSmallScreen ? 12.0 : (isLargeScreen ? 16.0 : 14.0);
    final buttonFontSize = isSmallScreen ? 12.0 : (isLargeScreen ? 16.0 : 14.0);
    final spacingHeight = isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0);
    final iconSize = isSmallScreen ? 20.0 : (isLargeScreen ? 28.0 : 24.0);
    final buttonPadding = isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0);
    final containerWidth = isSmallScreen ? 40.0 : (isLargeScreen ? 60.0 : 50.0);
    final buttonBorderRadius =
        isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0);

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            backgroundColor: isDarkMode ? ManhalColors.blue900 : Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadius),
              side: BorderSide(
                color: isDarkMode
                    ? ManhalColors.gold500.withValues(alpha: 0.3)
                    : ManhalColors.gold300,
                width: borderWidth,
              ),
            ),
            title: Text(
              'حجم الخط',
              style: IslamicTypography.luxuryTitle(
                isDark: isDarkMode,
                fontSize: titleFontSize,
                fontFamily: context.read<ManhalRawiProvider>().fontFamily,
              ),
              textAlign: TextAlign.center,
              textDirection:
                  TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'اختر حجم الخط المناسب',
                  style: IslamicTypography.luxuryBody(
                    isDark: isDarkMode,
                    fontSize: bodyFontSize,
                    fontFamily: context.read<ManhalRawiProvider>().fontFamily,
                  ),
                  textAlign: TextAlign.center,
                  textDirection: TextDirection
                      .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                ),
                SizedBox(height: spacingHeight),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  textDirection: TextDirection
                      .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                  children: [
                    IconButton(
                      icon: Icon(
                        Icons.remove,
                        color: isDarkMode
                            ? ManhalColors.gold500
                            : ManhalColors.primary,
                        size: iconSize,
                      ),
                      onPressed: currentFontSize > 12
                          ? () {
                              setState(() {
                                currentFontSize =
                                    math.max(12, currentFontSize - 1);
                              });
                            }
                          : null,
                      padding: EdgeInsets.all(buttonPadding),
                    ),
                    Container(
                      width: containerWidth,
                      alignment: Alignment.center,
                      padding: EdgeInsets.symmetric(
                          vertical: isSmallScreen
                              ? 4.0
                              : (isLargeScreen ? 8.0 : 6.0)),
                      decoration: BoxDecoration(
                        color: isDarkMode
                            ? ManhalColors.blue800.withValues(alpha: 0.3)
                            : ManhalColors.gold100.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(borderRadius / 2),
                        border: Border.all(
                          color: isDarkMode
                              ? ManhalColors.gold500.withValues(alpha: 0.2)
                              : ManhalColors.gold300.withValues(alpha: 0.5),
                          width: borderWidth / 2,
                        ),
                      ),
                      child: Text(
                        currentFontSize.toInt().toString(),
                        style: IslamicTypography.luxuryTitle(
                          isDark: isDarkMode,
                          fontSize: titleFontSize - 2,
                          fontFamily:
                              context.read<ManhalRawiProvider>().fontFamily,
                        ),
                        textDirection: TextDirection
                            .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                      ),
                    ),
                    IconButton(
                      icon: Icon(
                        Icons.add,
                        color: isDarkMode
                            ? ManhalColors.gold500
                            : ManhalColors.primary,
                        size: iconSize,
                      ),
                      onPressed: currentFontSize < 30
                          ? () {
                              setState(() {
                                currentFontSize =
                                    math.min(30, currentFontSize + 1);
                              });
                            }
                          : null,
                      padding: EdgeInsets.all(buttonPadding),
                    ),
                  ],
                ),
                SizedBox(height: spacingHeight),
                Text(
                  'مثال على حجم الخط',
                  style: IslamicTypography.luxuryBody(
                    isDark: isDarkMode,
                    fontSize: currentFontSize,
                    fontFamily: context.read<ManhalRawiProvider>().fontFamily,
                  ),
                  textAlign: TextAlign.center,
                  textDirection: TextDirection
                      .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                ),
              ],
            ),
            actions: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                textDirection:
                    TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                children: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      // إخطار بأن التفاعل انتهى
                      widget.onInteractionStateChanged?.call(false);
                    },
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.symmetric(
                        horizontal: isSmallScreen
                            ? 12.0
                            : (isLargeScreen ? 20.0 : 16.0),
                        vertical:
                            isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0),
                      ),
                    ),
                    child: Text(
                      'إلغاء',
                      style: TextStyle(
                        fontFamily:
                            context.read<ManhalRawiProvider>().fontFamily,
                        fontSize: buttonFontSize,
                        color: isDarkMode
                            ? ManhalColors.gold300
                            : ManhalColors.primary,
                      ),
                      textDirection: TextDirection
                          .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                    ),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      provider.updateFontSize(currentFontSize);
                      Navigator.of(context).pop();
                      // إخطار بأن التفاعل انتهى
                      widget.onInteractionStateChanged?.call(false);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: isDarkMode
                          ? ManhalColors.gold500
                          : ManhalColors.primary,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(
                        horizontal: isSmallScreen
                            ? 12.0
                            : (isLargeScreen ? 20.0 : 16.0),
                        vertical:
                            isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0),
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(buttonBorderRadius),
                      ),
                    ),
                    child: Text(
                      'تطبيق',
                      style: IslamicTypography.luxuryBody(
                        isDark: false,
                        fontSize: buttonFontSize,
                        fontFamily:
                            context.read<ManhalRawiProvider>().fontFamily,
                      ),
                      textDirection: TextDirection
                          .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                    ),
                  ),
                ],
              ),
            ],
          );
        },
      ),
    );
  }
}
