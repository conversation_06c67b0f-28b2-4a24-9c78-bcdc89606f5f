import 'package:flutter/material.dart';
import '../../../data/models/poem.dart';

/// حوار مشاركة القصيدة أو بيت محدد - معطل حالياً
///
/// تم تعطيل ميزة المشاركة مؤقتاً وسيتم تفعيلها في إصدار لاحق
class ShareDialog extends StatelessWidget {
  final Poem poem;
  final int? selectedVerseIndex;

  const ShareDialog({
    super.key,
    required this.poem,
    this.selectedVerseIndex,
  });

  @override
  Widget build(BuildContext context) {
    // هذا المكون معطل حالياً
    return const SizedBox.shrink();
  }
}
