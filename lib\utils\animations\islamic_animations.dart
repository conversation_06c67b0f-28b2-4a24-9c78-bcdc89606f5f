import 'dart:math';
import 'package:flutter/material.dart';
import '../theme/manhal_colors.dart';

/// مكتبة الرسوم المتحركة الإسلامية
class IslamicAnimations {
  /// تأثير ظهور تدريجي مع حركة
  static Widget fadeSlideIn({
    required Widget child,
    required bool isDark,
    int durationMs = 800,
    int delayMs = 0,
    Offset beginOffset = const Offset(0, 30),
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: Duration(milliseconds: durationMs),
      curve: Curves.easeOutQuart,
      builder: (context, value, child) {
        return Opacity(
          opacity: value,
          child: Transform.translate(
            offset: Offset(
              beginOffset.dx * (1 - value),
              beginOffset.dy * (1 - value),
            ),
            child: child,
          ),
        );
      },
      child: child,
    );
  }

  /// تأثير توهج ذهبي
  static Widget goldenGlow({
    required Widget child,
    required bool isDark,
    int durationMs = 2000,
    double maxGlowOpacity = 0.3,
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: Duration(milliseconds: durationMs),
      curve: Curves.easeInOut,
      builder: (context, value, child) {
        final glowColor = isDark ? ManhalColors.gold500 : ManhalColors.primary;
        final glowOpacity = (value < 0.5)
            ? value * 2 * maxGlowOpacity
            : (1 - value) * 2 * maxGlowOpacity;

        return Container(
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: glowColor.withValues(alpha: glowOpacity),
                blurRadius: 20,
                spreadRadius: 2,
              ),
            ],
          ),
          child: child,
        );
      },
      child: child,
    );
  }

  /// تأثير ظهور الكتابة العربية حرفاً بحرف
  static Widget arabicTextTyping({
    required String text,
    required TextStyle style,
    int durationMs = 2000,
    Curve curve = Curves.easeOut,
    TextAlign textAlign = TextAlign.center,
    int delayMs = 0,
  }) {
    return _ArabicTextTypingAnimation(
      text: text,
      style: style,
      durationMs: durationMs,
      curve: curve,
      textAlign: textAlign,
      delayMs: delayMs,
    );
  }

  /// تأثير تحول الصفحة بطريقة الكتاب
  static Widget pageTransition({
    required Widget child,
    required bool forward,
    int durationMs = 800,
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: Duration(milliseconds: durationMs),
      curve: Curves.easeInOutBack,
      builder: (context, value, child) {
        final rotation = forward ? (1 - value) * 0.5 : -value * 0.5;
        final scale = 0.8 + (0.2 * value);
        final opacity = value;

        return Opacity(
          opacity: opacity,
          child: Transform(
            alignment: forward ? Alignment.centerLeft : Alignment.centerRight,
            transform: Matrix4.identity()
              ..setEntry(3, 2, 0.001)
              ..rotateY(rotation)
              ..scale(scale),
            child: child,
          ),
        );
      },
      child: child,
    );
  }

  /// تأثير تلاشي الخلفية مع تغير الألوان
  static Widget backgroundFade({
    required Widget child,
    required bool isDark,
    int durationMs = 1000,
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: Duration(milliseconds: durationMs),
      curve: Curves.easeInOut,
      builder: (context, value, child) {
        final startColor = isDark ? ManhalColors.blue900 : Colors.white;
        final endColor = isDark
            ? ManhalColors.blue800
            : ManhalColors.gold100.withValues(alpha: 0.3);

        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color.lerp(startColor, endColor, value) ?? startColor,
                Color.lerp(endColor, startColor, value) ?? endColor,
              ],
            ),
          ),
          child: child,
        );
      },
      child: child,
    );
  }

  /// تأثير نبض للعناصر
  static Widget pulsatingEffect({
    required Widget child,
    required bool isDark,
    int durationMs = 1500,
    double minScale = 0.95,
    double maxScale = 1.05,
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: Duration(milliseconds: durationMs),
      curve: Curves.easeInOut,
      builder: (context, value, child) {
        // استخدام دالة الجيب لإنشاء تأثير النبض
        final sinValue = sin(value * pi * 2) * 0.5 + 0.5;
        final scale = minScale + sinValue * (maxScale - minScale);

        return Transform.scale(
          scale: scale,
          child: child,
        );
      },
      child: child,
    );
  }

  /// تأثير تلاشي الخلفية مع الزخارف الإسلامية
  static Widget islamicBackgroundTransition({
    required Widget child,
    required bool isDark,
    int durationMs = 1200,
  }) {
    return _IslamicBackgroundTransition(
      isDark: isDark,
      durationMs: durationMs,
      child: child,
    );
  }
}

/// مكون تحريك النص العربي حرفاً بحرف
class _ArabicTextTypingAnimation extends StatefulWidget {
  final String text;
  final TextStyle style;
  final int durationMs;
  final Curve curve;
  final TextAlign textAlign;
  final int delayMs;

  const _ArabicTextTypingAnimation({
    required this.text,
    required this.style,
    required this.durationMs,
    required this.curve,
    required this.textAlign,
    required this.delayMs,
  });

  @override
  State<_ArabicTextTypingAnimation> createState() =>
      _ArabicTextTypingAnimationState();
}

class _ArabicTextTypingAnimationState extends State<_ArabicTextTypingAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: widget.durationMs),
    );

    _animation = CurvedAnimation(
      parent: _controller,
      curve: widget.curve,
    );

    if (widget.delayMs > 0) {
      Future.delayed(Duration(milliseconds: widget.delayMs), () {
        if (mounted) {
          _controller.forward();
        }
      });
    } else {
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        final textLength = widget.text.length;
        final visibleLength = (textLength * _animation.value).round();
        final visibleText = widget.text.substring(0, visibleLength);

        return Text(
          visibleText,
          style: widget.style,
          textAlign: widget.textAlign,
          textDirection: TextDirection.rtl,
        );
      },
    );
  }
}

/// مكون تحريك الخلفية الإسلامية
class _IslamicBackgroundTransition extends StatefulWidget {
  final Widget child;
  final bool isDark;
  final int durationMs;

  const _IslamicBackgroundTransition({
    required this.child,
    required this.isDark,
    required this.durationMs,
  });

  @override
  State<_IslamicBackgroundTransition> createState() =>
      _IslamicBackgroundTransitionState();
}

class _IslamicBackgroundTransitionState
    extends State<_IslamicBackgroundTransition>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: widget.durationMs),
    );

    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final baseColor =
        widget.isDark ? ManhalColors.gold500 : ManhalColors.primary;
    final backgroundColor = widget.isDark ? ManhalColors.blue900 : Colors.white;

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return CustomPaint(
          painter: _IslamicBackgroundPainter(
            progress: _animation.value,
            baseColor: baseColor,
            backgroundColor: backgroundColor,
            isDark: widget.isDark,
          ),
          child: child,
        );
      },
      child: widget.child,
    );
  }
}

/// رسام الخلفية الإسلامية المتحركة
class _IslamicBackgroundPainter extends CustomPainter {
  final double progress;
  final Color baseColor;
  final Color backgroundColor;
  final bool isDark;

  _IslamicBackgroundPainter({
    required this.progress,
    required this.baseColor,
    required this.backgroundColor,
    required this.isDark,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;

    // رسم الخلفية
    final backgroundPaint = Paint()..color = backgroundColor;
    canvas.drawRect(Rect.fromLTWH(0, 0, width, height), backgroundPaint);

    // رسم الزخارف الإسلامية
    final patternPaint = Paint()
      ..color = baseColor.withValues(alpha: 0.1 * progress)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    // رسم الزخارف في الزوايا
    _drawCornerPattern(canvas, patternPaint, Offset(0, 0), size, progress);
    _drawCornerPattern(canvas, patternPaint, Offset(width, 0), size, progress);
    _drawCornerPattern(canvas, patternPaint, Offset(0, height), size, progress);
    _drawCornerPattern(
        canvas, patternPaint, Offset(width, height), size, progress);

    // رسم الإطار
    if (progress > 0.5) {
      final borderProgress = (progress - 0.5) * 2;
      final borderPaint = Paint()
        ..color = baseColor.withValues(alpha: 0.2 * borderProgress)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2.0;

      final borderRect = Rect.fromLTWH(
        10,
        10,
        width - 20,
        height - 20,
      );
      canvas.drawRect(borderRect, borderPaint);
    }
  }

  void _drawCornerPattern(
      Canvas canvas, Paint paint, Offset corner, Size size, double progress) {
    final patternSize =
        size.width < size.height ? size.width / 4 : size.height / 4;
    final adjustedSize = patternSize * progress;

    final path = Path();
    final isTopLeft = corner.dx == 0 && corner.dy == 0;
    final isTopRight = corner.dx == size.width && corner.dy == 0;
    final isBottomLeft = corner.dx == 0 && corner.dy == size.height;
    final isBottomRight = corner.dx == size.width && corner.dy == size.height;

    if (isTopLeft) {
      path.moveTo(corner.dx, corner.dy + adjustedSize);
      path.lineTo(corner.dx, corner.dy);
      path.lineTo(corner.dx + adjustedSize, corner.dy);

      // رسم الزخارف الداخلية
      path.moveTo(corner.dx + adjustedSize * 0.3, corner.dy);
      path.lineTo(
          corner.dx + adjustedSize * 0.3, corner.dy + adjustedSize * 0.3);
      path.lineTo(corner.dx, corner.dy + adjustedSize * 0.3);

      // رسم قوس زخرفي
      path.moveTo(corner.dx + adjustedSize * 0.6, corner.dy);
      path.quadraticBezierTo(
        corner.dx + adjustedSize * 0.3,
        corner.dy + adjustedSize * 0.3,
        corner.dx,
        corner.dy + adjustedSize * 0.6,
      );
    } else if (isTopRight) {
      path.moveTo(corner.dx - adjustedSize, corner.dy);
      path.lineTo(corner.dx, corner.dy);
      path.lineTo(corner.dx, corner.dy + adjustedSize);

      // رسم الزخارف الداخلية
      path.moveTo(corner.dx - adjustedSize * 0.3, corner.dy);
      path.lineTo(
          corner.dx - adjustedSize * 0.3, corner.dy + adjustedSize * 0.3);
      path.lineTo(corner.dx, corner.dy + adjustedSize * 0.3);

      // رسم قوس زخرفي
      path.moveTo(corner.dx - adjustedSize * 0.6, corner.dy);
      path.quadraticBezierTo(
        corner.dx - adjustedSize * 0.3,
        corner.dy + adjustedSize * 0.3,
        corner.dx,
        corner.dy + adjustedSize * 0.6,
      );
    } else if (isBottomLeft) {
      path.moveTo(corner.dx, corner.dy - adjustedSize);
      path.lineTo(corner.dx, corner.dy);
      path.lineTo(corner.dx + adjustedSize, corner.dy);

      // رسم الزخارف الداخلية
      path.moveTo(corner.dx, corner.dy - adjustedSize * 0.3);
      path.lineTo(
          corner.dx + adjustedSize * 0.3, corner.dy - adjustedSize * 0.3);
      path.lineTo(corner.dx + adjustedSize * 0.3, corner.dy);

      // رسم قوس زخرفي
      path.moveTo(corner.dx, corner.dy - adjustedSize * 0.6);
      path.quadraticBezierTo(
        corner.dx + adjustedSize * 0.3,
        corner.dy - adjustedSize * 0.3,
        corner.dx + adjustedSize * 0.6,
        corner.dy,
      );
    } else if (isBottomRight) {
      path.moveTo(corner.dx - adjustedSize, corner.dy);
      path.lineTo(corner.dx, corner.dy);
      path.lineTo(corner.dx, corner.dy - adjustedSize);

      // رسم الزخارف الداخلية
      path.moveTo(corner.dx - adjustedSize * 0.3, corner.dy);
      path.lineTo(
          corner.dx - adjustedSize * 0.3, corner.dy - adjustedSize * 0.3);
      path.lineTo(corner.dx, corner.dy - adjustedSize * 0.3);

      // رسم قوس زخرفي
      path.moveTo(corner.dx - adjustedSize * 0.6, corner.dy);
      path.quadraticBezierTo(
        corner.dx - adjustedSize * 0.3,
        corner.dy - adjustedSize * 0.3,
        corner.dx,
        corner.dy - adjustedSize * 0.6,
      );
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}
