import '../../data/models/poem.dart';
import '../../data/repositories/poems_repository.dart';

/// حالة استخدام لإدارة المفضلات
class ManageFavorites {
  final PoemsRepository repository;

  ManageFavorites(this.repository);

  /// الحصول على قائمة القصائد المفضلة
  Future<List<Poem>> getFavorites() async {
    return await repository.getFavoritePoems();
  }

  /// إضافة قصيدة إلى المفضلة
  Future<void> addToFavorites(int poemId) async {
    await repository.addToFavorites(poemId);
  }

  /// إزالة قصيدة من المفضلة
  Future<void> removeFromFavorites(int poemId) async {
    await repository.removeFromFavorites(poemId);
  }

  /// تبديل حالة المفضلة للقصيدة
  Future<bool> toggleFavorite(int poemId) async {
    return await repository.toggleFavorite(poemId);
  }

  /// التحقق مما إذا كانت القصيدة مفضلة
  Future<bool> isFavorite(int poemId) async {
    return await repository.isFavorite(poemId);
  }

  /// تحديث حالة المفضلة لجميع القصائد
  Future<List<Poem>> getPoemsWithFavoriteStatus() async {
    return await repository.getPoemsWithFavoriteStatus();
  }
}
