import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../data/models/poet.dart';
import '../providers/manhal_rawi_provider.dart';
import '../../../../utils/theme/manhal_colors.dart';
import '../../../../utils/theme/typography.dart';

class PoetCard extends StatelessWidget {
  final Poet poet;
  final VoidCallback onTap;
  final bool isSelected;

  const PoetCard({
    super.key,
    required this.poet,
    required this.onTap,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = context.watch<ManhalRawiProvider>().isDarkMode;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 180,
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: isSelected
                ? (isDarkMode
                    ? [
                        ManhalColors.gold600.withValues(alpha: 0.3),
                        ManhalColors.gold500.withValues(alpha: 0.1)
                      ]
                    : [
                        ManhalColors.primary.withValues(alpha: 0.2),
                        ManhalColors.gold300.withValues(alpha: 0.1)
                      ])
                : (isDarkMode
                    ? [ManhalColors.blue800, ManhalColors.blue900]
                    : [Colors.white, ManhalColors.gold100.withValues(alpha: 0.3)]),
          ),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected
                ? (isDarkMode ? ManhalColors.gold500 : ManhalColors.primary)
                : (isDarkMode ? ManhalColors.blue700 : ManhalColors.gold200),
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: isDarkMode
                  ? Colors.black.withValues(alpha: 0.3)
                  : Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              spreadRadius: 1,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // صورة الشاعر
              Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isDarkMode
                        ? ManhalColors.gold500
                        : ManhalColors.primary,
                    width: 2,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: (isDarkMode
                              ? ManhalColors.gold500
                              : ManhalColors.primary)
                          .withValues(alpha: 0.3),
                      blurRadius: 8,
                      spreadRadius: 1,
                    ),
                  ],
                ),
                child: ClipOval(
                  child: poet.imageUrl != null
                      ? Image.asset(
                          poet.imageUrl!,
                          width: 90,
                          height: 90,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) =>
                              Container(
                            width: 90,
                            height: 90,
                            color: isDarkMode
                                ? ManhalColors.blue700
                                : ManhalColors.gold100,
                            child: Icon(
                              Icons.person,
                              size: 45,
                              color: isDarkMode
                                  ? ManhalColors.gold500
                                  : ManhalColors.primary,
                            ),
                          ),
                        )
                      : Container(
                          width: 90,
                          height: 90,
                          color: isDarkMode
                              ? ManhalColors.blue700
                              : ManhalColors.gold100,
                          child: Icon(
                            Icons.person,
                            size: 45,
                            color: isDarkMode
                                ? ManhalColors.gold500
                                : ManhalColors.primary,
                          ),
                        ),
                ),
              ),

              const SizedBox(height: 16),

              // اسم الشاعر
              Text(
                poet.name,
                style: ManhalTypography.poetName.copyWith(
                  color:
                      isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
                  fontWeight: FontWeight.bold,
                  fontSize: 20,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              if (poet.title != null) ...[
                const SizedBox(height: 4),

                // لقب الشاعر
                Text(
                  poet.title!,
                  style: ManhalTypography.bodyMedium.copyWith(
                    color: isDarkMode
                        ? ManhalColors.textLight
                        : ManhalColors.textDark,
                    fontStyle: FontStyle.italic,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],

              const SizedBox(height: 8),

              // العصر الأدبي
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: isDarkMode
                      ? ManhalColors.blue700.withValues(alpha: 0.5)
                      : ManhalColors.gold100.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isDarkMode
                        ? ManhalColors.gold600.withValues(alpha: 0.3)
                        : ManhalColors.gold300,
                    width: 1,
                  ),
                ),
                child: Text(
                  poet.era,
                  style: ManhalTypography.bodySmall.copyWith(
                    color: isDarkMode
                        ? ManhalColors.gold300
                        : ManhalColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
