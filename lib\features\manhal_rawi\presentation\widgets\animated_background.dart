import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'dart:math' as math;
import '../../../../utils/theme/manhal_colors.dart';
import '../animations/background_effects.dart';

/// خلفية متحركة للتطبيق
class AnimatedBackground extends StatefulWidget {
  final Widget child;
  final bool isDark;
  final AnimatedBackgroundType type;
  final double opacity;

  const AnimatedBackground({
    super.key,
    required this.child,
    this.isDark = false,
    this.type = AnimatedBackgroundType.pattern,
    this.opacity = 1.0,
  });

  @override
  State<AnimatedBackground> createState() => _AnimatedBackgroundState();
}

class _AnimatedBackgroundState extends State<AnimatedBackground>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 20),
    );

    _animation = Tween<double>(begin: 0, end: 2 * math.pi).animate(_controller);

    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // الخلفية المتحركة
        _buildAnimatedBackground(),

        // المحتوى
        widget.child,
      ],
    );
  }

  /// بناء الخلفية المتحركة
  Widget _buildAnimatedBackground() {
    switch (widget.type) {
      case AnimatedBackgroundType.pattern:
        return _buildPatternBackground();
      case AnimatedBackgroundType.gradient:
        return _buildGradientBackground();
      case AnimatedBackgroundType.waves:
        return _buildWavesBackground();
      case AnimatedBackgroundType.light:
        return _buildLightBackground();
    }
  }

  /// بناء خلفية بنقوش إسلامية
  Widget _buildPatternBackground() {
    return Opacity(
      opacity: widget.opacity,
      child: BackgroundEffects.islamicPatternBackground(
        context: context,
        isDark: widget.isDark,
        opacity: 0.05,
      ),
    )
        .animate(
          onPlay: (controller) => controller.repeat(reverse: true),
        )
        .scale(
          duration: const Duration(seconds: 20),
          begin: const Offset(1.0, 1.0),
          end: const Offset(1.05, 1.05),
          curve: Curves.easeInOut,
        );
  }

  /// بناء خلفية بتدرج لوني
  Widget _buildGradientBackground() {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Opacity(
          opacity: widget.opacity,
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment(
                  math.cos(_animation.value) * 0.2 + 0.8,
                  math.sin(_animation.value) * 0.2,
                ),
                end: Alignment(
                  -math.cos(_animation.value) * 0.2 - 0.8,
                  -math.sin(_animation.value) * 0.2,
                ),
                colors: widget.isDark
                    ? [
                        ManhalColors.backgroundDark,
                        ManhalColors.blue900,
                        ManhalColors.blue800,
                      ]
                    : [
                        ManhalColors.backgroundLight,
                        ManhalColors.gold100,
                        Colors.white,
                      ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء خلفية بموجات
  Widget _buildWavesBackground() {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Opacity(
          opacity: widget.opacity,
          child: CustomPaint(
            painter: AnimatedWavesPainter(
              animation: _animation.value,
              isDark: widget.isDark,
            ),
            size: Size.infinite,
          ),
        );
      },
    );
  }

  /// بناء خلفية بتأثير الضوء
  Widget _buildLightBackground() {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        final size = MediaQuery.of(context).size;
        final centerX =
            size.width * 0.5 + math.cos(_animation.value) * size.width * 0.3;
        final centerY =
            size.height * 0.3 + math.sin(_animation.value) * size.height * 0.2;

        return Opacity(
          opacity: widget.opacity,
          child: BackgroundEffects.radialLightEffect(
            context: context,
            isDark: widget.isDark,
            center: Offset(centerX, centerY),
          ),
        );
      },
    );
  }
}

/// أنواع الخلفيات المتحركة
enum AnimatedBackgroundType {
  pattern, // نقوش إسلامية
  gradient, // تدرج لوني
  waves, // موجات
  light, // تأثير الضوء
}

/// رسام الموجات المتحركة
class AnimatedWavesPainter extends CustomPainter {
  final double animation;
  final bool isDark;

  AnimatedWavesPainter({
    required this.animation,
    required this.isDark,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final baseColor =
        isDark ? ManhalColors.backgroundDark : ManhalColors.backgroundLight;
    final accentColor = isDark ? ManhalColors.blue800 : ManhalColors.gold100;

    // رسم الخلفية الأساسية
    final backgroundPaint = Paint()
      ..color = baseColor
      ..style = PaintingStyle.fill;

    canvas.drawRect(
        Rect.fromLTWH(0, 0, size.width, size.height), backgroundPaint);

    // رسم الموجات
    final waveCount = 3;

    for (int i = 0; i < waveCount; i++) {
      final wavePaint = Paint()
        ..color = accentColor.withValues(alpha: 0.1 - (i * 0.02))
        ..style = PaintingStyle.fill;

      final path = Path();

      // نقطة البداية
      path.moveTo(0, size.height);

      // عدد النقاط في المنحنى
      const pointCount = 10;

      // ارتفاع الموجة
      final waveHeight = size.height * 0.1 * (waveCount - i) / waveCount;

      // تردد الموجة
      final frequency = 1.0 + (i * 0.5);

      // إزاحة الموجة
      final offset = i * (size.height * 0.05);

      // إزاحة الحركة
      final animationOffset = animation * frequency;

      // رسم المنحنى
      for (int j = 0; j <= pointCount; j++) {
        final x = size.width * j / pointCount;
        final normalizedX = x / size.width;
        final y = size.height -
            offset -
            waveHeight *
                math.sin((normalizedX * math.pi * frequency) + animationOffset);

        if (j == 0) {
          path.lineTo(x, y);
        } else {
          final prevX = size.width * (j - 1) / pointCount;
          final controlX = (prevX + x) / 2;
          final prevY = size.height -
              offset -
              waveHeight *
                  math.sin(((j - 1) / pointCount * math.pi * frequency) +
                      animationOffset);

          path.quadraticBezierTo(controlX, prevY, x, y);
        }
      }

      // إغلاق المسار
      path.lineTo(size.width, size.height);
      path.lineTo(0, size.height);
      path.close();

      canvas.drawPath(path, wavePaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
