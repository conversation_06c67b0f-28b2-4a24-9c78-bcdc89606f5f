import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/manhal_rawi_provider.dart';
import '../widgets/luxury_poem_card.dart';
import 'luxury_poem_details_screen.dart';
import 'settings_screen.dart';
import '../../../../utils/theme/manhal_colors.dart';
import '../../../../utils/theme/islamic_typography.dart';
import '../../../../utils/helpers/responsive_helper.dart';

// استخدام ColorExtension لتجنب التكرار ولمطابقة الكود الأصلي الذي قد يستخدمها
extension ColorExtension on Color {
  Color withValues({int? alpha, int? red, int? green, int? blue}) {
    return Color.fromARGB(
      alpha ?? a.toInt(),
      red ?? r.toInt(),
      green ?? g.toInt(),
      blue ?? b.toInt(),
    );
  }
}

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;
  List<String> _searchSuggestions = [];
  bool _showSuggestions = false;
  final FocusNode _searchFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();

    // تحديث حقل البحث بنص البحث الحالي في ManhalRawiProvider
    final provider = context.read<ManhalRawiProvider>();
    if (provider.searchQuery.isNotEmpty) {
      _searchController.text = provider.searchQuery;
    }

    _searchFocusNode.addListener(_onFocusChange);
    _searchController.addListener(_onSearchTextChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchTextChanged);
    _searchFocusNode.removeListener(_onFocusChange);
    _searchController.dispose();
    _searchFocusNode.dispose();

    // إعادة تعيين نتائج البحث عند الخروج من الشاشة
    // استخدام Future.microtask لضمان تنفيذ clearFilters بعد انتهاء dispose
    Future.microtask(() {
      if (mounted) {
        context.read<ManhalRawiProvider>().clearFilters();
      }
    });

    super.dispose();
  }

  void _onFocusChange() {
    if (_searchFocusNode.hasFocus && _searchController.text.isNotEmpty) {
      _updateSuggestions(_searchController.text);
    } else {
      setState(() {
        _showSuggestions = false;
      });
    }
  }

  void _onSearchTextChanged() {
    if (_searchController.text.isNotEmpty) {
      _updateSuggestions(_searchController.text);
    } else {
      setState(() {
        _showSuggestions = false;
        _searchSuggestions = [];
      });
      // إعادة تعيين نتائج البحث عندما يكون حقل البحث فارغًا
      context.read<ManhalRawiProvider>().clearFilters();
    }
  }

  void _updateSuggestions(String query) {
    if (query.length < 2) {
      setState(() {
        _showSuggestions = false;
        _searchSuggestions = [];
      });
      return;
    }

    // الحصول على اقتراحات البحث من Provider
    final provider = context.read<ManhalRawiProvider>();
    final suggestions = provider.getSearchSuggestions(query);

    setState(() {
      _searchSuggestions = suggestions;
      _showSuggestions = suggestions.isNotEmpty;
    });
  }

  void _performSearch(String query) {
    if (query.isEmpty) {
      return;
    }

    setState(() {
      _isSearching = true;
      _showSuggestions = false;
    });

    context.read<ManhalRawiProvider>().searchPoems(query).then((_) {
      // لا يوجد تحقق mounted في الأصلي
      setState(() {
        _isSearching = false;
      });
    });
  }

  void _selectSuggestion(String suggestion) {
    _searchController.text = suggestion;
    _performSearch(suggestion);
    _searchFocusNode.unfocus();
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _showSuggestions = false;
      _searchSuggestions = [];
    });
    // إعادة تعيين نتائج البحث عند الضغط على زر المسح
    context.read<ManhalRawiProvider>().clearFilters();
  }

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<ManhalRawiProvider>();
    final isDarkMode = provider.isDarkMode;
    final poems = provider.filteredPoems;
    final poets = provider.poets;

    // الحصول على أبعاد الشاشة للتوافق مع مختلف الأحجام
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = ResponsiveHelper.isSmallScreen(context);
    final isLargeScreen = ResponsiveHelper.isLargeScreen(context) ||
        ResponsiveHelper.isExtraLargeScreen(context);

    return Directionality(
      textDirection: TextDirection.rtl, // تأكيد اتجاه RTL للشاشة بأكملها
      child: Scaffold(
        body: Container(
          // بداية الـ body
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: isDarkMode
                  ? [
                      ManhalColors.blue900,
                      ManhalColors.blue800,
                    ]
                  : [
                      ManhalColors.backgroundLight,
                      Colors.white,
                    ],
            ),
            image: DecorationImage(
              image: AssetImage(
                isDarkMode
                    ? 'assets/images/paper_texture_dark.png'
                    : 'assets/images/paper_texture_light.png',
              ),
              opacity: 0.1,
              fit: BoxFit.cover,
            ),
          ),
          child: SafeArea(
            child: Column(
              children: [
                _buildAppBar(context), // بناء الـ AppBar المخصص

                // حقل البحث الفاخر
                Container(
                  margin: EdgeInsets.fromLTRB(
                    ResponsiveHelper.getResponsiveSpacing(
                      context,
                      defaultValue:
                          isSmallScreen ? 12.0 : (isLargeScreen ? 24.0 : 16.0),
                    ),
                    ResponsiveHelper.getResponsiveSpacing(
                      context,
                      defaultValue:
                          isSmallScreen ? 6.0 : (isLargeScreen ? 12.0 : 8.0),
                    ),
                    ResponsiveHelper.getResponsiveSpacing(
                      context,
                      defaultValue:
                          isSmallScreen ? 12.0 : (isLargeScreen ? 24.0 : 16.0),
                    ),
                    ResponsiveHelper.getResponsiveSpacing(
                      context,
                      defaultValue:
                          isSmallScreen ? 12.0 : (isLargeScreen ? 24.0 : 16.0),
                    ),
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(
                      ResponsiveHelper.getResponsiveBorderRadius(
                        context,
                        defaultValue: isLargeScreen ? 20.0 : 16.0,
                      ),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: isDarkMode
                            ? Colors.black.withValues(alpha: 0.2)
                            : Colors.black.withValues(alpha: 0.05),
                        blurRadius: ResponsiveHelper.getResponsiveShadow(
                          context,
                          defaultBlurRadius: isLargeScreen ? 15.0 : 10.0,
                        )[0],
                        spreadRadius: ResponsiveHelper.getResponsiveShadow(
                          context,
                          defaultSpreadRadius: 0.0,
                        )[1],
                        offset: Offset(
                            0,
                            ResponsiveHelper.getResponsiveShadow(
                              context,
                              defaultYOffset: 4.0,
                            )[2]),
                      ),
                    ],
                  ),
                  child: Material(
                    borderRadius: BorderRadius.circular(
                      ResponsiveHelper.getResponsiveBorderRadius(
                        context,
                        defaultValue: isLargeScreen ? 20.0 : 16.0,
                      ),
                    ),
                    color: Colors.transparent,
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(
                          ResponsiveHelper.getResponsiveBorderRadius(
                            context,
                            defaultValue: isLargeScreen ? 20.0 : 16.0,
                          ),
                        ),
                        gradient: LinearGradient(
                          begin: Alignment.topRight,
                          end: Alignment.bottomLeft,
                          colors: isDarkMode
                              ? [
                                  ManhalColors.blue800,
                                  ManhalColors.blue900,
                                ]
                              : [
                                  Colors.white,
                                  ManhalColors.gold100.withValues(alpha: 0.3),
                                ],
                        ),
                        border: Border.all(
                          color: isDarkMode
                              ? ManhalColors.gold500.withValues(alpha: 0.3)
                              : ManhalColors.gold300,
                          width: ResponsiveHelper.getResponsiveBorderWidth(
                            context,
                            defaultValue: isLargeScreen ? 2.0 : 1.5,
                          ),
                        ),
                      ),
                      child: Column(
                        children: [
                          TextField(
                            controller: _searchController,
                            focusNode: _searchFocusNode,
                            style: IslamicTypography.luxuryBody(
                              isDark: isDarkMode,
                              fontSize: ResponsiveHelper.getResponsiveFontSize(
                                context,
                                fontSize: isSmallScreen
                                    ? 14.0
                                    : (isLargeScreen ? 18.0 : 16.0),
                              ),
                            ),
                            decoration: InputDecoration(
                              hintText: 'ابحث عن قصيدة أو بيت شعر...',
                              hintStyle: IslamicTypography.luxuryBody(
                                isDark: isDarkMode,
                                fontSize:
                                    ResponsiveHelper.getResponsiveFontSize(
                                  context,
                                  fontSize: isSmallScreen
                                      ? 14.0
                                      : (isLargeScreen ? 18.0 : 16.0),
                                ),
                                fontWeight: FontWeight.w300,
                              ),
                              prefixIcon: Icon(
                                Icons.search,
                                color: isDarkMode
                                    ? ManhalColors.gold500
                                    : ManhalColors.primary,
                                size: ResponsiveHelper.getResponsiveIconSize(
                                  context,
                                  defaultSize: isLargeScreen ? 28.0 : 24.0,
                                ),
                              ),
                              suffixIcon: _searchController.text.isNotEmpty
                                  ? IconButton(
                                      icon: Icon(
                                        Icons.clear,
                                        color: isDarkMode
                                            ? ManhalColors.gold500
                                            : ManhalColors.primary,
                                        size: ResponsiveHelper
                                            .getResponsiveIconSize(
                                          context,
                                          defaultSize: isSmallScreen
                                              ? 18.0
                                              : (isLargeScreen ? 24.0 : 20.0),
                                        ),
                                      ),
                                      onPressed: _clearSearch,
                                    )
                                  : null,
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.symmetric(
                                horizontal:
                                    ResponsiveHelper.getResponsiveSpacing(
                                  context,
                                  defaultValue: isSmallScreen
                                      ? 12.0
                                      : (isLargeScreen ? 24.0 : 16.0),
                                ),
                                vertical: ResponsiveHelper.getResponsiveSpacing(
                                  context,
                                  defaultValue: isSmallScreen
                                      ? 12.0
                                      : (isLargeScreen ? 20.0 : 16.0),
                                ),
                              ),
                            ),
                            textInputAction: TextInputAction.search,
                            onSubmitted: _performSearch,
                          ),

                          // قائمة الاقتراحات
                          if (_showSuggestions && _searchSuggestions.isNotEmpty)
                            Container(
                              constraints: BoxConstraints(
                                maxHeight: isLargeScreen
                                    ? 300.0 *
                                        ResponsiveHelper.getScaleFactor(context)
                                    : (isSmallScreen
                                        ? 150.0 *
                                            ResponsiveHelper.getScaleFactor(
                                                context)
                                        : 200.0 *
                                            ResponsiveHelper.getScaleFactor(
                                                context)),
                              ),
                              decoration: BoxDecoration(
                                color: isDarkMode
                                    ? ManhalColors.blue800
                                        .withValues(alpha: 0.9)
                                    : Colors.white.withValues(alpha: 0.95),
                                borderRadius: BorderRadius.only(
                                  bottomLeft: Radius.circular(
                                    ResponsiveHelper.getResponsiveBorderRadius(
                                      context,
                                      defaultValue: isLargeScreen ? 20.0 : 16.0,
                                    ),
                                  ),
                                  bottomRight: Radius.circular(
                                    ResponsiveHelper.getResponsiveBorderRadius(
                                      context,
                                      defaultValue: isLargeScreen ? 20.0 : 16.0,
                                    ),
                                  ),
                                ),
                                border: Border.all(
                                  color: isDarkMode
                                      ? ManhalColors.gold500
                                          .withValues(alpha: 0.3)
                                      : ManhalColors.gold300,
                                  width:
                                      ResponsiveHelper.getResponsiveBorderWidth(
                                    context,
                                    defaultValue: isLargeScreen ? 1.5 : 1.0,
                                  ),
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: isDarkMode
                                        ? Colors.black.withValues(alpha: 0.3)
                                        : Colors.black.withValues(alpha: 0.1),
                                    blurRadius:
                                        ResponsiveHelper.getResponsiveShadow(
                                      context,
                                      defaultBlurRadius:
                                          isLargeScreen ? 12.0 : 8.0,
                                    )[0],
                                    spreadRadius:
                                        ResponsiveHelper.getResponsiveShadow(
                                      context,
                                      defaultSpreadRadius: 0.0,
                                    )[1],
                                    offset: Offset(
                                        0,
                                        ResponsiveHelper.getResponsiveShadow(
                                          context,
                                          defaultYOffset: 4.0,
                                        )[2]),
                                  ),
                                ],
                              ),
                              child: ListView.separated(
                                padding: EdgeInsets.symmetric(
                                  vertical:
                                      ResponsiveHelper.getResponsiveSpacing(
                                    context,
                                    defaultValue: isSmallScreen
                                        ? 6.0
                                        : (isLargeScreen ? 12.0 : 8.0),
                                  ),
                                ),
                                shrinkWrap: true,
                                itemCount: _searchSuggestions.length,
                                separatorBuilder: (context, index) => Divider(
                                  color: isDarkMode
                                      ? ManhalColors.gold500
                                          .withValues(alpha: 0.1)
                                      : ManhalColors.gold300
                                          .withValues(alpha: 0.3),
                                  height: 1,
                                  indent: ResponsiveHelper.getResponsiveSpacing(
                                    context,
                                    defaultValue: isSmallScreen
                                        ? 12.0
                                        : (isLargeScreen ? 24.0 : 16.0),
                                  ),
                                  endIndent:
                                      ResponsiveHelper.getResponsiveSpacing(
                                    context,
                                    defaultValue: isSmallScreen
                                        ? 12.0
                                        : (isLargeScreen ? 24.0 : 16.0),
                                  ),
                                ),
                                itemBuilder: (context, index) {
                                  final suggestion = _searchSuggestions[index];
                                  return InkWell(
                                    onTap: () => _selectSuggestion(suggestion),
                                    child: Padding(
                                      padding: EdgeInsets.symmetric(
                                        horizontal: ResponsiveHelper
                                            .getResponsiveSpacing(
                                          context,
                                          defaultValue: isSmallScreen
                                              ? 12.0
                                              : (isLargeScreen ? 24.0 : 16.0),
                                        ),
                                        vertical: ResponsiveHelper
                                            .getResponsiveSpacing(
                                          context,
                                          defaultValue: isSmallScreen
                                              ? 8.0
                                              : (isLargeScreen ? 16.0 : 12.0),
                                        ),
                                      ),
                                      child: Row(
                                        children: [
                                          Icon(
                                            Icons.history,
                                            size: ResponsiveHelper
                                                .getResponsiveIconSize(
                                              context,
                                              defaultSize: isSmallScreen
                                                  ? 16.0
                                                  : (isLargeScreen
                                                      ? 22.0
                                                      : 18.0),
                                            ),
                                            color: isDarkMode
                                                ? ManhalColors.gold500
                                                    .withValues(alpha: 0.7)
                                                : ManhalColors.primary
                                                    .withValues(alpha: 0.7),
                                          ),
                                          SizedBox(
                                            width: ResponsiveHelper
                                                .getResponsiveSpacing(
                                              context,
                                              defaultValue: isSmallScreen
                                                  ? 8.0
                                                  : (isLargeScreen
                                                      ? 16.0
                                                      : 12.0),
                                            ),
                                          ),
                                          Expanded(
                                            child: Text(
                                              suggestion,
                                              style:
                                                  IslamicTypography.luxuryBody(
                                                isDark: isDarkMode,
                                                fontSize: ResponsiveHelper
                                                    .getResponsiveFontSize(
                                                  context,
                                                  fontSize: isSmallScreen
                                                      ? 12.0
                                                      : (isLargeScreen
                                                          ? 16.0
                                                          : 14.0),
                                                ),
                                              ),
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                          IconButton(
                                            icon: Icon(
                                              Icons.north_west,
                                              size: ResponsiveHelper
                                                  .getResponsiveIconSize(
                                                context,
                                                defaultSize: isSmallScreen
                                                    ? 14.0
                                                    : (isLargeScreen
                                                        ? 20.0
                                                        : 16.0),
                                              ),
                                              color: isDarkMode
                                                  ? ManhalColors.gold500
                                                  : ManhalColors.primary,
                                            ),
                                            onPressed: () =>
                                                _selectSuggestion(suggestion),
                                            padding: EdgeInsets.zero,
                                            constraints: BoxConstraints(
                                              minWidth: isSmallScreen
                                                  ? 24.0 *
                                                      ResponsiveHelper
                                                          .getScaleFactor(
                                                              context)
                                                  : (isLargeScreen
                                                      ? 40.0 *
                                                          ResponsiveHelper
                                                              .getScaleFactor(
                                                                  context)
                                                      : 32.0 *
                                                          ResponsiveHelper
                                                              .getScaleFactor(
                                                                  context)),
                                              minHeight: isSmallScreen
                                                  ? 24.0 *
                                                      ResponsiveHelper
                                                          .getScaleFactor(
                                                              context)
                                                  : (isLargeScreen
                                                      ? 40.0 *
                                                          ResponsiveHelper
                                                              .getScaleFactor(
                                                                  context)
                                                      : 32.0 *
                                                          ResponsiveHelper
                                                              .getScaleFactor(
                                                                  context)),
                                            ),
                                            splashRadius: isSmallScreen
                                                ? 16.0 *
                                                    ResponsiveHelper
                                                        .getScaleFactor(context)
                                                : (isLargeScreen
                                                    ? 24.0 *
                                                        ResponsiveHelper
                                                            .getScaleFactor(
                                                                context)
                                                    : 20.0 *
                                                        ResponsiveHelper
                                                            .getScaleFactor(
                                                                context)),
                                          ),
                                        ],
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ),

                // نتائج البحث
                if (_isSearching)
                  Expanded(
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 50,
                            height: 50,
                            child: CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation<Color>(
                                isDarkMode
                                    ? ManhalColors.gold500
                                    : ManhalColors.primary,
                              ),
                              strokeWidth: 3,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'جاري البحث...',
                            style: IslamicTypography.luxuryBody(
                              isDark: isDarkMode,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                else if (provider.searchQuery.isNotEmpty)
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // عنوان نتائج البحث
                        Container(
                          margin: EdgeInsets.fromLTRB(
                            isSmallScreen ? 12 : (isLargeScreen ? 24 : 16),
                            0, // الهامش الأصلي
                            isSmallScreen ? 12 : (isLargeScreen ? 24 : 16),
                            isSmallScreen ? 12 : (isLargeScreen ? 24 : 16),
                          ),
                          padding: EdgeInsets.symmetric(
                            vertical:
                                isSmallScreen ? 8 : (isLargeScreen ? 16 : 12),
                            horizontal:
                                isSmallScreen ? 12 : (isLargeScreen ? 24 : 16),
                          ),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.centerRight,
                              end: Alignment.centerLeft,
                              colors: isDarkMode
                                  ? [
                                      ManhalColors.blue800,
                                      ManhalColors.blue900,
                                    ]
                                  : [
                                      ManhalColors.gold100,
                                      Colors.white,
                                    ],
                            ),
                            borderRadius:
                                BorderRadius.circular(isLargeScreen ? 16 : 12),
                            border: Border.all(
                              color: isDarkMode
                                  // استخدام withValues لمطابقة الأصلي
                                  ? ManhalColors.gold500.withValues(alpha: 0.3)
                                  : ManhalColors.gold300,
                              width: isLargeScreen ? 1.5 : 1,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: isDarkMode
                                    // استخدام withValues لمطابقة الأصلي
                                    ? Colors.black.withValues(alpha: 0.2)
                                    : Colors.black.withValues(alpha: 0.05),
                                blurRadius: isLargeScreen ? 6 : 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.search, // الأيقونة الأصلية
                                color: isDarkMode
                                    ? ManhalColors.gold500
                                    : ManhalColors.primary,
                                size: isSmallScreen
                                    ? 18
                                    : (isLargeScreen ? 24 : 20),
                              ),
                              SizedBox(
                                  width: isSmallScreen
                                      ? 6
                                      : (isLargeScreen ? 12 : 8)),
                              Expanded(
                                child: Text(
                                  'نتائج البحث عن "${provider.searchQuery}"',
                                  style: IslamicTypography.luxuryTitle(
                                    isDark: isDarkMode,
                                    fontSize: isSmallScreen
                                        ? 14
                                        : (isLargeScreen ? 18 : 16),
                                  ),
                                  // لا يوجد overflow في الأصلي
                                ),
                              ),
                              // لا يوجد SizedBox في الأصلي
                              Text(
                                '${poems.length} نتيجة',
                                style: IslamicTypography.luxuryCaption(
                                  isDark: isDarkMode,
                                  fontSize: isSmallScreen
                                      ? 12
                                      : (isLargeScreen ? 16 : 14),
                                ),
                              ),
                            ],
                          ),
                        ),

                        if (poems.isEmpty)
                          Expanded(
                            child: Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.search_off,
                                    size: isSmallScreen
                                        ? 48
                                        : (isLargeScreen ? 80 : 64),
                                    color: isDarkMode
                                        // استخدام withValues لمطابقة الأصلي
                                        ? ManhalColors.gold500
                                            .withValues(alpha: 0.5)
                                        : ManhalColors.gold300,
                                  ),
                                  SizedBox(
                                      height: isSmallScreen
                                          ? 12
                                          : (isLargeScreen ? 24 : 16)),
                                  Text(
                                    'لا توجد نتائج مطابقة',
                                    style: IslamicTypography.luxuryBody(
                                      isDark: isDarkMode,
                                      fontSize: isSmallScreen
                                          ? 16
                                          : (isLargeScreen ? 22 : 18),
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  SizedBox(
                                      height: isSmallScreen
                                          ? 6
                                          : (isLargeScreen ? 12 : 8)),
                                  Text(
                                    'حاول استخدام كلمات بحث مختلفة',
                                    style: IslamicTypography.luxuryCaption(
                                      isDark: isDarkMode,
                                      fontSize: isSmallScreen
                                          ? 12
                                          : (isLargeScreen ? 16 : 14),
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ),
                          )
                        else
                          Expanded(
                            child: isLargeScreen && screenSize.width > 800
                                // استخدام GridView للشاشات الكبيرة
                                ? GridView.builder(
                                    padding: EdgeInsets.only(
                                      // الحشو الأصلي
                                      bottom: isLargeScreen ? 24 : 16,
                                      left: isSmallScreen
                                          ? 8
                                          : (isLargeScreen ? 20 : 12),
                                      right: isSmallScreen
                                          ? 8
                                          : (isLargeScreen ? 20 : 12),
                                    ),
                                    gridDelegate:
                                        SliverGridDelegateWithFixedCrossAxisCount(
                                      crossAxisCount:
                                          screenSize.width > 1200 ? 3 : 2,
                                      childAspectRatio: 1.2, // النسبة الأصلية
                                      crossAxisSpacing: isLargeScreen
                                          ? 16
                                          : 8, // المسافة الأصلية
                                      mainAxisSpacing: isLargeScreen
                                          ? 16
                                          : 8, // المسافة الأصلية
                                    ), // التأكد من إغلاق القوس
                                    itemCount: poems.length,
                                    itemBuilder: (context, index) {
                                      // بداية itemBuilder
                                      final poem = poems[index];
                                      // البحث عن الشاعر، استخدام الأول كاحتياط إذا لم يوجد
                                      final poet = poets.firstWhere(
                                          (p) => p.id == poem.poetId,
                                          orElse: () => poets.first);

                                      return AnimatedOpacity(
                                        duration:
                                            const Duration(milliseconds: 500),
                                        opacity: 1.0,
                                        curve: Curves.easeInOut,
                                        child: GestureDetector(
                                          onTap: () {
                                            Navigator.push(
                                              context,
                                              PageRouteBuilder(
                                                pageBuilder: (context,
                                                        animation,
                                                        secondaryAnimation) =>
                                                    LuxuryPoemDetailsScreen(
                                                  poem: poem,
                                                  poet: poet,
                                                ), // أغلق هنا
                                                transitionsBuilder: (context,
                                                    animation,
                                                    secondaryAnimation,
                                                    child) {
                                                  return FadeTransition(
                                                    opacity: animation,
                                                    child: child,
                                                  ); // أغلق هنا
                                                },
                                                transitionDuration:
                                                    const Duration(
                                                        milliseconds: 500),
                                              ), // أغلق هنا
                                            ); // أغلق هنا و ;
                                          },
                                          child: LuxuryPoemCard(
                                            poem: poem,
                                            poet: poet,
                                            showAnimation: index < 3, // الأصلي
                                          ), // أغلق هنا
                                        ), // أغلق هنا
                                      ); // أغلق هنا
                                    }, // أغلق هنا
                                  ) // <<<--- !!! تم التأكد من إضافة هذا القوس لإغلاق GridView.builder !!!
                                // استخدام ListView للشاشات الصغيرة
                                : ListView.builder(
                                    padding: EdgeInsets.only(
                                      // الحشو الأصلي
                                      bottom: isLargeScreen
                                          ? 24
                                          : (isSmallScreen ? 12 : 16),
                                    ),
                                    itemCount: poems.length,
                                    itemBuilder: (context, index) {
                                      // بداية itemBuilder
                                      final poem = poems[index];
                                      // البحث عن الشاعر، استخدام الأول كاحتياط
                                      final poet = poets.firstWhere(
                                          (p) => p.id == poem.poetId,
                                          orElse: () => poets.first);

                                      return AnimatedOpacity(
                                        duration:
                                            const Duration(milliseconds: 500),
                                        opacity: 1.0,
                                        curve: Curves.easeInOut,
                                        child: AnimatedPadding(
                                          duration:
                                              const Duration(milliseconds: 500),
                                          padding: EdgeInsets.symmetric(
                                            // الحشو الأصلي
                                            vertical: isSmallScreen
                                                ? 3
                                                : (isLargeScreen ? 6 : 4),
                                            horizontal: isSmallScreen
                                                ? 8
                                                : (isLargeScreen ? 16 : 12),
                                          ), // أغلق هنا
                                          child: GestureDetector(
                                            onTap: () {
                                              Navigator.push(
                                                context,
                                                PageRouteBuilder(
                                                  pageBuilder: (context,
                                                          animation,
                                                          secondaryAnimation) =>
                                                      LuxuryPoemDetailsScreen(
                                                    poem: poem,
                                                    poet: poet,
                                                  ), // أغلق هنا
                                                  transitionsBuilder: (context,
                                                      animation,
                                                      secondaryAnimation,
                                                      child) {
                                                    return FadeTransition(
                                                      opacity: animation,
                                                      child: child,
                                                    ); // أغلق هنا
                                                  },
                                                  transitionDuration:
                                                      const Duration(
                                                          milliseconds: 500),
                                                ), // أغلق هنا
                                              ); // أغلق هنا و ;
                                            },
                                            child: LuxuryPoemCard(
                                              poem: poem,
                                              poet: poet,
                                              showAnimation:
                                                  index < 3, // الأصلي
                                            ), // أغلق هنا
                                          ), // أغلق هنا
                                        ), // أغلق هنا
                                      ); // أغلق هنا
                                    }, // أغلق هنا
                                  ), // أغلق هنا (نهاية ListView.builder)
                          ), // أغلق هنا (نهاية Expanded)
                      ],
                    ), // أغلق هنا (نهاية Column)
                  ) // أغلق هنا (نهاية Expanded المشروط)
                else
                  Expanded(
                    // بداية الـ Expanded للحالة الافتراضية
                    child: SingleChildScrollView(
                      physics: const AlwaysScrollableScrollPhysics(), // الأصلي
                      child: ConstrainedBox(
                        constraints: BoxConstraints(
                          minHeight: MediaQuery.of(context).size.height *
                              0.5, // الأصلي
                        ), // أغلق هنا
                        child: Center(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                                vertical: 16), // الأصلي
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                // استخدام حجم متغير للأيقونة بناءً على حجم الشاشة
                                Container(
                                  width:
                                      MediaQuery.of(context).size.width * 0.2,
                                  height:
                                      MediaQuery.of(context).size.width * 0.2,
                                  constraints: const BoxConstraints(
                                    // الأصلي
                                    maxWidth: 80,
                                    maxHeight: 80,
                                    minWidth: 60,
                                    minHeight: 60,
                                  ), // أغلق هنا
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle, // الأصلي
                                    color: isDarkMode // الأصلي
                                        // استخدام withValues لمطابقة الأصلي
                                        ? ManhalColors.blue800
                                            .withValues(alpha: 0.3)
                                        : ManhalColors.gold100
                                            .withValues(alpha: 0.3),
                                    border: Border.all(
                                      // الأصلي
                                      color: isDarkMode
                                          // استخدام withValues لمطابقة الأصلي
                                          ? ManhalColors.gold500
                                              .withValues(alpha: 0.3)
                                          : ManhalColors.gold300,
                                      width: 1,
                                    ), // أغلق هنا
                                  ), // أغلق هنا
                                  child: Icon(
                                    // الأصلي
                                    Icons.search,
                                    size:
                                        MediaQuery.of(context).size.width * 0.1,
                                    color: isDarkMode
                                        ? ManhalColors.gold500
                                        : ManhalColors.primary,
                                  ), // أغلق هنا
                                ), // أغلق هنا
                                const SizedBox(height: 16), // الأصلي
                                Text(
                                  // الأصلي
                                  'ابحث عن قصيدة أو بيت شعر',
                                  style: IslamicTypography.luxuryTitle(
                                    isDark: isDarkMode,
                                    fontSize: 18,
                                  ),
                                  textAlign: TextAlign.center,
                                ), // أغلق هنا
                                const SizedBox(height: 8), // الأصلي
                                Container(
                                  // الأصلي
                                  width:
                                      MediaQuery.of(context).size.width * 0.7,
                                  constraints: const BoxConstraints(
                                    maxWidth: 300,
                                  ), // أغلق هنا
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 12, vertical: 8), // الأصلي
                                  decoration: BoxDecoration(
                                    // الأصلي
                                    color: isDarkMode
                                        // استخدام withValues لمطابقة الأصلي
                                        ? ManhalColors.blue800
                                            .withValues(alpha: 0.5)
                                        : ManhalColors.gold100
                                            .withValues(alpha: 0.5),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: isDarkMode
                                          // استخدام withValues لمطابقة الأصلي
                                          ? ManhalColors.gold500
                                              .withValues(alpha: 0.2)
                                          : ManhalColors.gold300
                                              .withValues(alpha: 0.5),
                                      width: 1,
                                    ), // أغلق هنا
                                  ), // أغلق هنا
                                  child: Text(
                                    // الأصلي
                                    'يمكنك البحث بعنوان القصيدة أو محتواها أو اسم الشاعر',
                                    style: IslamicTypography.luxuryCaption(
                                      isDark: isDarkMode,
                                      fontSize: 13,
                                    ),
                                    textAlign: TextAlign.center,
                                  ), // أغلق هنا
                                ), // أغلق هنا
                              ], // نهاية children للـ Column
                            ), // أغلق هنا (نهاية Padding)
                          ), // أغلق هنا (نهاية Center)
                        ), // أغلق هنا (نهاية ConstrainedBox)
                      ), // أغلق هنا (نهاية SingleChildScrollView)
                    ),
                  ),
              ],
            ),
          ),
        ),
        floatingActionButton: _searchController.text.isNotEmpty
            ? Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: isDarkMode
                          // استخدام withValues لمطابقة الأصلي
                          ? ManhalColors.gold500.withValues(alpha: 0.3)
                          : ManhalColors.primary.withValues(alpha: 0.2),
                      blurRadius: 10,
                      spreadRadius: 2,
                    ), // أغلق هنا
                  ], // أغلق هنا
                ), // أغلق هنا
                child: FloatingActionButton(
                  onPressed: () {
                    _performSearch(_searchController.text);
                  }, // أغلق هنا
                  backgroundColor: isDarkMode
                      ? ManhalColors.gold500
                      : ManhalColors.primary, // الأصلي
                  foregroundColor: Colors.white, // الأصلي
                  elevation: 4, // الأصلي
                  shape: RoundedRectangleBorder(
                    // الأصلي
                    borderRadius: BorderRadius.circular(30),
                    side: BorderSide(
                      color: isDarkMode
                          // استخدام withValues لمطابقة الأصلي
                          ? ManhalColors.gold300
                          : Colors.white.withValues(alpha: 0.8),
                      width: 2,
                    ), // أغلق هنا
                  ), // أغلق هنا
                  child: const Icon(Icons.search, size: 28), // الأصلي
                ), // أغلق هنا
              ) // أغلق هنا (نهاية Container)
            : null, // نهاية floatingActionButton
      ),
    ); // أغلق هنا (نهاية Directionality)
  } // نهاية دالة build

  Widget _buildAppBar(BuildContext context) {
    final isDarkMode = context.watch<ManhalRawiProvider>().isDarkMode;

    // الحصول على أبعاد الشاشة للتوافق مع مختلف الأحجام
    final isSmallScreen = ResponsiveHelper.isSmallScreen(context);
    final isLargeScreen = ResponsiveHelper.isLargeScreen(context) ||
        ResponsiveHelper.isExtraLargeScreen(context);

    // تحديد أحجام العناصر بناءً على حجم الشاشة
    final double iconSize = ResponsiveHelper.getResponsiveIconSize(
      context,
      defaultSize: isLargeScreen ? 24.0 : (isSmallScreen ? 18.0 : 20.0),
    );

    final double buttonSize = ResponsiveHelper.getResponsiveIconSize(
      context,
      defaultSize: isLargeScreen ? 48.0 : (isSmallScreen ? 36.0 : 40.0),
    );

    final double borderWidth = ResponsiveHelper.getResponsiveBorderWidth(
      context,
      defaultValue: isLargeScreen ? 1.5 : 1.0,
    );

    final double titleFontSize = ResponsiveHelper.getResponsiveFontSize(
      context,
      fontSize: isLargeScreen ? 22.0 : (isSmallScreen ? 16.0 : 18.0),
    );

    final EdgeInsets padding = EdgeInsets.symmetric(
      horizontal: ResponsiveHelper.getResponsiveSpacing(
        context,
        defaultValue: isLargeScreen ? 16.0 : (isSmallScreen ? 6.0 : 8.0),
      ),
      vertical: ResponsiveHelper.getResponsiveSpacing(
        context,
        defaultValue: isLargeScreen ? 16.0 : (isSmallScreen ? 8.0 : 12.0),
      ),
    );

    return Container(
      padding: padding, // الأصلي
      decoration: BoxDecoration(
        gradient: LinearGradient(
          // الأصلي
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: isDarkMode
              ? [
                  ManhalColors.blue800,
                  ManhalColors.blue900,
                ]
              : [
                  Colors.white,
                  // استخدام withValues لمطابقة الأصلي
                  ManhalColors.gold100.withValues(alpha: 0.3),
                ],
        ), // أغلق هنا
        border: Border(
          // الأصلي
          bottom: BorderSide(
            color: isDarkMode
                // استخدام withValues لمطابقة الأصلي
                ? ManhalColors.gold500.withValues(alpha: 0.2)
                : ManhalColors.gold300.withValues(alpha: 0.5),
            width: borderWidth,
          ), // أغلق هنا
        ), // أغلق هنا
        boxShadow: [
          // الأصلي
          BoxShadow(
            color: isDarkMode
                // استخدام withValues لمطابقة الأصلي
                ? Colors.black.withValues(alpha: 0.2)
                : Colors.black.withValues(alpha: 0.05),
            blurRadius: isLargeScreen ? 12 : 8,
            offset: const Offset(0, 2),
          ), // أغلق هنا
        ], // أغلق هنا
      ), // أغلق هنا
      child: Row(
        children: [
          // زر الرجوع المزخرف
          Container(
            // الأصلي
            height: buttonSize,
            width: buttonSize,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isDarkMode
                  // استخدام withValues لمطابقة الأصلي
                  ? ManhalColors.blue800.withValues(alpha: 0.5)
                  : Colors.white.withValues(alpha: 0.8),
              border: Border.all(
                color: isDarkMode
                    // استخدام withValues لمطابقة الأصلي
                    ? ManhalColors.gold500.withValues(alpha: 0.3)
                    : ManhalColors.gold300,
                width: borderWidth,
              ), // أغلق هنا
            ), // أغلق هنا
            child: IconButton(
              // الأصلي
              icon: Icon(
                Icons
                    .arrow_back_ios_new, // تغيير من arrow_back_ios إلى arrow_forward_ios للتوافق مع اتجاه RTL
                color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
                size: iconSize,
              ), // أغلق هنا
              onPressed: () {
                // إعادة تعيين نتائج البحث قبل العودة إلى الشاشة السابقة
                final provider = context.read<ManhalRawiProvider>();
                provider.clearFilters();

                // العودة إلى الشاشة السابقة مباشرة
                Navigator.pop(context);
              }, // أغلق هنا
              tooltip: 'الرجوع',
              padding: EdgeInsets.zero,
            ), // أغلق هنا
          ), // أغلق هنا

          // عنوان الصفحة
          Expanded(
            // الأصلي
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  // الأصلي
                  Icons.search,
                  color:
                      isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
                  size: iconSize,
                ), // أغلق هنا
                SizedBox(width: isSmallScreen ? 4 : 8), // الأصلي
                Text(
                  // الأصلي
                  'البحث في المنهل الروي',
                  style: IslamicTypography.luxuryTitle(
                    isDark: isDarkMode,
                    fontSize: titleFontSize,
                  ),
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                ), // أغلق هنا
              ], // أغلق هنا
            ), // أغلق هنا
          ), // أغلق هنا

          // زر الإعدادات المزخرف
          Container(
            // الأصلي
            height: buttonSize,
            width: buttonSize,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isDarkMode
                  // استخدام withValues لمطابقة الأصلي
                  ? ManhalColors.blue800.withValues(alpha: 0.5)
                  : Colors.white.withValues(alpha: 0.8),
              border: Border.all(
                color: isDarkMode
                    // استخدام withValues لمطابقة الأصلي
                    ? ManhalColors.gold500.withValues(alpha: 0.3)
                    : ManhalColors.gold300,
                width: borderWidth,
              ), // أغلق هنا
            ), // أغلق هنا
            child: IconButton(
              // الأصلي
              icon: Icon(
                Icons.settings,
                color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
                size: iconSize,
              ), // أغلق هنا
              onPressed: () {
                // إعادة تعيين نتائج البحث قبل الانتقال إلى شاشة الإعدادات
                final provider = context.read<ManhalRawiProvider>();
                provider.clearFilters();

                Navigator.push(
                  context,
                  PageRouteBuilder(
                    pageBuilder: (context, animation, secondaryAnimation) =>
                        const SettingsScreen(), // الأصلي
                    transitionsBuilder:
                        (context, animation, secondaryAnimation, child) {
                      return FadeTransition(
                        opacity: animation,
                        child: child,
                      ); // أغلق هنا
                    }, // أغلق هنا
                    transitionDuration:
                        const Duration(milliseconds: 300), // الأصلي
                  ), // أغلق هنا
                ); // أغلق هنا و ;
              }, // أغلق هنا
              tooltip: 'الإعدادات',
              padding: EdgeInsets.zero,
            ), // أغلق هنا
          ), // أغلق هنا
        ], // نهاية children للـ Row
      ), // أغلق هنا (نهاية Container الـ AppBar)
    ); // أغلق هنا (نهاية return)
  } // نهاية دالة _buildAppBar
} // نهاية كلاس _SearchScreenState
