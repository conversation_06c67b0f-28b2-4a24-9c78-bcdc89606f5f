import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../data/models/verse.dart';
import '../providers/manhal_rawi_provider.dart';
import '../../../../utils/theme/manhal_colors.dart';
import '../../../../utils/theme/islamic_typography.dart';
import '../../../../utils/animations/islamic_animations.dart';

/// مكون عرض الأبيات الشعرية بتأثيرات متحركة
class AnimatedVerseDisplay extends StatefulWidget {
  final List<Verse> verses;
  final int selectedIndex;
  final Function(int)? onVerseSelected;
  final bool showAnimation;
  final bool showDivider;
  final bool enableSelection;

  const AnimatedVerseDisplay({
    super.key,
    required this.verses,
    this.selectedIndex = 0,
    this.onVerseSelected,
    this.showAnimation = true,
    this.showDivider = true,
    this.enableSelection = true,
  });

  @override
  State<AnimatedVerseDisplay> createState() => _AnimatedVerseDisplayState();
}

class _AnimatedVerseDisplayState extends State<AnimatedVerseDisplay> {
  late PageController _pageController;
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.selectedIndex;
    _pageController = PageController(initialPage: _currentIndex);
  }

  @override
  void didUpdateWidget(AnimatedVerseDisplay oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.selectedIndex != _currentIndex) {
      _currentIndex = widget.selectedIndex;
      _pageController.animateToPage(
        _currentIndex,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<ManhalRawiProvider>();
    final isDarkMode = provider.isDarkMode;

    return Column(
      children: [
        // عرض الأبيات
        SizedBox(
          height: 180,
          child: PageView.builder(
            controller: _pageController,
            itemCount: widget.verses.length,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index;
              });
              if (widget.onVerseSelected != null) {
                widget.onVerseSelected!(index);
              }
            },
            itemBuilder: (context, index) {
              final verse = widget.verses[index];
              final isSelected = index == _currentIndex;

              return _buildVerseCard(verse, isDarkMode, isSelected);
            },
          ),
        ),

        const SizedBox(height: 16),

        // مؤشرات الصفحات
        if (widget.verses.length > 1)
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              widget.verses.length,
              (index) => _buildPageIndicator(index, isDarkMode),
            ),
          ),
      ],
    );
  }

  Widget _buildVerseCard(Verse verse, bool isDarkMode, bool isSelected) {
    return GestureDetector(
      onTap: widget.enableSelection
          ? () {
              if (widget.onVerseSelected != null) {
                widget.onVerseSelected!(_currentIndex);
              }
            }
          : null,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topRight,
            end: Alignment.bottomLeft,
            colors: isSelected
                ? (isDarkMode
                    ? [
                        ManhalColors.blue800,
                        ManhalColors.blue900,
                      ]
                    : [
                        ManhalColors.gold100.withValues(alpha: 0.7),
                        Colors.white,
                      ])
                : (isDarkMode
                    ? [
                        ManhalColors.blue900,
                        ManhalColors.blue800,
                      ]
                    : [
                        Colors.white,
                        ManhalColors.gold100.withValues(alpha: 0.3),
                      ]),
          ),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected
                ? (isDarkMode ? ManhalColors.gold500 : ManhalColors.primary)
                : (isDarkMode ? ManhalColors.blue700 : ManhalColors.gold200),
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: isSelected
                  ? (isDarkMode
                      ? ManhalColors.gold500.withValues(alpha: 0.3)
                      : ManhalColors.primary.withValues(alpha: 0.2))
                  : Colors.black.withValues(alpha: 0.05),
              blurRadius: isSelected ? 10 : 5,
              spreadRadius: isSelected ? 1 : 0,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // الشطر الأول
              widget.showAnimation && isSelected
                  ? IslamicAnimations.arabicTextTyping(
                      text: verse.first,
                      style: IslamicTypography.luxuryVerse(
                        isDark: isDarkMode,
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                      durationMs: 1000,
                      textAlign: TextAlign.center,
                    )
                  : Text(
                      verse.first,
                      style: IslamicTypography.luxuryVerse(
                        isDark: isDarkMode,
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),

              // الفاصل بين الشطرين
              if (widget.showDivider)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: Container(
                    height: 1,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.transparent,
                          isDarkMode
                              ? ManhalColors.gold500.withValues(alpha: 0.7)
                              : ManhalColors.primary.withValues(alpha: 0.7),
                          Colors.transparent,
                        ],
                        stops: const [0.0, 0.5, 1.0],
                      ),
                    ),
                  ),
                ),

              // الشطر الثاني
              widget.showAnimation && isSelected
                  ? IslamicAnimations.arabicTextTyping(
                      text: verse.second,
                      style: IslamicTypography.luxuryVerse(
                        isDark: isDarkMode,
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                      durationMs: 1000,
                      delayMs: 500,
                      textAlign: TextAlign.center,
                    )
                  : Text(
                      verse.second,
                      style: IslamicTypography.luxuryVerse(
                        isDark: isDarkMode,
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPageIndicator(int index, bool isDarkMode) {
    final isActive = index == _currentIndex;

    return GestureDetector(
      onTap: () {
        _pageController.animateToPage(
          index,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        margin: const EdgeInsets.symmetric(horizontal: 4),
        height: 8,
        width: isActive ? 24 : 8,
        decoration: BoxDecoration(
          color: isActive
              ? (isDarkMode ? ManhalColors.gold500 : ManhalColors.primary)
              : (isDarkMode ? ManhalColors.blue700 : ManhalColors.gold200),
          borderRadius: BorderRadius.circular(4),
          boxShadow: isActive
              ? [
                  BoxShadow(
                    color: isDarkMode
                        ? ManhalColors.gold500.withValues(alpha: 0.3)
                        : ManhalColors.primary.withValues(alpha: 0.3),
                    blurRadius: 4,
                    spreadRadius: 1,
                  ),
                ]
              : null,
        ),
      ),
    );
  }
}
