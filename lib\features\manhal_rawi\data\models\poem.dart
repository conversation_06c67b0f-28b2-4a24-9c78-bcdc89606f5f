import 'verse.dart';
import 'mukhammas_verse.dart';

/// أنواع القصائد المدعومة في التطبيق
enum PoemType {
  /// قصيدة عادية (كل بيت مكون من شطرين)
  regular,

  /// قصيدة مخمسة (كل بيت مكون من خمسة أشطر)
  mukhammas,
}

class Poem {
  final int id;
  final String title;
  final int poetId;
  final int categoryId;
  final List<Verse> verses;
  final List<MukhammasVerse>? mukhammasVerses; // أبيات القصيدة المخمسة
  final String description;
  final String meter;
  final String firstLetter; // الحرف الأول من القصيدة (محدد في قاعدة البيانات)
  final bool isFavorite; // حالة المفضلة
  final PoemType type; // نوع القصيدة (عادية أو مخمسة)

  Poem({
    required this.id,
    required this.title,
    required this.poetId,
    required this.categoryId,
    required this.verses,
    this.mukhammasVerses,
    required this.description,
    required this.meter,
    required this.firstLetter,
    this.isFavorite = false, // القيمة الافتراضية هي عدم الإضافة للمفضلة
    this.type = PoemType.regular, // القيمة الافتراضية هي قصيدة عادية
  });

  factory Poem.fromJson(Map<String, dynamic> json) {
    // تحديد نوع القصيدة
    final poemType =
        json['type'] == 'mukhammas' ? PoemType.mukhammas : PoemType.regular;

    // تحميل الأبيات المناسبة حسب نوع القصيدة
    List<Verse> verses = [];
    List<MukhammasVerse>? mukhammasVerses;

    if (poemType == PoemType.regular) {
      // تحميل الأبيات العادية
      if (json['verses'] != null) {
        verses = (json['verses'] as List)
            .map((verse) => Verse.fromJson(verse))
            .toList();
      }
    } else {
      // تحميل الأبيات المخمسة
      if (json['mukhammas_verses'] != null) {
        mukhammasVerses = (json['mukhammas_verses'] as List)
            .map((verse) => MukhammasVerse.fromJson(verse))
            .toList();
      }

      // إنشاء أبيات عادية فارغة للتوافق مع الواجهات القديمة
      verses = [];
    }

    return Poem(
      id: json['id'],
      title: json['title'],
      poetId: json['poetId'],
      categoryId: json['categoryId'],
      verses: verses,
      mukhammasVerses: mukhammasVerses,
      description: json['description'],
      meter: json['meter'],
      // استخدام الحرف الأول من قاعدة البيانات، أو استخراجه من العنوان إذا لم يكن موجوداً
      firstLetter: json['firstLetter'] ?? _getDefaultFirstLetter(json['title']),
      // استخدام حالة المفضلة من قاعدة البيانات، أو القيمة الافتراضية false
      isFavorite: json['isFavorite'] ?? false,
      // تعيين نوع القصيدة
      type: poemType,
    );
  }

  // دالة مساعدة لاستخراج الحرف الأول من العنوان إذا لم يكن محدداً في قاعدة البيانات
  static String _getDefaultFirstLetter(String? title) {
    if (title == null || title.isEmpty) return 'ا';

    // تنظيف النص من علامات الترقيم والمسافات
    String cleanText = title.trim();
    if (cleanText.isEmpty) return 'ا';

    // استخراج الحرف الأول
    String firstChar = cleanText[0];

    // التعامل مع الهمزات المختلفة
    if (firstChar == 'أ' || firstChar == 'إ' || firstChar == 'آ') {
      return 'ا';
    }

    // التعامل مع الهمزة المستقلة
    if (firstChar == 'ء') {
      return 'ء';
    }

    return firstChar;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'id': id,
      'title': title,
      'poetId': poetId,
      'categoryId': categoryId,
      'description': description,
      'meter': meter,
      'firstLetter': firstLetter,
      'isFavorite': isFavorite,
      'type': type == PoemType.mukhammas ? 'mukhammas' : 'regular',
    };

    // إضافة الأبيات المناسبة حسب نوع القصيدة
    if (type == PoemType.regular) {
      data['verses'] = verses.map((verse) => verse.toJson()).toList();
    } else if (type == PoemType.mukhammas && mukhammasVerses != null) {
      data['mukhammas_verses'] =
          mukhammasVerses!.map((verse) => verse.toJson()).toList();
    }

    return data;
  }

  // إنشاء نسخة جديدة من القصيدة مع تحديث حالة المفضلة
  Poem copyWith({
    bool? isFavorite,
    List<Verse>? verses,
    List<MukhammasVerse>? mukhammasVerses,
    PoemType? type,
  }) {
    return Poem(
      id: id,
      title: title,
      poetId: poetId,
      categoryId: categoryId,
      verses: verses ?? this.verses,
      mukhammasVerses: mukhammasVerses ?? this.mukhammasVerses,
      description: description,
      meter: meter,
      firstLetter: firstLetter,
      isFavorite: isFavorite ?? this.isFavorite,
      type: type ?? this.type,
    );
  }

  /// التحقق مما إذا كانت القصيدة مخمسة
  bool get isMukhammas => type == PoemType.mukhammas;
}
