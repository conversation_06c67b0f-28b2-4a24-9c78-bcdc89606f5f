import 'dart:math';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/manhal_rawi_provider.dart';
import '../../../../utils/theme/manhal_colors.dart';
import '../../../../utils/theme/islamic_patterns.dart';

/// خلفية إسلامية متحركة للتطبيق
class AnimatedIslamicBackground extends StatefulWidget {
  final Widget child;
  final bool showPatterns;
  final bool showCrescents;
  final double patternsOpacity;

  const AnimatedIslamicBackground({
    super.key,
    required this.child,
    this.showPatterns = true,
    this.showCrescents = true,
    this.patternsOpacity = 0.15,
  });

  @override
  State<AnimatedIslamicBackground> createState() =>
      _AnimatedIslamicBackgroundState();
}

class _AnimatedIslamicBackgroundState extends State<AnimatedIslamicBackground>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 20),
    );

    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );

    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<ManhalRawiProvider>();
    final isDarkMode = provider.isDarkMode;

    return Stack(
      children: [
        // الخلفية المتدرجة
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: isDarkMode
                  ? [
                      ManhalColors.blue900,
                      ManhalColors.blue800,
                    ]
                  : [
                      Colors.white,
                      ManhalColors.gold100.withValues(alpha: 0.3),
                    ],
            ),
          ),
        ),

        // الزخارف الإسلامية المتحركة
        if (widget.showPatterns)
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return Opacity(
                opacity: widget.patternsOpacity,
                child: CustomPaint(
                  painter: _AnimatedPatternPainter(
                    isDark: isDarkMode,
                    animation: _animation.value,
                  ),
                  size: Size.infinite,
                ),
              );
            },
          ),

        // الهلال الإسلامي في الزاوية العلوية
        if (widget.showCrescents)
          Positioned(
            top: 20,
            right: 20,
            child: Opacity(
              opacity: 0.2,
              child: IslamicPatterns.getCrescentDecoration(
                isDark: isDarkMode,
                size: 60,
                opacity: 0.5,
              ),
            ),
          ),

        // الهلال الإسلامي في الزاوية السفلية
        if (widget.showCrescents)
          Positioned(
            bottom: 20,
            left: 20,
            child: Opacity(
              opacity: 0.2,
              child: IslamicPatterns.getCrescentDecoration(
                isDark: isDarkMode,
                size: 40,
                opacity: 0.5,
              ),
            ),
          ),

        // المحتوى الرئيسي
        widget.child,
      ],
    );
  }
}

/// رسام الزخارف الإسلامية المتحركة
class _AnimatedPatternPainter extends CustomPainter {
  final bool isDark;
  final double animation;

  _AnimatedPatternPainter({
    required this.isDark,
    required this.animation,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final baseColor = isDark ? ManhalColors.gold500 : ManhalColors.primary;
    final paint = Paint()
      ..color = baseColor.withValues(alpha: 0.1)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    final width = size.width;
    final height = size.height;

    // رسم الزخارف في الزوايا
    _drawCornerPattern(canvas, paint, Offset(0, 0), size, animation);
    _drawCornerPattern(canvas, paint, Offset(width, 0), size, animation);
    _drawCornerPattern(canvas, paint, Offset(0, height), size, animation);
    _drawCornerPattern(canvas, paint, Offset(width, height), size, animation);

    // رسم الزخارف في المنتصف
    _drawCenterPattern(
        canvas, paint, Offset(width / 2, height / 2), size, animation);
  }

  void _drawCornerPattern(
      Canvas canvas, Paint paint, Offset corner, Size size, double animation) {
    final patternSize =
        size.width < size.height ? size.width / 3 : size.height / 3;

    final path = Path();
    final isTopLeft = corner.dx == 0 && corner.dy == 0;
    final isTopRight = corner.dx == size.width && corner.dy == 0;
    final isBottomLeft = corner.dx == 0 && corner.dy == size.height;
    // La esquina inferior derecha se maneja implícitamente con la condición else

    // تحديد نقطة البداية حسب الزاوية
    Offset start;
    if (isTopLeft) {
      start = Offset(corner.dx + 20, corner.dy + 20);
    } else if (isTopRight) {
      start = Offset(corner.dx - 20, corner.dy + 20);
    } else if (isBottomLeft) {
      start = Offset(corner.dx + 20, corner.dy - 20);
    } else {
      start = Offset(corner.dx - 20, corner.dy - 20);
    }

    // رسم الزخارف الدائرية
    for (int i = 0; i < 3; i++) {
      final radius = patternSize * 0.3 * (i + 1) * (0.8 + animation * 0.2);

      if (isTopLeft) {
        canvas.drawArc(
          Rect.fromCircle(center: start, radius: radius),
          3.14159 * 1.5,
          3.14159 / 2,
          false,
          paint,
        );
      } else if (isTopRight) {
        canvas.drawArc(
          Rect.fromCircle(center: start, radius: radius),
          3.14159,
          3.14159 / 2,
          false,
          paint,
        );
      } else if (isBottomLeft) {
        canvas.drawArc(
          Rect.fromCircle(center: start, radius: radius),
          0,
          3.14159 / 2,
          false,
          paint,
        );
      } else {
        canvas.drawArc(
          Rect.fromCircle(center: start, radius: radius),
          3.14159 / 2,
          3.14159 / 2,
          false,
          paint,
        );
      }
    }

    // رسم الخطوط الزخرفية
    if (isTopLeft) {
      path.moveTo(start.dx, start.dy);
      path.lineTo(start.dx + patternSize * 0.5, start.dy);
      path.moveTo(start.dx, start.dy);
      path.lineTo(start.dx, start.dy + patternSize * 0.5);
    } else if (isTopRight) {
      path.moveTo(start.dx, start.dy);
      path.lineTo(start.dx - patternSize * 0.5, start.dy);
      path.moveTo(start.dx, start.dy);
      path.lineTo(start.dx, start.dy + patternSize * 0.5);
    } else if (isBottomLeft) {
      path.moveTo(start.dx, start.dy);
      path.lineTo(start.dx + patternSize * 0.5, start.dy);
      path.moveTo(start.dx, start.dy);
      path.lineTo(start.dx, start.dy - patternSize * 0.5);
    } else {
      path.moveTo(start.dx, start.dy);
      path.lineTo(start.dx - patternSize * 0.5, start.dy);
      path.moveTo(start.dx, start.dy);
      path.lineTo(start.dx, start.dy - patternSize * 0.5);
    }

    canvas.drawPath(path, paint);
  }

  void _drawCenterPattern(
      Canvas canvas, Paint paint, Offset center, Size size, double animation) {
    final radius = size.width < size.height ? size.width / 6 : size.height / 6;
    final adjustedRadius = radius * (0.8 + animation * 0.2);

    // رسم النجمة الإسلامية الثمانية
    final path = Path();
    final points = <Offset>[];
    final sides = 8;

    for (int i = 0; i < sides; i++) {
      final angle = (i * 2 * 3.14159) / sides + animation * 0.2;
      points.add(Offset(
        center.dx + adjustedRadius * cos(angle),
        center.dy + adjustedRadius * sin(angle),
      ));
    }

    // رسم النجمة الأساسية
    path.moveTo(points[0].dx, points[0].dy);
    for (int i = 1; i < points.length; i++) {
      path.lineTo(points[i].dx, points[i].dy);
    }
    path.close();

    // رسم النجمة الداخلية
    final innerPoints = <Offset>[];
    final innerRadius = adjustedRadius * 0.6;

    for (int i = 0; i < sides; i++) {
      final angle =
          (i * 2 * 3.14159) / sides + 3.14159 / sides + animation * 0.2;
      innerPoints.add(Offset(
        center.dx + innerRadius * cos(angle),
        center.dy + innerRadius * sin(angle),
      ));
    }

    path.moveTo(innerPoints[0].dx, innerPoints[0].dy);
    for (int i = 1; i < innerPoints.length; i++) {
      path.lineTo(innerPoints[i].dx, innerPoints[i].dy);
    }
    path.close();

    // ربط النقاط
    for (int i = 0; i < sides; i++) {
      path.moveTo(points[i].dx, points[i].dy);
      path.lineTo(innerPoints[i].dx, innerPoints[i].dy);
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}
