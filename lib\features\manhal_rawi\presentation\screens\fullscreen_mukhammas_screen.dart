import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'dart:async';

import '../../data/models/mukhammas_verse.dart';
import '../../data/models/poem.dart';
import '../providers/manhal_rawi_provider.dart';
import '../widgets/classic_mukhammas_display.dart';
import '../widgets/luxury_mukhammas_display.dart';
import '../widgets/mukhammas_verse_display.dart';
import '../../../../utils/theme/manhal_colors.dart';
import '../../../../utils/theme/islamic_typography.dart';
import '../../../../utils/helpers/responsive_helper.dart';

class FullscreenMukhammasScreen extends StatefulWidget {
  final Poem poem;
  final int initialPage;

  const FullscreenMukhammasScreen({
    super.key,
    required this.poem,
    this.initialPage = 0,
  });

  @override
  State<FullscreenMukhammasScreen> createState() =>
      _FullscreenMukhammasScreenState();
}

class _FullscreenMukhammasScreenState extends State<FullscreenMukhammasScreen>
    with SingleTickerProviderStateMixin {
  late int _currentPage;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  bool _showControls = true;
  Timer? _controlsTimer;

  // متغير للتحكم في ما إذا كان المستخدم يتفاعل مع النماذج أو الحوارات
  bool _isInteractingWithModels = false;

  // متغير للتحكم في ما إذا كان المستخدم يتفاعل مع أي من نماذج العرض
  bool _isInteractingWithDisplayModels = false;

  // وقت ظهور عناصر التحكم بالثواني
  final int _controlsVisibilityDuration = 20; // زيادة المدة من 8 إلى 20 ثانية

  @override
  void initState() {
    super.initState();
    _currentPage = widget.initialPage;

    // تهيئة التحكم في الرسوم المتحركة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _fadeAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOut,
      ),
    );

    // إخفاء شريط الحالة وشريط التنقل
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);

    // تأكد من أن عناصر التحكم مرئية عند بدء الشاشة
    _showControls = true;

    // تأخير بدء مؤقت إخفاء عناصر التحكم لإعطاء المستخدم وقتًا كافيًا للتعرف على الواجهة
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        _startControlsTimer();
      }
    });
  }

  @override
  void dispose() {
    // إعادة شريط الحالة وشريط التنقل
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

    // إلغاء المؤقت
    _controlsTimer?.cancel();

    _animationController.dispose();
    super.dispose();
  }

  void _startControlsTimer() {
    // إلغاء المؤقت الحالي إذا كان موجودًا
    _controlsTimer?.cancel();

    // لا نبدأ مؤقتًا جديدًا إذا كان المستخدم يتفاعل مع النماذج أو نماذج العرض
    if (_isInteractingWithModels || _isInteractingWithDisplayModels) {
      // تأكد من أن عناصر التحكم مرئية
      if (!_showControls) {
        setState(() {
          _showControls = true;
        });
        _animationController.reverse();
      }
      return;
    }

    // بدء مؤقت جديد بالمدة المحددة
    _controlsTimer = Timer(Duration(seconds: _controlsVisibilityDuration), () {
      if (mounted) {
        // تحقق مرة أخرى قبل إخفاء عناصر التحكم
        if (!_isInteractingWithModels && !_isInteractingWithDisplayModels) {
          setState(() {
            _showControls = false;
          });
          _animationController.forward();
        }
      }
    });
  }

  void _toggleControls() {
    // إلغاء المؤقت الحالي لمنع تداخل المؤقتات
    _controlsTimer?.cancel();

    // إذا كان المستخدم يتفاعل مع النماذج أو نماذج العرض، نتأكد من أن عناصر التحكم مرئية دائمًا
    if (_isInteractingWithModels || _isInteractingWithDisplayModels) {
      if (!_showControls) {
        setState(() {
          _showControls = true;
        });
        _animationController.reverse();
      }
      return;
    }

    // إذا كانت عناصر التحكم مخفية، أظهرها
    if (!_showControls) {
      setState(() {
        _showControls = true;
      });
      _animationController.reverse();
      _startControlsTimer();
    }
    // إذا كانت عناصر التحكم ظاهرة، أخفها
    else {
      setState(() {
        _showControls = false;
      });
      _animationController.forward();
    }
  }

  void _onPageChanged(int page) {
    // إلغاء المؤقت الحالي لمنع تداخل المؤقتات
    _controlsTimer?.cancel();

    setState(() {
      _currentPage = page;
      _showControls = true;
    });

    _animationController.reverse();

    // تأخير بدء مؤقت إخفاء عناصر التحكم لإعطاء المستخدم وقتًا للتفاعل
    Future.delayed(const Duration(seconds: 1), () {
      // إذا لم يكن المستخدم يتفاعل مع النماذج أو نماذج العرض، نبدأ المؤقت
      if (mounted &&
          !_isInteractingWithModels &&
          !_isInteractingWithDisplayModels) {
        _startControlsTimer();
      }
    });
  }

  void _exitFullscreen() {
    Navigator.of(context).pop(_currentPage);
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = context.watch<ManhalRawiProvider>().isDarkMode;
    final mukhammasDisplayType =
        context.watch<ManhalRawiProvider>().mukhammasDisplayType;
    final verses = widget.poem.mukhammasVerses ?? [];

    return Directionality(
      textDirection: TextDirection.rtl, // تأكيد اتجاه RTL للشاشة بأكملها
      child: Scaffold(
        backgroundColor: isDarkMode ? Colors.black : Colors.white,
        body: GestureDetector(
          onTap: _toggleControls,
          child: Stack(
            children: [
              // عرض القصيدة المخمسة بملء الشاشة
              Center(
                child: _buildMukhammasDisplay(
                  mukhammasDisplayType,
                  verses,
                  isDarkMode,
                ),
              ),

              // عناصر التحكم
              AnimatedBuilder(
                animation: _fadeAnimation,
                builder: (context, child) {
                  return Opacity(
                    opacity: 1.0 - _fadeAnimation.value,
                    child: child,
                  );
                },
                child: _buildControls(isDarkMode),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMukhammasDisplay(
    MukhammasDisplayType displayType,
    List<MukhammasVerse> verses,
    bool isDarkMode,
  ) {
    // دالة للتعامل مع التفاعل مع نماذج العرض
    void onDisplayModelInteraction(bool isInteracting) {
      if (!mounted) return;

      setState(() {
        _isInteractingWithDisplayModels = isInteracting;

        // إذا كان المستخدم يتفاعل، تأكد من أن عناصر التحكم مرئية
        if (isInteracting) {
          _showControls = true;
          _animationController.reverse();

          // إلغاء المؤقت الحالي لمنع إخفاء عناصر التحكم أثناء التفاعل
          _controlsTimer?.cancel();
        } else {
          // إذا توقف التفاعل، ابدأ مؤقتًا جديدًا لإخفاء عناصر التحكم بعد فترة
          // تأخير بدء المؤقت لإعطاء المستخدم وقتًا للتفاعل مرة أخرى
          Future.delayed(const Duration(milliseconds: 500), () {
            if (mounted &&
                !_isInteractingWithModels &&
                !_isInteractingWithDisplayModels) {
              _startControlsTimer();
            }
          });
        }
      });
    }

    switch (displayType) {
      case MukhammasDisplayType.luxury:
        return LuxuryMukhammasDisplay(
          verses: verses,
          selectedIndex: _currentPage,
          onVerseSelected: (index) {
            _onPageChanged(index);
          },
          isFullScreen: true,
          onFullScreenToggle: _exitFullscreen,
          onInteractionStateChanged: onDisplayModelInteraction,
        );

      case MukhammasDisplayType.classic:
        return ClassicMukhammasDisplay(
          verses: verses,
          initialPage: _currentPage,
          onPageChanged: _onPageChanged,
          showPageIndicator: _showControls,
          isFullScreen: true,
          onToggleFullScreen: _exitFullscreen,
          onInteractionStateChanged: onDisplayModelInteraction,
        );

      case MukhammasDisplayType.standard:
        return MukhammasVerseDisplay(
          verses: verses,
          selectedIndex: _currentPage,
          onVerseSelected: (index) {
            _onPageChanged(index);
          },
          isFullScreen: true,
          onFullScreenToggle: _exitFullscreen,
          onInteractionStateChanged: onDisplayModelInteraction,
        );
    }
  }

  Widget _buildControls(bool isDarkMode) {
    if (!_showControls) {
      return const SizedBox.shrink();
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        // تعديل التصميم بناءً على حجم الشاشة
        final double screenWidth = constraints.maxWidth;
        final bool isTablet = screenWidth > 600;

        // تحديد أحجام العناصر باستخدام ResponsiveHelper
        final double iconSize = ResponsiveHelper.getResponsiveIconSize(
          context,
          defaultSize: isTablet ? 28.0 : 24.0,
        );

        final double titleFontSize = ResponsiveHelper.getResponsiveFontSize(
          context,
          fontSize: isTablet ? 20.0 : 16.0,
        );

        final double infoFontSize = ResponsiveHelper.getResponsiveFontSize(
          context,
          fontSize: isTablet ? 16.0 : 14.0,
        );

        // تحديد الهوامش باستخدام ResponsiveHelper
        final EdgeInsets topPadding = EdgeInsets.symmetric(
          horizontal: ResponsiveHelper.getResponsiveSpacing(
            context,
            defaultValue: isTablet ? 24.0 : 16.0,
          ),
          vertical: ResponsiveHelper.getResponsiveSpacing(
            context,
            defaultValue: isTablet ? 12.0 : 8.0,
          ),
        );

        final EdgeInsets bottomPadding = EdgeInsets.symmetric(
          horizontal: ResponsiveHelper.getResponsiveSpacing(
            context,
            defaultValue: isTablet ? 24.0 : 16.0,
          ),
          vertical: ResponsiveHelper.getResponsiveSpacing(
            context,
            defaultValue: isTablet ? 12.0 : 8.0,
          ),
        );

        // تحديد حجم الظل باستخدام ResponsiveHelper
        final double shadowBlur = ResponsiveHelper.getResponsiveShadow(
          context,
          defaultBlurRadius: isTablet ? 6.0 : 4.0,
        )[0];

        return SafeArea(
          child: Column(
            children: [
              // شريط العنوان
              Container(
                width: screenWidth,
                padding: topPadding,
                decoration: BoxDecoration(
                  color: isDarkMode
                      ? Colors.black.withValues(alpha: 0.7)
                      : Colors.white.withValues(alpha: 0.7),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: shadowBlur,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // زر الإغلاق
                    Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: isDarkMode
                            ? Colors.black.withValues(alpha: 0.3)
                            : Colors.white.withValues(alpha: 0.3),
                      ),
                      child: IconButton(
                        icon: Icon(
                          Icons.close,
                          color: isDarkMode
                              ? ManhalColors.gold500
                              : ManhalColors.primary,
                          size: iconSize,
                        ),
                        onPressed: _exitFullscreen,
                        padding: EdgeInsets.all(
                          ResponsiveHelper.getResponsiveSpacing(
                            context,
                            defaultValue: isTablet ? 8.0 : 4.0,
                          ),
                        ),
                        tooltip: 'إغلاق',
                      ),
                    ),

                    // عنوان القصيدة
                    Expanded(
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: ResponsiveHelper.getResponsiveSpacing(
                            context,
                            defaultValue: isTablet ? 16.0 : 8.0,
                          ),
                        ),
                        child: Text(
                          widget.poem.title,
                          style: IslamicTypography.luxuryTitle(
                            isDark: isDarkMode,
                            fontSize: titleFontSize,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),

                    // زر الإعدادات
                    Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: isDarkMode
                            ? Colors.black.withValues(alpha: 0.3)
                            : Colors.white.withValues(alpha: 0.3),
                      ),
                      child: IconButton(
                        icon: Icon(
                          Icons.settings,
                          color: isDarkMode
                              ? ManhalColors.gold500
                              : ManhalColors.primary,
                          size: iconSize,
                        ),
                        onPressed: () {
                          _showDisplayOptionsDialog(context);
                        },
                        padding: EdgeInsets.all(
                          ResponsiveHelper.getResponsiveSpacing(
                            context,
                            defaultValue: isTablet ? 8.0 : 4.0,
                          ),
                        ),
                        tooltip: 'الإعدادات',
                      ),
                    ),
                  ],
                ),
              ),

              const Spacer(),

              // شريط التنقل السفلي
              Container(
                width: screenWidth,
                padding: bottomPadding,
                decoration: BoxDecoration(
                  color: isDarkMode
                      ? Colors.black.withValues(alpha: 0.7)
                      : Colors.white.withValues(alpha: 0.7),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: shadowBlur,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // معلومات الصفحة
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: ResponsiveHelper.getResponsiveSpacing(
                          context,
                          defaultValue: isTablet ? 16.0 : 12.0,
                        ),
                        vertical: ResponsiveHelper.getResponsiveSpacing(
                          context,
                          defaultValue: isTablet ? 8.0 : 4.0,
                        ),
                      ),
                      decoration: BoxDecoration(
                        color: isDarkMode
                            ? Colors.black.withValues(alpha: 0.3)
                            : Colors.white.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(
                          ResponsiveHelper.getResponsiveBorderRadius(
                            context,
                            defaultValue: isTablet ? 20.0 : 16.0,
                          ),
                        ),
                        border: Border.all(
                          color: isDarkMode
                              ? ManhalColors.gold500.withValues(alpha: 0.3)
                              : ManhalColors.gold300,
                          width: ResponsiveHelper.getResponsiveBorderWidth(
                            context,
                            defaultValue: isTablet ? 1.5 : 1.0,
                          ),
                        ),
                      ),
                      child: Text(
                        'صفحة ${_currentPage + 1} من ${((widget.poem.mukhammasVerses?.length ?? 0) / context.read<ManhalRawiProvider>().verseDisplaySettings.versesPerPage).ceil()}',
                        style: IslamicTypography.luxuryCaption(
                          isDark: isDarkMode,
                          fontSize: infoFontSize,
                        ),
                      ),
                    ),

                    // أزرار التنقل (يمكن إضافتها هنا إذا كنت ترغب في ذلك)
                    if (isTablet) ...[
                      SizedBox(
                        width: ResponsiveHelper.getResponsiveSpacing(
                          context,
                          defaultValue: 16.0,
                        ),
                      ),
                      _buildNavigationButtons(
                          isDarkMode, screenWidth, isTablet),
                    ],
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // بناء أزرار التنقل
  Widget _buildNavigationButtons(
      bool isDarkMode, double screenWidth, bool isTablet) {
    final verses = widget.poem.mukhammasVerses ?? [];
    if (verses.isEmpty) return const SizedBox.shrink();

    final double iconSize = ResponsiveHelper.getResponsiveIconSize(
      context,
      defaultSize: isTablet ? 24.0 : 20.0,
    );

    return Row(
      children: [
        // زر البيت السابق
        Container(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isDarkMode
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.white.withValues(alpha: 0.3),
          ),
          child: IconButton(
            icon: Icon(
              Icons
                  .arrow_forward_ios, // تغيير من arrow_back_ios إلى arrow_forward_ios للتوافق مع اتجاه RTL
              color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
              size: iconSize,
            ),
            onPressed: _currentPage > 0
                ? () {
                    _onPageChanged(_currentPage - 1);
                  }
                : null,
            tooltip: 'البيت السابق',
            padding: EdgeInsets.all(
              ResponsiveHelper.getResponsiveSpacing(
                context,
                defaultValue: isTablet ? 8.0 : 4.0,
              ),
            ),
          ),
        ),

        SizedBox(
          width: ResponsiveHelper.getResponsiveSpacing(
            context,
            defaultValue: isTablet ? 16.0 : 8.0,
          ),
        ),

        // زر البيت التالي
        Container(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isDarkMode
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.white.withValues(alpha: 0.3),
          ),
          child: IconButton(
            icon: Icon(
              Icons
                  .arrow_back_ios, // تغيير من arrow_forward_ios إلى arrow_back_ios للتوافق مع اتجاه RTL
              color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
              size: iconSize,
            ),
            onPressed: _currentPage < verses.length - 1
                ? () {
                    _onPageChanged(_currentPage + 1);
                  }
                : null,
            tooltip: 'البيت التالي',
            padding: EdgeInsets.all(
              ResponsiveHelper.getResponsiveSpacing(
                context,
                defaultValue: isTablet ? 8.0 : 4.0,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _showDisplayOptionsDialog(BuildContext context) {
    // إيقاف المؤقت عند فتح نافذة الإعدادات
    _controlsTimer?.cancel();

    // تعيين حالة التفاعل مع النماذج إلى صحيح
    setState(() {
      _isInteractingWithModels = true;
    });

    final isDarkMode = context.read<ManhalRawiProvider>().isDarkMode;
    final provider = context.read<ManhalRawiProvider>();

    // الحصول على حجم الشاشة
    final double screenWidth = MediaQuery.of(context).size.width;
    final bool isTablet = screenWidth > 600;

    // تعديل الأحجام باستخدام ResponsiveHelper
    final double titleFontSize = ResponsiveHelper.getResponsiveFontSize(
      context,
      fontSize: isTablet ? 22.0 : 18.0,
    );

    final double optionTitleFontSize = ResponsiveHelper.getResponsiveFontSize(
      context,
      fontSize: isTablet ? 18.0 : 16.0,
    );

    final double optionSubtitleFontSize =
        ResponsiveHelper.getResponsiveFontSize(
      context,
      fontSize: isTablet ? 16.0 : 14.0,
    );

    final double iconSize = ResponsiveHelper.getResponsiveIconSize(
      context,
      defaultSize: isTablet ? 28.0 : 24.0,
    );

    final double buttonFontSize = ResponsiveHelper.getResponsiveFontSize(
      context,
      fontSize: isTablet ? 16.0 : 14.0,
    );

    final double dialogWidth = isTablet ? 400.0 : 300.0;

    final double borderRadius = ResponsiveHelper.getResponsiveBorderRadius(
      context,
      defaultValue: isTablet ? 20.0 : 16.0,
    );

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        child: Container(
          width: dialogWidth,
          padding: EdgeInsets.all(
            ResponsiveHelper.getResponsiveSpacing(
              context,
              defaultValue: isTablet ? 24.0 : 16.0,
            ),
          ),
          decoration: BoxDecoration(
            color: isDarkMode ? ManhalColors.blue900 : Colors.white,
            borderRadius: BorderRadius.circular(borderRadius),
            border: Border.all(
              color: isDarkMode
                  ? ManhalColors.gold500.withValues(alpha: 0.3)
                  : ManhalColors.gold300,
              width: ResponsiveHelper.getResponsiveBorderWidth(
                context,
                defaultValue: isTablet ? 1.5 : 1.0,
              ),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // عنوان الحوار
              Padding(
                padding: EdgeInsets.only(
                  bottom: ResponsiveHelper.getResponsiveSpacing(
                    context,
                    defaultValue: isTablet ? 16.0 : 12.0,
                  ),
                ),
                child: Text(
                  'خيارات العرض',
                  style: IslamicTypography.luxuryTitle(
                    isDark: isDarkMode,
                    fontSize: titleFontSize,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              // قائمة الخيارات
              SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // نمط العرض
                    _buildOptionTile(
                      context: context,
                      title: 'نمط العرض',
                      subtitle:
                          _getDisplayTypeName(provider.mukhammasDisplayType),
                      icon: Icons.style,
                      isDarkMode: isDarkMode,
                      titleFontSize: optionTitleFontSize,
                      subtitleFontSize: optionSubtitleFontSize,
                      iconSize: iconSize,
                      onTap: () {
                        Navigator.of(context).pop();
                        _showDisplayTypeDialog(context);
                      },
                    ),

                    SizedBox(
                      height: ResponsiveHelper.getResponsiveSpacing(
                        context,
                        defaultValue: isTablet ? 8.0 : 4.0,
                      ),
                    ),

                    // حجم الخط
                    _buildOptionTile(
                      context: context,
                      title: 'حجم الخط',
                      subtitle:
                          '${provider.verseDisplaySettings.fontSize.toInt()}',
                      icon: Icons.text_fields,
                      isDarkMode: isDarkMode,
                      titleFontSize: optionTitleFontSize,
                      subtitleFontSize: optionSubtitleFontSize,
                      iconSize: iconSize,
                      onTap: () {
                        Navigator.of(context).pop();
                        _showFontSizeDialog(context);
                      },
                    ),

                    SizedBox(
                      height: ResponsiveHelper.getResponsiveSpacing(
                        context,
                        defaultValue: isTablet ? 8.0 : 4.0,
                      ),
                    ),

                    // عدد الأبيات في الصفحة
                    _buildOptionTile(
                      context: context,
                      title: 'عدد الأبيات في الصفحة',
                      subtitle:
                          '${provider.verseDisplaySettings.versesPerPage}',
                      icon: Icons.view_agenda,
                      isDarkMode: isDarkMode,
                      titleFontSize: optionTitleFontSize,
                      subtitleFontSize: optionSubtitleFontSize,
                      iconSize: iconSize,
                      onTap: () {
                        Navigator.of(context).pop();
                        _showVersesPerPageDialog(context);
                      },
                    ),
                  ],
                ),
              ),

              // زر الإغلاق
              Padding(
                padding: EdgeInsets.only(
                  top: ResponsiveHelper.getResponsiveSpacing(
                    context,
                    defaultValue: isTablet ? 16.0 : 12.0,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();

                        // تعيين حالة التفاعل مع النماذج إلى خاطئ
                        setState(() {
                          _isInteractingWithModels = false;
                        });

                        // إعادة تشغيل المؤقت
                        _startControlsTimer();
                      },
                      style: TextButton.styleFrom(
                        padding: EdgeInsets.symmetric(
                          horizontal: ResponsiveHelper.getResponsiveSpacing(
                            context,
                            defaultValue: isTablet ? 24.0 : 16.0,
                          ),
                          vertical: ResponsiveHelper.getResponsiveSpacing(
                            context,
                            defaultValue: isTablet ? 12.0 : 8.0,
                          ),
                        ),
                      ),
                      child: Text(
                        'إغلاق',
                        style: TextStyle(
                          fontFamily: 'Amiri',
                          fontSize: buttonFontSize,
                          color: isDarkMode
                              ? ManhalColors.gold300
                              : ManhalColors.primary,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // بناء خيار في قائمة الخيارات
  Widget _buildOptionTile({
    required BuildContext context,
    required String title,
    required String subtitle,
    required IconData icon,
    required bool isDarkMode,
    required double titleFontSize,
    required double subtitleFontSize,
    required double iconSize,
    required VoidCallback onTap,
  }) {
    final double screenWidth = MediaQuery.of(context).size.width;
    final bool isTablet = screenWidth > 600;

    final double borderRadius = ResponsiveHelper.getResponsiveBorderRadius(
      context,
      defaultValue: isTablet ? 16.0 : 12.0,
    );

    return Container(
      decoration: BoxDecoration(
        color: isDarkMode
            ? Colors.black.withValues(alpha: 0.2)
            : Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: ListTile(
        title: Text(
          title,
          style: IslamicTypography.luxuryBody(
            isDark: isDarkMode,
            fontSize: titleFontSize,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: IslamicTypography.luxuryCaption(
            isDark: isDarkMode,
            fontSize: subtitleFontSize,
          ),
        ),
        trailing: Icon(
          icon,
          color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
          size: iconSize,
        ),
        onTap: onTap,
        contentPadding: EdgeInsets.symmetric(
          horizontal: ResponsiveHelper.getResponsiveSpacing(
            context,
            defaultValue: isTablet ? 16.0 : 12.0,
          ),
          vertical: ResponsiveHelper.getResponsiveSpacing(
            context,
            defaultValue: isTablet ? 8.0 : 4.0,
          ),
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        dense: !isTablet,
      ),
    );
  }

  String _getDisplayTypeName(MukhammasDisplayType type) {
    switch (type) {
      case MukhammasDisplayType.luxury:
        return 'النمط الفاخر';
      case MukhammasDisplayType.classic:
        return 'النمط الكلاسيكي';
      case MukhammasDisplayType.standard:
        return 'النمط القياسي';
    }
  }

  void _showDisplayTypeDialog(BuildContext context) {
    // إيقاف المؤقت عند فتح نافذة الإعدادات
    _controlsTimer?.cancel();

    // تعيين حالة التفاعل مع النماذج إلى صحيح
    setState(() {
      _isInteractingWithModels = true;
    });

    final isDarkMode = context.read<ManhalRawiProvider>().isDarkMode;
    final provider = context.read<ManhalRawiProvider>();

    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: isDarkMode ? ManhalColors.blue900 : Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(
            color: isDarkMode
                ? ManhalColors.gold500.withValues(alpha: 0.3)
                : ManhalColors.gold300,
            width: 1,
          ),
        ),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // عنوان الحوار
                Text(
                  'اختر نمط العرض',
                  style: IslamicTypography.luxuryTitle(
                    isDark: isDarkMode,
                    fontSize: 18,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 16),

                // النمط القياسي
                _buildDisplayTypeOption(
                  context,
                  isDarkMode,
                  title: 'النمط القياسي',
                  subtitle: 'عرض الأبيات بالطريقة القياسية',
                  value: MukhammasDisplayType.standard,
                  groupValue: provider.mukhammasDisplayType,
                  onChanged: (value) {
                    provider.updateMukhammasDisplayType(value!);
                    Navigator.of(context).pop();

                    // تعيين حالة التفاعل مع النماذج إلى خاطئ
                    setState(() {
                      _isInteractingWithModels = false;
                    });

                    // إعادة تشغيل المؤقت بعد إغلاق النافذة
                    _startControlsTimer();
                  },
                ),

                // النمط الفاخر
                _buildDisplayTypeOption(
                  context,
                  isDarkMode,
                  title: 'النمط الفاخر',
                  subtitle: 'عرض الأبيات بطريقة فاخرة ومتطورة',
                  value: MukhammasDisplayType.luxury,
                  groupValue: provider.mukhammasDisplayType,
                  onChanged: (value) {
                    provider.updateMukhammasDisplayType(value!);
                    Navigator.of(context).pop();

                    // تعيين حالة التفاعل مع النماذج إلى خاطئ
                    setState(() {
                      _isInteractingWithModels = false;
                    });

                    // إعادة تشغيل المؤقت بعد إغلاق النافذة
                    _startControlsTimer();
                  },
                ),

                // النمط الكلاسيكي
                _buildDisplayTypeOption(
                  context,
                  isDarkMode,
                  title: 'النمط الكلاسيكي',
                  subtitle: 'عرض الأبيات بطريقة كلاسيكية فاخرة',
                  value: MukhammasDisplayType.classic,
                  groupValue: provider.mukhammasDisplayType,
                  onChanged: (value) {
                    provider.updateMukhammasDisplayType(value!);
                    Navigator.of(context).pop();

                    // تعيين حالة التفاعل مع النماذج إلى خاطئ
                    setState(() {
                      _isInteractingWithModels = false;
                    });

                    // إعادة تشغيل المؤقت بعد إغلاق النافذة
                    _startControlsTimer();
                  },
                ),

                const SizedBox(height: 8),

                // زر الإلغاء
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();

                    // تعيين حالة التفاعل مع النماذج إلى خاطئ
                    setState(() {
                      _isInteractingWithModels = false;
                    });

                    // إعادة تشغيل المؤقت بعد إغلاق النافذة
                    _startControlsTimer();
                  },
                  child: Text(
                    'إلغاء',
                    style: TextStyle(
                      fontFamily: 'Amiri',
                      fontSize: 14,
                      color: isDarkMode
                          ? ManhalColors.gold300
                          : ManhalColors.primary,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء خيار نمط العرض
  Widget _buildDisplayTypeOption<T>(
    BuildContext context,
    bool isDarkMode, {
    required String title,
    required String subtitle,
    required T value,
    required T groupValue,
    required ValueChanged<T?> onChanged,
  }) {
    return InkWell(
      onTap: () => onChanged(value),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 4),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: value == groupValue
              ? (isDarkMode
                  ? ManhalColors.gold500.withValues(alpha: 0.2)
                  : ManhalColors.primary.withValues(alpha: 0.1))
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: value == groupValue
                ? (isDarkMode ? ManhalColors.gold500 : ManhalColors.primary)
                : Colors.transparent,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Radio<T>(
              value: value,
              groupValue: groupValue,
              activeColor:
                  isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
              onChanged: onChanged,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: IslamicTypography.luxuryBody(
                      isDark: isDarkMode,
                      fontSize: 16,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: IslamicTypography.luxuryCaption(
                      isDark: isDarkMode,
                      fontSize: 13,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showFontSizeDialog(BuildContext context) {
    // إيقاف المؤقت عند فتح نافذة الإعدادات
    _controlsTimer?.cancel();

    // تعيين حالة التفاعل مع النماذج إلى صحيح
    setState(() {
      _isInteractingWithModels = true;
    });

    final isDarkMode = context.read<ManhalRawiProvider>().isDarkMode;
    final provider = context.read<ManhalRawiProvider>();
    double currentFontSize = provider.verseDisplaySettings.fontSize;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return Dialog(
            backgroundColor: isDarkMode ? ManhalColors.blue900 : Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
              side: BorderSide(
                color: isDarkMode
                    ? ManhalColors.gold500.withValues(alpha: 0.3)
                    : ManhalColors.gold300,
                width: 1,
              ),
            ),
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // عنوان الحوار
                    Text(
                      'حجم الخط',
                      style: IslamicTypography.luxuryTitle(
                        isDark: isDarkMode,
                        fontSize: 18,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 16),

                    // وصف
                    Text(
                      'اختر حجم الخط المناسب',
                      style: IslamicTypography.luxuryBody(
                        isDark: isDarkMode,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 16),

                    // شريط التمرير
                    Slider(
                      value: currentFontSize,
                      min: 12,
                      max: 30,
                      divisions: 18,
                      label: currentFontSize.toInt().toString(),
                      activeColor: isDarkMode
                          ? ManhalColors.gold500
                          : ManhalColors.primary,
                      inactiveColor: isDarkMode
                          ? ManhalColors.gold500.withValues(alpha: 0.3)
                          : ManhalColors.primary.withValues(alpha: 0.3),
                      onChanged: (value) {
                        setState(() {
                          currentFontSize = value;
                        });
                      },
                    ),

                    const SizedBox(height: 16),

                    // مثال على حجم الخط
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: isDarkMode
                            ? ManhalColors.blue800.withValues(alpha: 0.5)
                            : ManhalColors.gold100.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: isDarkMode
                              ? ManhalColors.gold500.withValues(alpha: 0.3)
                              : ManhalColors.gold300,
                          width: 1,
                        ),
                      ),
                      child: Text(
                        'مثال على حجم الخط',
                        style: IslamicTypography.luxuryBody(
                          isDark: isDarkMode,
                          fontSize: currentFontSize,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),

                    const SizedBox(height: 16),

                    // أزرار الإجراءات
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        TextButton(
                          onPressed: () {
                            Navigator.of(context).pop();

                            // تعيين حالة التفاعل مع النماذج إلى خاطئ
                            setState(() {
                              _isInteractingWithModels = false;
                            });

                            // إعادة تشغيل المؤقت بعد إغلاق النافذة
                            _startControlsTimer();
                          },
                          child: Text(
                            'إلغاء',
                            style: TextStyle(
                              fontFamily: 'Amiri',
                              fontSize: 14,
                              color: isDarkMode
                                  ? ManhalColors.gold300
                                  : ManhalColors.primary,
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        ElevatedButton(
                          onPressed: () {
                            provider.updateFontSize(currentFontSize);
                            Navigator.of(context).pop();

                            // تعيين حالة التفاعل مع النماذج إلى خاطئ
                            setState(() {
                              _isInteractingWithModels = false;
                            });

                            // إعادة تشغيل المؤقت بعد إغلاق النافذة
                            _startControlsTimer();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: isDarkMode
                                ? ManhalColors.gold500
                                : ManhalColors.primary,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: Text(
                            'تطبيق',
                            style: IslamicTypography.luxuryBody(
                              isDark: false,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  void _showVersesPerPageDialog(BuildContext context) {
    // إيقاف المؤقت عند فتح نافذة الإعدادات
    _controlsTimer?.cancel();

    // تعيين حالة التفاعل مع النماذج إلى صحيح
    setState(() {
      _isInteractingWithModels = true;
    });

    final isDarkMode = context.read<ManhalRawiProvider>().isDarkMode;
    final provider = context.read<ManhalRawiProvider>();
    int currentVersesPerPage = provider.verseDisplaySettings.versesPerPage;

    // قائمة الخيارات المتاحة لعدد الأبيات - تم تحديثها لتتوافق مع صفحة الإعدادات
    final List<int> availableOptions = [5, 10, 15, 20, 25, 30];

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return Dialog(
            backgroundColor: isDarkMode ? ManhalColors.blue900 : Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
              side: BorderSide(
                color: isDarkMode
                    ? ManhalColors.gold500.withValues(alpha: 0.3)
                    : ManhalColors.gold300,
                width: 1,
              ),
            ),
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // عنوان الحوار
                    Text(
                      'عدد الأبيات في الصفحة',
                      style: IslamicTypography.luxuryTitle(
                        isDark: isDarkMode,
                        fontSize: 18,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 16),

                    // وصف
                    Text(
                      'اختر عدد الأبيات المناسب',
                      style: IslamicTypography.luxuryBody(
                        isDark: isDarkMode,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 16),

                    // عرض مثال للأبيات
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: isDarkMode
                            ? ManhalColors.blue800.withValues(alpha: 0.5)
                            : ManhalColors.gold100.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: isDarkMode
                              ? ManhalColors.gold500.withValues(alpha: 0.3)
                              : ManhalColors.gold300,
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.menu_book,
                            size: 24,
                            color: isDarkMode
                                ? ManhalColors.gold500
                                : ManhalColors.primary,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'عدد الأبيات: $currentVersesPerPage',
                            style: TextStyle(
                              fontFamily: provider.fontFamily,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: isDarkMode
                                  ? ManhalColors.gold500
                                  : ManhalColors.primary,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 16),

                    // خيارات عدد الأبيات
                    Wrap(
                      alignment: WrapAlignment.center,
                      spacing: 8,
                      runSpacing: 8,
                      children: availableOptions.map((option) {
                        final isSelected = option == currentVersesPerPage;
                        return InkWell(
                          onTap: () {
                            setState(() {
                              currentVersesPerPage = option;
                            });
                          },
                          child: Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? (isDarkMode
                                      ? ManhalColors.gold500
                                      : ManhalColors.primary)
                                  : (isDarkMode
                                      ? ManhalColors.blue800
                                          .withValues(alpha: 0.5)
                                      : Colors.white),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: isDarkMode
                                    ? ManhalColors.gold500
                                    : ManhalColors.primary,
                                width: 1,
                              ),
                            ),
                            child: Center(
                              child: Text(
                                option.toString(),
                                style: TextStyle(
                                  fontFamily: 'Amiri',
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: isSelected
                                      ? (isDarkMode
                                          ? Colors.black
                                          : Colors.white)
                                      : (isDarkMode
                                          ? ManhalColors.gold500
                                          : ManhalColors.primary),
                                ),
                              ),
                            ),
                          ),
                        );
                      }).toList(),
                    ),

                    const SizedBox(height: 16),

                    // أزرار الإجراءات
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        TextButton(
                          onPressed: () {
                            Navigator.of(context).pop();

                            // تعيين حالة التفاعل مع النماذج إلى خاطئ
                            setState(() {
                              _isInteractingWithModels = false;
                            });

                            // إعادة تشغيل المؤقت بعد إغلاق النافذة
                            _startControlsTimer();
                          },
                          child: Text(
                            'إلغاء',
                            style: TextStyle(
                              fontFamily: 'Amiri',
                              fontSize: 14,
                              color: isDarkMode
                                  ? ManhalColors.gold300
                                  : ManhalColors.primary,
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        ElevatedButton(
                          onPressed: () {
                            provider.updateVersesPerPage(currentVersesPerPage);
                            Navigator.of(context).pop();

                            // تعيين حالة التفاعل مع النماذج إلى خاطئ
                            setState(() {
                              _isInteractingWithModels = false;
                            });

                            // إعادة تشغيل المؤقت بعد إغلاق النافذة
                            _startControlsTimer();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: isDarkMode
                                ? ManhalColors.gold500
                                : ManhalColors.primary,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: Text(
                            'تطبيق',
                            style: IslamicTypography.luxuryBody(
                              isDark: false,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
