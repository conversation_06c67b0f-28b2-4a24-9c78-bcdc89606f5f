import 'package:flutter/material.dart';
import '../../../../../utils/theme/manhal_colors.dart';

/// مكون للخط العربي الفني
class CalligraphyArt extends StatelessWidget {
  final String text;
  final double size;
  final Color? color;
  final bool isDark;
  final CalligraphyStyle style;
  final double opacity;

  const CalligraphyArt({
    super.key,
    required this.text,
    this.size = 100,
    this.color,
    this.isDark = false,
    this.style = CalligraphyStyle.diwani,
    this.opacity = 1.0,
  });

  @override
  Widget build(BuildContext context) {
    final artColor = color ??
        (isDark
            ? ManhalColors.gold500.withValues(alpha: opacity)
            : ManhalColors.primary.withValues(alpha: opacity));

    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(size / 10),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // الإطار الزخرفي
          if (style == CalligraphyStyle.framed ||
              style == CalligraphyStyle.medallion)
            _buildFrame(artColor),

          // النص الخطي
          Padding(
            padding: EdgeInsets.all(size / 10),
            child: FittedBox(
              fit: BoxFit.contain,
              child: Text(
                text,
                style: _getTextStyle(artColor),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// إنشاء الإطار الزخرفي
  Widget _buildFrame(Color color) {
    if (style == CalligraphyStyle.medallion) {
      return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: color,
            width: size / 50,
          ),
        ),
      );
    } else {
      return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(size / 10),
          border: Border.all(
            color: color,
            width: size / 50,
          ),
        ),
        child: Padding(
          padding: EdgeInsets.all(size / 25),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(size / 20),
              border: Border.all(
                color: color.withValues(alpha: 0.7),
                width: size / 100,
              ),
            ),
          ),
        ),
      );
    }
  }

  /// الحصول على نمط النص المناسب
  TextStyle _getTextStyle(Color color) {
    switch (style) {
      case CalligraphyStyle.diwani:
        return TextStyle(
          fontFamily: 'Aref Ruqaa',
          fontSize: size / 4,
          color: color,
          height: 1.2,
        );
      case CalligraphyStyle.thuluth:
        return TextStyle(
          fontFamily: 'Amiri',
          fontSize: size / 4,
          fontWeight: FontWeight.bold,
          color: color,
          height: 1.5,
        );
      case CalligraphyStyle.naskh:
        return TextStyle(
          fontFamily: 'Scheherazade New',
          fontSize: size / 4,
          color: color,
          height: 1.3,
        );
      case CalligraphyStyle.framed:
      case CalligraphyStyle.medallion:
        return TextStyle(
          fontFamily: 'Aref Ruqaa',
          fontSize: size / 5,
          fontWeight: FontWeight.bold,
          color: color,
          height: 1.2,
        );
    }
  }
}

/// أنماط الخط العربي
enum CalligraphyStyle {
  diwani, // خط ديواني
  thuluth, // خط ثلث
  naskh, // خط نسخ
  framed, // خط مؤطر
  medallion, // خط في ميدالية
}
