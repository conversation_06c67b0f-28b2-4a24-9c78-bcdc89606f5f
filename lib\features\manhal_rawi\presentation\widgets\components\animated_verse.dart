import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import '../../../data/models/verse.dart';
import '../../providers/manhal_rawi_provider.dart';
import '../../../../../utils/theme/manhal_colors.dart';
import '../../../../../utils/theme/typography.dart';
import '../../../../../utils/theme/decorations.dart';

class AnimatedVerse extends StatelessWidget {
  final Verse verse;
  final int index;
  final bool isActive;
  final VoidCallback? onTap;

  const AnimatedVerse({
    super.key,
    required this.verse,
    required this.index,
    this.isActive = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = context.watch<ManhalRawiProvider>().isDarkMode;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration:
            ManhalDecorations.verseDecoration(isDark: isDarkMode).copyWith(
          boxShadow: isActive
              ? [
                  BoxShadow(
                    color: isDarkMode
                        ? ManhalColors.gold600.withValues(alpha: 0.3)
                        : ManhalColors.gold500.withValues(alpha: 0.2),
                    blurRadius: 8,
                    spreadRadius: 1,
                  ),
                ]
              : null,
          border: Border.all(
            color: isActive
                ? (isDarkMode ? ManhalColors.gold500 : ManhalColors.primary)
                : (isDarkMode
                    ? ManhalColors.gold600.withValues(alpha: 0.3)
                    : ManhalColors.gold400.withValues(alpha: 0.5)),
            width: isActive ? 2 : 1,
          ),
        ),
        child: Column(
          children: [
            // رقم البيت
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              decoration: BoxDecoration(
                color: isDarkMode
                    ? ManhalColors.blue800.withValues(alpha: 0.7)
                    : ManhalColors.gold200.withValues(alpha: 0.7),
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(12),
                  topLeft: Radius.circular(12),
                ),
              ),
              child: Text(
                'البيت ${index + 1}',
                style: ManhalTypography.bodySmall.copyWith(
                  color: isDarkMode
                      ? ManhalColors.textLight
                      : ManhalColors.textDark,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            // الشطر الأول
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 12, 16, 8),
              child: _buildVerseText(
                verse.first,
                isDarkMode,
                isActive,
                isFirstHalf: true,
              ),
            ),

            // خط فاصل
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Divider(
                color: isDarkMode
                    ? ManhalColors.gold600.withValues(alpha: 0.3)
                    : ManhalColors.gold400.withValues(alpha: 0.5),
                thickness: 1,
              ),
            ),

            // الشطر الثاني
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 8, 16, 12),
              child: _buildVerseText(
                verse.second,
                isDarkMode,
                isActive,
                isFirstHalf: false,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVerseText(String text, bool isDarkMode, bool isActive,
      {required bool isFirstHalf}) {
    final baseWidget = Text(
      text,
      style: ManhalTypography.verseText.copyWith(
        color: isDarkMode ? ManhalColors.textLight : ManhalColors.textDark,
        fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
      ),
      textAlign: TextAlign.center,
      overflow: TextOverflow.visible,
    );

    if (isActive) {
      return baseWidget
          .animate(delay: isFirstHalf ? 100.ms : 300.ms)
          .fadeIn(duration: 500.ms)
          .slideX(
            begin: isFirstHalf ? -0.1 : 0.1,
            end: 0,
            duration: 500.ms,
            curve: Curves.easeOutQuad,
          );
    }

    return baseWidget;
  }
}
