import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../data/models/category.dart';
import '../providers/manhal_rawi_provider.dart';
import '../../../../utils/theme/manhal_colors.dart';
import '../../../../utils/theme/typography.dart';

class CategorySelector extends StatelessWidget {
  final List<Category> categories;

  const CategorySelector({
    super.key,
    required this.categories,
  });

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<ManhalRawiProvider>();
    final isDarkMode = provider.isDarkMode;
    final selectedCategoryId = provider.selectedCategoryId;

    return SizedBox(
      height: 50,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: categories.length + 1, // +1 for "All" option
        itemBuilder: (context, index) {
          // "All" option
          if (index == 0) {
            final isSelected = selectedCategoryId == null;
            return _buildCategoryChip(
              context: context,
              label: 'الكل',
              isSelected: isSelected,
              isDarkMode: isDarkMode,
              onTap: () {
                provider.filterByCategory(null);
              },
            );
          }

          // Category options
          final category = categories[index - 1];
          final isSelected = selectedCategoryId == category.id;

          return _buildCategoryChip(
            context: context,
            label: category.name,
            isSelected: isSelected,
            isDarkMode: isDarkMode,
            onTap: () {
              provider.filterByCategory(category.id);
            },
          );
        },
      ),
    );
  }

  Widget _buildCategoryChip({
    required BuildContext context,
    required String label,
    required bool isSelected,
    required bool isDarkMode,
    required VoidCallback onTap,
  }) {
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: isSelected
                ? (isDarkMode ? ManhalColors.gold600 : ManhalColors.primary)
                : (isDarkMode ? ManhalColors.blue800 : Colors.white),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: isSelected
                  ? (isDarkMode ? ManhalColors.gold500 : ManhalColors.primary)
                  : (isDarkMode ? ManhalColors.blue700 : ManhalColors.gold300),
              width: 1.5,
            ),
            boxShadow: isSelected
                ? [
                    BoxShadow(
                      color: isDarkMode
                          ? Colors.black.withValues(alpha: 0.3)
                          : Colors.black.withValues(alpha: 0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ]
                : null,
          ),
          child: Center(
            child: Text(
              label,
              style: ManhalTypography.categoryText.copyWith(
                color: isSelected
                    ? (isDarkMode ? Colors.white : Colors.white)
                    : (isDarkMode
                        ? ManhalColors.textLight
                        : ManhalColors.textDark),
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
