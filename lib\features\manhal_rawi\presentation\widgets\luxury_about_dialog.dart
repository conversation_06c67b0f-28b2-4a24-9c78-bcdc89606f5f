import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/manhal_rawi_provider.dart';
import '../../../../utils/theme/manhal_colors.dart';
import '../../../../utils/theme/islamic_typography.dart';
import '../../../../utils/animations/islamic_animations.dart';
import 'dart:math' as math;

/// حوار فاخر لعرض معلومات حول التطبيق
class LuxuryAboutDialog extends StatefulWidget {
  const LuxuryAboutDialog({super.key});

  @override
  State<LuxuryAboutDialog> createState() => _LuxuryAboutDialogState();
}

class _LuxuryAboutDialogState extends State<LuxuryAboutDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _patternAnimation;

  @override
  void initState() {
    super.initState();

    // إنشاء متحكم الرسوم المتحركة
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    );

    // رسوم متحركة للتلاشي
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
      ),
    );

    // رسوم متحركة للتكبير
    _scaleAnimation = Tween<double>(begin: 0.9, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.2, 0.7, curve: Curves.easeOutBack),
      ),
    );

    // رسوم متحركة للزخارف
    _patternAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.5, 1.0, curve: Curves.easeOut),
      ),
    );

    // بدء الرسوم المتحركة
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = context.watch<ManhalRawiProvider>().isDarkMode;
    final size = MediaQuery.of(context).size;

    return Dialog(
      backgroundColor: Colors.transparent,
      elevation: 0,
      insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Opacity(
            opacity: _fadeAnimation.value,
            child: Transform.scale(
              scale: _scaleAnimation.value,
              child: child,
            ),
          );
        },
        child: Container(
          width: double.infinity,
          constraints: BoxConstraints(
            maxWidth: 500,
            maxHeight: size.height * 0.8,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24),
            gradient: LinearGradient(
              begin: Alignment.topRight,
              end: Alignment.bottomLeft,
              colors: isDarkMode
                  ? [
                      ManhalColors.blue900,
                      ManhalColors.blue800,
                    ]
                  : [
                      Colors.white,
                      ManhalColors.gold100.withValues(alpha: 0.3),
                    ],
            ),
            boxShadow: [
              BoxShadow(
                color:
                    (isDarkMode ? ManhalColors.gold500 : ManhalColors.primary)
                        .withValues(alpha: 0.3),
                blurRadius: 20,
                spreadRadius: 2,
              ),
            ],
            border: Border.all(
              color: isDarkMode
                  ? ManhalColors.gold500.withValues(alpha: 0.5)
                  : ManhalColors.primary.withValues(alpha: 0.3),
              width: 2,
            ),
          ),
          child: Stack(
            children: [
              // زخارف الخلفية
              Positioned.fill(
                child: AnimatedBuilder(
                  animation: _patternAnimation,
                  builder: (context, child) {
                    return Opacity(
                      opacity: _patternAnimation.value * 0.2,
                      child: CustomPaint(
                        painter: _AboutPatternPainter(
                          isDark: isDarkMode,
                          progress: _patternAnimation.value,
                        ),
                        size: Size.infinite,
                      ),
                    );
                  },
                ),
              ),

              // المحتوى الرئيسي
              SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // الشعار والعنوان
                    _buildHeader(isDarkMode),

                    const SizedBox(height: 24),

                    // معلومات التطبيق
                    _buildAppInfo(isDarkMode),

                    const SizedBox(height: 24),

                    // معلومات المؤلف
                    _buildAuthorInfo(isDarkMode),

                    const SizedBox(height: 24),

                    // معلومات المطور
                    _buildDeveloperInfo(isDarkMode),

                    const SizedBox(height: 24),

                    // رسالة المبرمج الروحانية
                    _buildSpiritualMessage(isDarkMode),

                    const SizedBox(height: 32),

                    // زر الإغلاق
                    _buildCloseButton(isDarkMode),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(bool isDarkMode) {
    return Column(
      children: [
        // الشعار
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: isDarkMode
                  ? [
                      ManhalColors.blue800,
                      ManhalColors.blue900,
                    ]
                  : [
                      Colors.white,
                      ManhalColors.gold100.withValues(alpha: 0.5),
                    ],
              radius: 0.8,
            ),
            boxShadow: [
              BoxShadow(
                color:
                    (isDarkMode ? ManhalColors.gold500 : ManhalColors.primary)
                        .withValues(alpha: 0.3),
                blurRadius: 15,
                spreadRadius: 1,
              ),
            ],
            border: Border.all(
              color: isDarkMode
                  ? ManhalColors.gold500.withValues(alpha: 0.5)
                  : ManhalColors.primary.withValues(alpha: 0.3),
              width: 2,
            ),
          ),
          child: Center(
            child: Image.asset(
              'assets/images/p1.png',
              width: 50,
              height: 50,
              fit: BoxFit.contain,
            ),
          ),
        ),

        const SizedBox(height: 16),

        // عنوان التطبيق
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: isDarkMode
                  ? ManhalColors.gold500.withValues(alpha: 0.3)
                  : ManhalColors.primary.withValues(alpha: 0.2),
              width: 1.5,
            ),
            gradient: LinearGradient(
              begin: Alignment.topRight,
              end: Alignment.bottomLeft,
              colors: isDarkMode
                  ? [
                      ManhalColors.blue800.withValues(alpha: 0.5),
                      ManhalColors.blue900.withValues(alpha: 0.3),
                    ]
                  : [
                      Colors.white.withValues(alpha: 0.7),
                      ManhalColors.gold100.withValues(alpha: 0.3),
                    ],
            ),
            boxShadow: [
              BoxShadow(
                color:
                    (isDarkMode ? ManhalColors.gold500 : ManhalColors.primary)
                        .withValues(alpha: 0.2),
                blurRadius: 8,
                spreadRadius: 1,
              ),
            ],
          ),
          child: Text(
            'المنهل الروي',
            style: IslamicTypography.luxuryBookTitle(
              isDark: isDarkMode,
              fontSize: 32,
            ),
            textAlign: TextAlign.center,
          ),
        ),

        const SizedBox(height: 8),

        // إصدار التطبيق
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: isDarkMode
                ? ManhalColors.blue900.withValues(alpha: 0.3)
                : Colors.white.withValues(alpha: 0.3),
            border: Border.all(
              color: isDarkMode
                  ? ManhalColors.gold500.withValues(alpha: 0.2)
                  : ManhalColors.primary.withValues(alpha: 0.1),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.new_releases,
                size: 14,
                color: isDarkMode ? ManhalColors.gold300 : ManhalColors.primary,
              ),
              const SizedBox(width: 6),
              Text(
                'الإصدار 1.0.7',
                style: IslamicTypography.luxurySubtitle(
                  isDark: isDarkMode,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAppInfo(bool isDarkMode) {
    return IslamicAnimations.fadeSlideIn(
      isDark: isDarkMode,
      durationMs: 800,
      delayMs: 300,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: isDarkMode
              ? ManhalColors.blue800.withValues(alpha: 0.3)
              : Colors.white.withValues(alpha: 0.5),
          border: Border.all(
            color: isDarkMode
                ? ManhalColors.gold500.withValues(alpha: 0.3)
                : ManhalColors.primary.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            // عنوان القسم
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  size: 18,
                  color:
                      isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'عن التطبيق',
                  style: IslamicTypography.luxurySubtitle(
                    isDark: isDarkMode,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // وصف التطبيق
            Text(
              'تطبيق المنهل الروي هو تطبيق مخصص لعرض ديوان الشعر للشاعر محمد هزاع باعلوي الحضرمي. يحتوي الديوان على مجموعة من القصائد المرتبة هجائياً من الهمزة إلى الياء، ويتضمن أنواعاً مختلفة من القصائد منها العادية والمخمسة.',
              style: IslamicTypography.luxuryBody(
                isDark: isDarkMode,
                fontSize: 15,
              ),
              textAlign: TextAlign.justify,
            ),

            const SizedBox(height: 12),

            // ميزات التطبيق
            Text(
              'يتميز التطبيق بواجهة مستخدم فاخرة وسهلة الاستخدام، مع إمكانية تخصيص طريقة عرض القصائد وحفظ المفضلة منها للرجوع إليها لاحقاً.',
              style: IslamicTypography.luxuryBody(
                isDark: isDarkMode,
                fontSize: 15,
              ),
              textAlign: TextAlign.justify,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAuthorInfo(bool isDarkMode) {
    return IslamicAnimations.fadeSlideIn(
      isDark: isDarkMode,
      durationMs: 800,
      delayMs: 500,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: isDarkMode
              ? ManhalColors.blue800.withValues(alpha: 0.3)
              : Colors.white.withValues(alpha: 0.5),
          border: Border.all(
            color: isDarkMode
                ? ManhalColors.gold500.withValues(alpha: 0.3)
                : ManhalColors.primary.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            // عنوان القسم
            Row(
              children: [
                Icon(
                  Icons.person,
                  size: 18,
                  color:
                      isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'المؤلف',
                  style: IslamicTypography.luxurySubtitle(
                    isDark: isDarkMode,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // اسم المؤلف
            Text(
              'محمد هزاع باعلوي الحضرمي',
              style: IslamicTypography.luxurySubtitle(
                isDark: isDarkMode,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 12),

            // نبذة عن المؤلف
            Text(
              'شاعر يمني من حضرموت، له العديد من القصائد والأشعار في مختلف المواضيع. يتميز شعره بالأصالة والعمق، وقد جمعت قصائده في ديوان "المنهل الروي" الذي يعد مرجعاً مهماً للشعر العربي الأصيل.',
              style: IslamicTypography.luxuryBody(
                isDark: isDarkMode,
                fontSize: 15,
              ),
              textAlign: TextAlign.justify,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeveloperInfo(bool isDarkMode) {
    return IslamicAnimations.fadeSlideIn(
      isDark: isDarkMode,
      durationMs: 800,
      delayMs: 700,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: isDarkMode
              ? ManhalColors.blue800.withValues(alpha: 0.3)
              : Colors.white.withValues(alpha: 0.5),
          border: Border.all(
            color: isDarkMode
                ? ManhalColors.gold500.withValues(alpha: 0.3)
                : ManhalColors.primary.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            // عنوان القسم
            Row(
              children: [
                Icon(
                  Icons.code,
                  size: 18,
                  color:
                      isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'تطوير',
                  style: IslamicTypography.luxurySubtitle(
                    isDark: isDarkMode,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // معلومات التطوير
            Text(
              'تم تطوير هذا التطبيق باستخدام إطار عمل Flutter، مع تطبيق نمط هندسة البرمجيات Feature-first with Clean Architecture لضمان جودة وقابلية صيانة الكود.',
              style: IslamicTypography.luxuryBody(
                isDark: isDarkMode,
                fontSize: 15,
              ),
              textAlign: TextAlign.justify,
            ),

            const SizedBox(height: 12),

            // حقوق الملكية
            Text(
              '© 2024 جميع الحقوق محفوظة',
              style: IslamicTypography.luxuryCaption(
                isDark: isDarkMode,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSpiritualMessage(bool isDarkMode) {
    return IslamicAnimations.fadeSlideIn(
      isDark: isDarkMode,
      durationMs: 800,
      delayMs: 900,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topRight,
            end: Alignment.bottomLeft,
            colors: isDarkMode
                ? [
                    ManhalColors.blue900.withValues(alpha: 0.7),
                    ManhalColors.blue800.withValues(alpha: 0.5),
                  ]
                : [
                    ManhalColors.gold100.withValues(alpha: 0.7),
                    Colors.white.withValues(alpha: 0.9),
                  ],
          ),
          border: Border.all(
            color: isDarkMode
                ? ManhalColors.gold500.withValues(alpha: 0.5)
                : ManhalColors.primary.withValues(alpha: 0.3),
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: (isDarkMode ? ManhalColors.gold500 : ManhalColors.primary)
                  .withValues(alpha: 0.2),
              blurRadius: 10,
              spreadRadius: 1,
            ),
          ],
        ),
        child: Column(
          children: [
            // رمز روحاني
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isDarkMode
                    ? ManhalColors.gold500.withValues(alpha: 0.2)
                    : ManhalColors.primary.withValues(alpha: 0.1),
                border: Border.all(
                  color: isDarkMode
                      ? ManhalColors.gold500.withValues(alpha: 0.5)
                      : ManhalColors.primary.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Icon(
                Icons.volunteer_activism,
                size: 20,
                color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
              ),
            ),

            const SizedBox(height: 16),

            // رسالة المبرمج
            Text(
              'بسم الله الرحمن الرحيم',
              style: IslamicTypography.luxurySubtitle(
                isDark: isDarkMode,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 12),

            Text(
              'الحمد لله الذي وفقني لإتمام هذا العمل، وأسأله سبحانه أن يجعله خالصاً لوجهه الكريم، وأن ينفع به المسلمين.',
              style: IslamicTypography.luxuryBody(
                isDark: isDarkMode,
                fontSize: 15,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 8),

            Text(
              'إن أصبت فمن الله وحده، وإن أخطأت أو قصّرت فمن نفسي والشيطان، وأستغفر الله العظيم على كل تقصير.',
              style: IslamicTypography.luxuryBody(
                isDark: isDarkMode,
                fontSize: 15,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 8),

            Text(
              'أسأل الله أن يتقبل هذا العمل، وأن يجعله في ميزان حسناتي يوم القيامة.',
              style: IslamicTypography.luxuryBody(
                isDark: isDarkMode,
                fontSize: 15,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 12),

            // توقيع
            Text(
              'المبرمج',
              style: IslamicTypography.luxuryCaption(
                isDark: isDarkMode,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCloseButton(bool isDarkMode) {
    return IslamicAnimations.fadeSlideIn(
      isDark: isDarkMode,
      durationMs: 800,
      delayMs: 1100,
      beginOffset: const Offset(0, 20),
      child: ElevatedButton(
        onPressed: () {
          Navigator.of(context).pop();
        },
        style: ElevatedButton.styleFrom(
          backgroundColor:
              isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 4,
          shadowColor:
              (isDarkMode ? ManhalColors.gold500 : ManhalColors.primary)
                  .withValues(alpha: 0.5),
        ),
        child: Text(
          'إغلاق',
          style: IslamicTypography.luxuryButton(
            isDark: false,
            fontSize: 18,
          ),
        ),
      ),
    );
  }
}

/// رسام الزخارف الإسلامية لحوار حول التطبيق
class _AboutPatternPainter extends CustomPainter {
  final bool isDark;
  final double progress;

  _AboutPatternPainter({
    required this.isDark,
    required this.progress,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final baseColor = isDark ? ManhalColors.gold500 : ManhalColors.primary;
    final paint = Paint()
      ..color = baseColor.withValues(alpha: 0.1)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    final width = size.width;
    final height = size.height;

    // رسم الزخارف في الزوايا
    _drawCornerPattern(canvas, paint, Offset(0, 0), size, progress);
    _drawCornerPattern(canvas, paint, Offset(width, 0), size, progress);
    _drawCornerPattern(canvas, paint, Offset(0, height), size, progress);
    _drawCornerPattern(canvas, paint, Offset(width, height), size, progress);

    // رسم إطار زخرفي
    final borderRect = Rect.fromLTWH(
      width * 0.05,
      height * 0.05,
      width * 0.9,
      height * 0.9,
    );

    final borderPath = Path();
    borderPath.addRRect(RRect.fromRectAndRadius(
      borderRect,
      Radius.circular(16),
    ));

    canvas.drawPath(borderPath, paint);
  }

  void _drawCornerPattern(
      Canvas canvas, Paint paint, Offset corner, Size size, double progress) {
    final patternSize = size.width * 0.15 * progress;
    final center = Offset(
      corner.dx == 0
          ? corner.dx + patternSize / 2
          : corner.dx - patternSize / 2,
      corner.dy == 0
          ? corner.dy + patternSize / 2
          : corner.dy - patternSize / 2,
    );

    // رسم الزخرفة الإسلامية الهندسية
    final path = Path();
    final sides = 8;
    final radius = patternSize / 2;

    for (int i = 0; i < sides; i++) {
      final angle = (i * 2 * math.pi) / sides;
      final point = Offset(
        center.dx + radius * 0.8 * math.cos(angle),
        center.dy + radius * 0.8 * math.sin(angle),
      );

      if (i == 0) {
        path.moveTo(point.dx, point.dy);
      } else {
        path.lineTo(point.dx, point.dy);
      }
    }

    path.close();
    canvas.drawPath(path, paint);

    // رسم الزخرفة الداخلية
    final innerPath = Path();
    final innerRadius = radius * 0.6;

    for (int i = 0; i < sides; i++) {
      final angle = (i * 2 * math.pi) / sides + math.pi / sides;
      final point = Offset(
        center.dx + innerRadius * math.cos(angle),
        center.dy + innerRadius * math.sin(angle),
      );

      if (i == 0) {
        innerPath.moveTo(point.dx, point.dy);
      } else {
        innerPath.lineTo(point.dx, point.dy);
      }
    }

    innerPath.close();
    canvas.drawPath(innerPath, paint);

    // ربط النقاط
    for (int i = 0; i < sides; i++) {
      final outerAngle = (i * 2 * math.pi) / sides;
      final innerAngle = (i * 2 * math.pi) / sides + math.pi / sides;

      final outerPoint = Offset(
        center.dx + radius * 0.8 * math.cos(outerAngle),
        center.dy + radius * 0.8 * math.sin(outerAngle),
      );

      final innerPoint = Offset(
        center.dx + innerRadius * math.cos(innerAngle),
        center.dy + innerRadius * math.sin(innerAngle),
      );

      canvas.drawLine(outerPoint, innerPoint, paint);
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}
