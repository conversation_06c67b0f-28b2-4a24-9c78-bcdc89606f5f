import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';

import '../../data/models/poem.dart';
import '../../data/models/poet.dart';
import '../providers/manhal_rawi_provider.dart';
import '../widgets/animated_islamic_background.dart';
import '../widgets/book_style_verse_display.dart';
import '../widgets/professional_verse_display.dart';
import '../widgets/classic_verse_display.dart';
import '../widgets/mukhammas_verse_display.dart';
import '../widgets/luxury_mukhammas_display.dart';
import '../widgets/classic_mukhammas_display.dart';
import '../screens/fullscreen_verse_screen.dart';
import '../screens/fullscreen_mukhammas_screen.dart';
// تم تعليق الاستيرادات غير المستخدمة بعد تعليق بطاقة المؤلف
// import '../screens/luxury_author_details_screen.dart';
// import '../widgets/luxury_author_card.dart';
import '../screens/settings_screen.dart';
import '../../../../utils/theme/manhal_colors.dart';
import '../../../../utils/theme/islamic_typography.dart';
import '../../../../utils/theme/islamic_patterns.dart';
import '../../../../utils/helpers/error_handler.dart';

/// صفحة عرض تفاصيل القصيدة بتصميم فاخر
class LuxuryPoemDetailsScreen extends StatefulWidget {
  final Poem poem;
  final Poet poet;

  const LuxuryPoemDetailsScreen({
    super.key,
    required this.poem,
    required this.poet,
  });

  @override
  State<LuxuryPoemDetailsScreen> createState() =>
      _LuxuryPoemDetailsScreenState();
}

class _LuxuryPoemDetailsScreenState extends State<LuxuryPoemDetailsScreen>
    with SingleTickerProviderStateMixin {
  int _activeVerseIndex = 0;
  late AnimationController _controller;
  late Animation<double> _headerAnimation;
  late Animation<double> _contentAnimation;

  // متغير لتخزين حالة وضع ملء الشاشة
  bool _isFullScreen = false;

  // متغير لتخزين حالة الانتقال بين الأوضاع
  bool _isTransitioning = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _headerAnimation = CurvedAnimation(
      parent: _controller,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    );

    _contentAnimation = CurvedAnimation(
      parent: _controller,
      curve: const Interval(0.3, 1.0, curve: Curves.easeOut),
    );

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();

    // إعادة إظهار شريط الحالة وشريط التنقل عند الخروج من الشاشة
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

    super.dispose();
  }

  /// نسخ البيت المحدد إلى الحافظة
  /// تستخدم هذه الدالة فقط عند النقر على زر النسخ وليس عند تحديد البيت
  void _copyVerseToClipboard(int index) {
    String text;

    if (widget.poem.isMukhammas) {
      // نسخ البيت المخمس
      final verse = widget.poem.mukhammasVerses![index];
      text = '${verse.firstLine1} ${verse.firstLine2}\n'
          '${verse.secondLine1} ${verse.secondLine2}\n'
          '${verse.fifthLine}';
    } else {
      // نسخ البيت العادي
      final verse = widget.poem.verses[index];
      text = '${verse.first}\n${verse.second}';
    }

    Clipboard.setData(ClipboardData(text: text)).then((_) {
      if (!mounted) return;

      showErrorMessage(
        context,
        'تم نسخ البيت إلى الحافظة',
      );
    }).catchError((error) {
      if (!mounted) return;

      showErrorMessage(
        context,
        'حدث خطأ أثناء نسخ البيت',
      );
    });
  }

  void _sharePoem() {
    try {
      // إنشاء نص المشاركة
      final title = widget.poem.title;
      final poet = widget.poet.name;
      final description = widget.poem.description;

      // إضافة الأبيات حسب نوع القصيدة
      String verses;

      if (widget.poem.isMukhammas) {
        // إضافة الأبيات المخمسة
        verses = widget.poem.mukhammasVerses!.map((verse) {
          return '${verse.firstLine1} ${verse.firstLine2}\n'
              '${verse.secondLine1} ${verse.secondLine2}\n'
              '${verse.fifthLine}';
        }).join('\n\n');
      } else {
        // إضافة الأبيات العادية
        verses = widget.poem.verses.map((verse) {
          return '${verse.first}\n${verse.second}';
        }).join('\n\n');
      }

      // النص الكامل للمشاركة
      final shareText = '''
$title
للشاعر: $poet

$verses

$description

- من ديوان المنهل الروي -
''';

      // مشاركة النص (استخدام الطريقة الحديثة)
      Share.share(shareText, subject: 'قصيدة $title من ديوان المنهل الراوي');
    } catch (e) {
      if (!mounted) return;

      showErrorMessage(
        context,
        'حدث خطأ أثناء مشاركة القصيدة',
      );
    }
  }

  // تبديل حالة المفضلة للقصيدة
  void _toggleFavorite() {
    try {
      final provider = context.read<ManhalRawiProvider>();
      provider.toggleFavorite(widget.poem.id);

      // عرض رسالة للمستخدم
      final isFavorite = !widget.poem.isFavorite; // الحالة الجديدة بعد التبديل

      showErrorMessage(
        context,
        isFavorite
            ? 'تمت إضافة القصيدة إلى المفضلة'
            : 'تمت إزالة القصيدة من المفضلة',
      );
    } catch (e) {
      if (!mounted) return;

      showErrorMessage(
        context,
        'حدث خطأ أثناء تحديث المفضلة',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<ManhalRawiProvider>();
    final isDarkMode = provider.isDarkMode;

    // الحصول على أبعاد الشاشة للتوافق مع مختلف الأحجام
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isLargeScreen = screenSize.width > 600;

    return Directionality(
      textDirection: TextDirection.rtl, // تأكيد اتجاه RTL للشاشة بأكملها
      child: Scaffold(
        body: AnimatedIslamicBackground(
          child: SafeArea(
            child: Column(
              children: [
                _buildAppBar(context),
                Expanded(
                  child: ListView(
                    padding: EdgeInsets.zero,
                    children: [
                      // عنوان القصيدة المتحرك
                      AnimatedBuilder(
                        animation: _headerAnimation,
                        builder: (context, child) {
                          return Opacity(
                            opacity: _headerAnimation.value,
                            child: Transform.translate(
                              offset:
                                  Offset(0, 30 * (1 - _headerAnimation.value)),
                              child: child,
                            ),
                          );
                        },
                        child: Padding(
                          padding: EdgeInsets.fromLTRB(
                            isSmallScreen ? 12 : (isLargeScreen ? 24 : 16),
                            isLargeScreen ? 32 : 24,
                            isSmallScreen ? 12 : (isLargeScreen ? 24 : 16),
                            isSmallScreen ? 6 : (isLargeScreen ? 12 : 8),
                          ),
                          child: Column(
                            children: [
                              // عنوان القصيدة
                              Text(
                                widget.poem.title,
                                style: IslamicTypography.luxuryPoemTitle(
                                  isDark: isDarkMode,
                                  fontSize: isSmallScreen
                                      ? 26
                                      : (isLargeScreen ? 36 : 30),
                                ),
                                textAlign: TextAlign.center,
                              ),

                              SizedBox(height: isLargeScreen ? 12 : 8),

                              // البحر الشعري
                              Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: isSmallScreen
                                      ? 12
                                      : (isLargeScreen ? 20 : 16),
                                  vertical: isSmallScreen
                                      ? 4
                                      : (isLargeScreen ? 8 : 6),
                                ),
                                decoration: BoxDecoration(
                                  color: isDarkMode
                                      ? ManhalColors.blue900
                                          .withValues(alpha: 0.7)
                                      : ManhalColors.gold100
                                          .withValues(alpha: 0.7),
                                  borderRadius: BorderRadius.circular(
                                      isLargeScreen ? 20 : 16),
                                  border: Border.all(
                                    color: isDarkMode
                                        ? ManhalColors.gold600
                                            .withValues(alpha: 0.3)
                                        : ManhalColors.gold300,
                                    width: isLargeScreen ? 1.5 : 1,
                                  ),
                                ),
                                child: Text(
                                  'البحر ${widget.poem.meter}',
                                  style: IslamicTypography.luxuryCategory(
                                    isDark: isDarkMode,
                                    fontSize: isSmallScreen
                                        ? 14
                                        : (isLargeScreen ? 18 : 16),
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      // بطاقة المؤلف (تم تعليقها بناءً على طلب المستخدم)
                      /*
                      AnimatedBuilder(
                        animation: _contentAnimation,
                        builder: (context, child) {
                          return Opacity(
                            opacity: _contentAnimation.value,
                            child: Transform.translate(
                              offset:
                                  Offset(0, 30 * (1 - _contentAnimation.value)),
                              child: child,
                            ),
                          );
                        },
                        child: Padding(
                          padding: EdgeInsets.symmetric(
                            vertical:
                                isSmallScreen ? 12 : (isLargeScreen ? 24 : 16),
                          ),
                          child: Center(
                            child: GestureDetector(
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) =>
                                        LuxuryAuthorDetailsScreen(
                                      poet: widget.poet,
                                    ),
                                  ),
                                );
                              },
                              child: LuxuryAuthorCard(
                                poet: widget.poet,
                                showAnimation: false,
                              ),
                            ),
                          ),
                        ),
                      ),
                      */

                      // وصف القصيدة
                      AnimatedBuilder(
                        animation: _contentAnimation,
                        builder: (context, child) {
                          return Opacity(
                            opacity: _contentAnimation.value,
                            child: Transform.translate(
                              offset:
                                  Offset(0, 30 * (1 - _contentAnimation.value)),
                              child: child,
                            ),
                          );
                        },
                        child: Container(
                          margin: EdgeInsets.fromLTRB(
                            isSmallScreen ? 12 : (isLargeScreen ? 24 : 16),
                            isSmallScreen ? 6 : (isLargeScreen ? 12 : 8),
                            isSmallScreen ? 12 : (isLargeScreen ? 24 : 16),
                            isSmallScreen ? 16 : (isLargeScreen ? 32 : 24),
                          ),
                          padding: EdgeInsets.all(
                            isSmallScreen ? 12 : (isLargeScreen ? 24 : 16),
                          ),
                          decoration: IslamicPatterns.getDecorativeBorder(
                            isDark: isDarkMode,
                            borderRadius: BorderRadius.circular(
                              isLargeScreen ? 20 : 16,
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // عنوان الوصف
                              Text(
                                'عن القصيدة',
                                style: IslamicTypography.luxurySubtitle(
                                  isDark: isDarkMode,
                                  fontSize: isSmallScreen
                                      ? 18
                                      : (isLargeScreen ? 24 : 20),
                                ),
                              ),

                              SizedBox(height: isLargeScreen ? 12 : 8),

                              // نص الوصف
                              Text(
                                widget.poem.description,
                                style: IslamicTypography.luxuryBody(
                                  isDark: isDarkMode,
                                  fontSize: isSmallScreen
                                      ? 14
                                      : (isLargeScreen ? 18 : 16),
                                ),
                                textAlign: TextAlign.justify,
                              ),

                              SizedBox(height: isLargeScreen ? 24 : 16),

                              // معلومات إضافية
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceAround,
                                children: [
                                  _buildInfoItem(
                                    context: context,
                                    icon: Icons.format_list_numbered,
                                    label: 'عدد الأبيات',
                                    value:
                                        '${widget.poem.isMukhammas ? widget.poem.mukhammasVerses!.length : widget.poem.verses.length}',
                                  ),
                                  _buildInfoItem(
                                    context: context,
                                    icon: Icons.history_edu,
                                    label: 'العصر',
                                    value: widget.poet.era,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),

                      // عنوان قسم الأبيات
                      AnimatedBuilder(
                        animation: _contentAnimation,
                        builder: (context, child) {
                          return Opacity(
                            opacity: _contentAnimation.value,
                            child: Transform.translate(
                              offset:
                                  Offset(0, 30 * (1 - _contentAnimation.value)),
                              child: child,
                            ),
                          );
                        },
                        child: Padding(
                          padding: EdgeInsets.fromLTRB(
                            isSmallScreen ? 12 : (isLargeScreen ? 24 : 16),
                            isSmallScreen ? 6 : (isLargeScreen ? 12 : 8),
                            isSmallScreen ? 12 : (isLargeScreen ? 24 : 16),
                            isSmallScreen ? 6 : (isLargeScreen ? 12 : 8),
                          ),
                          child: Row(
                            children: [
                              // زخرفة إسلامية
                              CustomPaint(
                                painter: IslamicPatterns.getFloralPattern(
                                  isDark: isDarkMode,
                                  opacity: 0.3,
                                  complexity: 2,
                                ),
                                size: Size(
                                  isSmallScreen
                                      ? 24
                                      : (isLargeScreen ? 36 : 30),
                                  isSmallScreen
                                      ? 24
                                      : (isLargeScreen ? 36 : 30),
                                ),
                              ),

                              SizedBox(width: isLargeScreen ? 12 : 8),

                              // عنوان القسم
                              Text(
                                'أبيات القصيدة',
                                style: IslamicTypography.luxurySubtitle(
                                  isDark: isDarkMode,
                                  fontSize: isSmallScreen
                                      ? 18
                                      : (isLargeScreen ? 26 : 22),
                                ),
                              ),

                              const Spacer(),

                              // عدد الأبيات
                              Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: isSmallScreen
                                      ? 8
                                      : (isLargeScreen ? 16 : 12),
                                  vertical: isSmallScreen
                                      ? 2
                                      : (isLargeScreen ? 6 : 4),
                                ),
                                decoration: BoxDecoration(
                                  color: isDarkMode
                                      ? ManhalColors.blue900
                                          .withValues(alpha: 0.7)
                                      : ManhalColors.gold100
                                          .withValues(alpha: 0.7),
                                  borderRadius: BorderRadius.circular(
                                    isLargeScreen ? 16 : 12,
                                  ),
                                  border: Border.all(
                                    color: isDarkMode
                                        ? ManhalColors.gold600
                                            .withValues(alpha: 0.3)
                                        : ManhalColors.gold300,
                                    width: isLargeScreen ? 1.5 : 1,
                                  ),
                                ),
                                child: Text(
                                  '${_activeVerseIndex + 1}/${widget.poem.isMukhammas ? widget.poem.mukhammasVerses!.length : widget.poem.verses.length}',
                                  style: IslamicTypography.luxuryCategory(
                                    isDark: isDarkMode,
                                    fontSize: isSmallScreen
                                        ? 12
                                        : (isLargeScreen ? 16 : 14),
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      // عارض الأبيات بالنمط المختار
                      AnimatedBuilder(
                        animation: _contentAnimation,
                        builder: (context, child) {
                          return Opacity(
                            opacity: _contentAnimation.value,
                            child: child,
                          );
                        },
                        child: Container(
                          padding: EdgeInsets.only(
                            bottom:
                                isSmallScreen ? 24 : (isLargeScreen ? 48 : 32),
                          ),
                          // تعديل الهوامش للقصائد المخمسة لتناسب التصميم الفاخر
                          margin: widget.poem.isMukhammas
                              ? EdgeInsets.symmetric(
                                  horizontal: isSmallScreen
                                      ? 4
                                      : (isLargeScreen ? 16 : 8),
                                )
                              : EdgeInsets.zero,
                          child: _buildVerseDisplay(),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar(BuildContext context) {
    final isDarkMode = context.watch<ManhalRawiProvider>().isDarkMode;

    // الحصول على أبعاد الشاشة للتوافق مع مختلف الأحجام
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;
    final isLargeScreen = screenWidth > 600;

    // تحديد أحجام العناصر بناءً على حجم الشاشة
    final double iconSize =
        isLargeScreen ? 24.0 : (isSmallScreen ? 18.0 : 20.0);
    final double buttonSize =
        isLargeScreen ? 48.0 : (isSmallScreen ? 36.0 : 40.0);
    final double borderWidth = isLargeScreen ? 1.5 : 1.0;
    final double buttonMargin =
        isLargeScreen ? 12.0 : (isSmallScreen ? 4.0 : 8.0);
    final EdgeInsets padding = isLargeScreen
        ? const EdgeInsets.symmetric(horizontal: 16, vertical: 12)
        : (isSmallScreen
            ? const EdgeInsets.symmetric(horizontal: 6, vertical: 6)
            : const EdgeInsets.symmetric(horizontal: 8, vertical: 8));

    return AnimatedBuilder(
      animation: _headerAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _headerAnimation.value,
          child: child,
        );
      },
      child: Container(
        padding: padding,
        decoration: BoxDecoration(
          color: Colors.transparent,
          boxShadow: [
            BoxShadow(
              color: isDarkMode
                  ? Colors.black.withValues(alpha: 0.1)
                  : Colors.black.withValues(alpha: 0.05),
              blurRadius: isLargeScreen ? 6 : 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // زر الرجوع
            Container(
              height: buttonSize,
              width: buttonSize,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isDarkMode
                    ? ManhalColors.blue800.withValues(alpha: 0.7)
                    : Colors.white.withValues(alpha: 0.7),
                border: Border.all(
                  color: isDarkMode
                      ? ManhalColors.gold500.withValues(alpha: 0.3)
                      : ManhalColors.gold300,
                  width: borderWidth,
                ),
              ),
              child: IconButton(
                icon: Icon(
                  Icons
                      .arrow_back_ios_new, // تغيير من arrow_back_ios_new إلى arrow_forward_ios للتوافق مع اتجاه RTL
                  color:
                      isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
                  size: iconSize,
                ),
                onPressed: () {
                  Navigator.pop(context);
                },
                padding: EdgeInsets.zero,
              ),
            ),

            const Spacer(),

            // زر نسخ البيت المحدد
            Container(
              height: buttonSize,
              width: buttonSize,
              margin: EdgeInsets.only(
                  right:
                      buttonMargin), // تغيير من left إلى right للتوافق مع اتجاه RTL
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isDarkMode
                    ? ManhalColors.blue800.withValues(alpha: 0.7)
                    : Colors.white.withValues(alpha: 0.7),
                border: Border.all(
                  color: isDarkMode
                      ? ManhalColors.gold500.withValues(alpha: 0.3)
                      : ManhalColors.gold300,
                  width: borderWidth,
                ),
              ),
              child: IconButton(
                icon: Icon(
                  Icons.content_copy,
                  color:
                      isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
                  size: iconSize,
                ),
                onPressed: () => _copyVerseToClipboard(_activeVerseIndex),
                tooltip: 'نسخ البيت المحدد',
                padding: EdgeInsets.zero,
              ),
            ),

            // زر المفضلة
            Container(
              height: buttonSize,
              width: buttonSize,
              margin: EdgeInsets.only(
                  right:
                      buttonMargin), // تغيير من left إلى right للتوافق مع اتجاه RTL
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isDarkMode
                    ? ManhalColors.blue800.withValues(alpha: 0.7)
                    : Colors.white.withValues(alpha: 0.7),
                border: Border.all(
                  color: isDarkMode
                      ? ManhalColors.gold500.withValues(alpha: 0.3)
                      : ManhalColors.gold300,
                  width: borderWidth,
                ),
              ),
              child: IconButton(
                icon: Icon(
                  widget.poem.isFavorite
                      ? Icons.favorite
                      : Icons.favorite_border,
                  color: widget.poem.isFavorite
                      ? Colors.red
                      : (isDarkMode
                          ? ManhalColors.gold500
                          : ManhalColors.primary),
                  size: iconSize,
                ),
                onPressed: _toggleFavorite,
                tooltip: 'إضافة/إزالة من المفضلة',
                padding: EdgeInsets.zero,
              ),
            ),

            // زر المشاركة
            Container(
              height: buttonSize,
              width: buttonSize,
              margin: EdgeInsets.only(
                  right:
                      buttonMargin), // تغيير من left إلى right للتوافق مع اتجاه RTL
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isDarkMode
                    ? ManhalColors.blue800.withValues(alpha: 0.7)
                    : Colors.white.withValues(alpha: 0.7),
                border: Border.all(
                  color: isDarkMode
                      ? ManhalColors.gold500.withValues(alpha: 0.3)
                      : ManhalColors.gold300,
                  width: borderWidth,
                ),
              ),
              child: IconButton(
                icon: Icon(
                  Icons.share,
                  color:
                      isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
                  size: iconSize,
                ),
                onPressed: _sharePoem,
                tooltip: 'مشاركة القصيدة',
                padding: EdgeInsets.zero,
              ),
            ),

            // زر الإعدادات
            Container(
              height: buttonSize,
              width: buttonSize,
              margin: EdgeInsets.only(
                  right:
                      buttonMargin), // تغيير من left إلى right للتوافق مع اتجاه RTL
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isDarkMode
                    ? ManhalColors.blue800.withValues(alpha: 0.7)
                    : Colors.white.withValues(alpha: 0.7),
                border: Border.all(
                  color: isDarkMode
                      ? ManhalColors.gold500.withValues(alpha: 0.3)
                      : ManhalColors.gold300,
                  width: borderWidth,
                ),
              ),
              child: IconButton(
                icon: Icon(
                  Icons.settings,
                  color:
                      isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
                  size: iconSize,
                ),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const SettingsScreen(),
                    ),
                  );
                },
                tooltip: 'الإعدادات',
                padding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // تبديل وضع ملء الشاشة
  void _toggleFullScreen() {
    final provider = context.read<ManhalRawiProvider>();

    if (_isFullScreen) {
      // إغلاق وضع ملء الشاشة
      setState(() {
        _isFullScreen = false;
        _isTransitioning = true;
      });

      // تأخير لإظهار تأثير الانتقال
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted) {
          setState(() {
            _isTransitioning = false;
          });
        }
      });

      // إعادة إظهار شريط الحالة وشريط التنقل
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    } else {
      // فتح وضع ملء الشاشة باستخدام الشاشة المخصصة
      if (widget.poem.isMukhammas) {
        // إذا كانت القصيدة مخمسة، نستخدم شاشة ملء الشاشة المخصصة للقصائد المخمسة
        Navigator.of(context)
            .push(
          MaterialPageRoute(
            builder: (context) => FullscreenMukhammasScreen(
              poem: widget.poem,
              initialPage: _activeVerseIndex,
            ),
          ),
        )
            .then((newIndex) {
          // تحديث مؤشر البيت النشط عند العودة من وضع ملء الشاشة
          if (newIndex != null) {
            setState(() {
              _activeVerseIndex = newIndex;
            });
          }
        });
      } else {
        // إذا كانت القصيدة عادية، نستخدم شاشة ملء الشاشة المخصصة
        Navigator.of(context)
            .push(
          MaterialPageRoute(
            builder: (context) => FullscreenVerseScreen(
              poem: widget.poem,
              initialPage: _activeVerseIndex,
              displayType: provider.verseDisplayType,
            ),
          ),
        )
            .then((newIndex) {
          // تحديث مؤشر البيت النشط عند العودة من وضع ملء الشاشة
          if (newIndex != null) {
            setState(() {
              _activeVerseIndex = newIndex;
            });
          }
        });
      }
    }
  }

  // بناء عارض الأبيات المناسب حسب الإعدادات
  Widget _buildVerseDisplay() {
    final provider = context.watch<ManhalRawiProvider>();
    final isDarkMode = provider.isDarkMode;

    // الحصول على أبعاد الشاشة للتوافق مع مختلف الأحجام
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;
    final isLargeScreen = screenWidth > 600;

    // تحديد أحجام العناصر بناءً على حجم الشاشة
    final double iconSize =
        isLargeScreen ? 32.0 : (isSmallScreen ? 20.0 : 24.0);
    final double buttonPadding =
        isLargeScreen ? 12.0 : (isSmallScreen ? 6.0 : 8.0);
    final double borderWidth = isLargeScreen ? 1.5 : 1.0;
    final double borderRadius =
        isLargeScreen ? 24.0 : (isSmallScreen ? 16.0 : 20.0);
    final double topPosition =
        isLargeScreen ? 24.0 : (isSmallScreen ? 12.0 : 16.0);
    final double rightPosition =
        isLargeScreen ? 24.0 : (isSmallScreen ? 12.0 : 16.0);

    // إنشاء أزرار التحكم في وضع ملء الشاشة
    final fullScreenControls = Positioned(
      top: topPosition,
      left: rightPosition, // تغيير من right إلى left للتوافق مع اتجاه RTL
      child: AnimatedOpacity(
        opacity: _isTransitioning ? 0.0 : 1.0,
        duration: const Duration(milliseconds: 300),
        child: Container(
          padding: EdgeInsets.all(isSmallScreen ? 6 : (isLargeScreen ? 12 : 8)),
          decoration: BoxDecoration(
            color: isDarkMode
                ? ManhalColors.blue900.withValues(alpha: 0.8)
                : Colors.white.withValues(alpha: 0.8),
            borderRadius: BorderRadius.circular(borderRadius),
            boxShadow: [
              BoxShadow(
                color: isDarkMode
                    ? Colors.black.withValues(alpha: 0.3)
                    : Colors.grey.withValues(alpha: 0.2),
                blurRadius: isLargeScreen ? 8 : 5,
                offset: const Offset(0, 2),
              ),
            ],
            border: Border.all(
              color: isDarkMode
                  ? ManhalColors.gold500.withValues(alpha: 0.3)
                  : ManhalColors.gold300,
              width: borderWidth,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // زر إغلاق وضع ملء الشاشة
              IconButton(
                icon: Icon(
                  Icons.fullscreen_exit,
                  color:
                      isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
                  size: iconSize,
                ),
                onPressed: _toggleFullScreen,
                tooltip: 'إغلاق وضع ملء الشاشة',
                padding: EdgeInsets.all(buttonPadding),
                constraints: BoxConstraints(
                  minWidth: isLargeScreen ? 56 : (isSmallScreen ? 32 : 40),
                  minHeight: isLargeScreen ? 56 : (isSmallScreen ? 32 : 40),
                ),
              ),
            ],
          ),
        ),
      ),
    );

    // التحقق مما إذا كانت القصيدة مخمسة
    Widget verseDisplay;

    if (widget.poem.isMukhammas &&
        widget.poem.mukhammasVerses != null &&
        widget.poem.mukhammasVerses!.isNotEmpty) {
      // عرض القصيدة المخمسة حسب النوع المحدد في الإعدادات
      final mukhammasDisplayType = provider.mukhammasDisplayType;

      switch (mukhammasDisplayType) {
        case MukhammasDisplayType.luxury:
          // النمط الفاخر
          verseDisplay = LuxuryMukhammasDisplay(
            verses: widget.poem.mukhammasVerses!,
            selectedIndex: _activeVerseIndex,
            onVerseSelected: (index) {
              setState(() {
                _activeVerseIndex = index;
              });
            },
            onFullScreenToggle: _toggleFullScreen,
            isFullScreen: _isFullScreen,
          );
          break;

        case MukhammasDisplayType.classic:
          // النمط الكلاسيكي
          verseDisplay = ClassicMukhammasDisplay(
            verses: widget.poem.mukhammasVerses!,
            initialPage: _activeVerseIndex,
            onPageChanged: (index) {
              setState(() {
                _activeVerseIndex = index;
              });
            },
            showPageIndicator: true,
            isFullScreen: _isFullScreen,
            onToggleFullScreen: _toggleFullScreen,
          );
          break;

        case MukhammasDisplayType.standard:
          // النمط القياسي
          verseDisplay = MukhammasVerseDisplay(
            verses: widget.poem.mukhammasVerses!,
            selectedIndex: _activeVerseIndex,
            onVerseSelected: (index) {
              setState(() {
                _activeVerseIndex = index;
              });
            },
            onFullScreenToggle: _toggleFullScreen,
            isFullScreen: _isFullScreen,
          );
          break;
      }
    } else {
      // عرض القصيدة العادية حسب النوع المحدد في الإعدادات
      final verseDisplayType = provider.verseDisplayType;

      switch (verseDisplayType) {
        case VerseDisplayType.bookStyle:
          // نمط الكتاب المفتوح
          verseDisplay = BookStyleVerseDisplay(
            verses: widget.poem.verses,
            selectedIndex: _activeVerseIndex,
            onVerseSelected: (index) {
              // Simplemente actualizamos el índice activo sin ninguna lógica adicional
              // La navegación automática se controla dentro del widget BookStyleVerseDisplay
              setState(() {
                _activeVerseIndex = index;
              });
            },
            onFullScreenToggle: _toggleFullScreen,
            isFullScreen: _isFullScreen,
          );
          break;

        case VerseDisplayType.classic:
          // النمط الكلاسيكي
          verseDisplay = ClassicVerseDisplay(
            verses: widget.poem.verses,
            selectedIndex: _activeVerseIndex,
            onVerseSelected: (index) {
              setState(() {
                _activeVerseIndex = index;
              });
            },
            onFullScreenToggle: _toggleFullScreen,
            isFullScreen: _isFullScreen,
          );
          break;

        case VerseDisplayType.professional:
          // النمط الاحترافي (الافتراضي)
          verseDisplay = ProfessionalVerseDisplay(
            verses: widget.poem.verses,
            selectedIndex: _activeVerseIndex,
            onVerseSelected: (index) {
              setState(() {
                _activeVerseIndex = index;
              });
            },
            onFullScreenToggle: _toggleFullScreen,
            isFullScreen: _isFullScreen,
          );
          break;
      }
    }

    // إذا كان في وضع ملء الشاشة، نعرض المحتوى في شاشة كاملة
    if (_isFullScreen) {
      return AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        color: isDarkMode ? Colors.black : Colors.white,
        child: Stack(
          children: [
            // عارض الأبيات
            Positioned.fill(
              child: verseDisplay,
            ),

            // أزرار التحكم (تظهر فقط للقصائد المخمسة)
            if (widget.poem.isMukhammas) fullScreenControls,
          ],
        ),
      );
    }

    // وإلا نعرض المحتوى العادي
    if (widget.poem.isMukhammas) {
      // للقصائد المخمسة نستخدم ارتفاع أكبر لاستيعاب التصميم الفاخر
      return SizedBox(
        height: MediaQuery.of(context).size.height *
            (isLargeScreen ? 0.75 : (isSmallScreen ? 0.65 : 0.7)),
        child: verseDisplay,
      );
    } else {
      // للقصائد العادية نستخدم الارتفاع المعتاد
      return SizedBox(
        height: MediaQuery.of(context).size.height *
            (isLargeScreen ? 0.65 : (isSmallScreen ? 0.55 : 0.6)),
        child: verseDisplay,
      );
    }
  }

  Widget _buildInfoItem({
    required BuildContext context,
    required IconData icon,
    required String label,
    required String value,
  }) {
    final isDarkMode = context.watch<ManhalRawiProvider>().isDarkMode;

    // الحصول على أبعاد الشاشة للتوافق مع مختلف الأحجام
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;
    final isLargeScreen = screenWidth > 600;

    // تحديد أحجام العناصر بناءً على حجم الشاشة
    final double iconSize =
        isLargeScreen ? 32.0 : (isSmallScreen ? 20.0 : 24.0);
    final double containerPadding =
        isLargeScreen ? 12.0 : (isSmallScreen ? 6.0 : 8.0);
    final double borderWidth = isLargeScreen ? 1.5 : 1.0;
    final double captionFontSize =
        isLargeScreen ? 14.0 : (isSmallScreen ? 10.0 : 12.0);
    final double valueFontSize =
        isLargeScreen ? 16.0 : (isSmallScreen ? 12.0 : 14.0);
    final double spacingHeight =
        isLargeScreen ? 6.0 : (isSmallScreen ? 2.0 : 4.0);

    return Column(
      children: [
        Container(
          padding: EdgeInsets.all(containerPadding),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isDarkMode
                ? ManhalColors.blue800.withValues(alpha: 0.7)
                : ManhalColors.gold100.withValues(alpha: 0.5),
            border: Border.all(
              color: isDarkMode
                  ? ManhalColors.gold500.withValues(alpha: 0.3)
                  : ManhalColors.gold300,
              width: borderWidth,
            ),
          ),
          child: Icon(
            icon,
            color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
            size: iconSize,
          ),
        ),
        SizedBox(height: spacingHeight),
        Text(
          label,
          style: IslamicTypography.luxuryCaption(
            isDark: isDarkMode,
            fontSize: captionFontSize,
          ),
        ),
        SizedBox(height: isSmallScreen ? 1 : 2),
        Text(
          value,
          style: IslamicTypography.luxuryBody(
            isDark: isDarkMode,
            fontSize: valueFontSize,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}
