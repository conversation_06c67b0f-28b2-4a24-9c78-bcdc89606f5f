# دليل استخدام الخطوط في تطبيق المنهل الروي

## مقدمة

تم تحويل جميع الخطوط في تطبيق المنهل الروي من استخدام مكتبة Google Fonts إلى استخدام الخطوط المحلية المخصصة. هذا يوفر عدة مزايا:

1. **أداء أفضل**: لا يحتاج التطبيق إلى تنزيل الخطوط من الإنترنت.
2. **عمل بدون إنترنت**: يمكن للتطبيق العمل بشكل كامل بدون اتصال بالإنترنت.
3. **تحكم أكبر**: يمكنك تخصيص الخطوط وتعديلها حسب الحاجة.
4. **حجم أصغر للتطبيق**: يمكنك تضمين فقط الأوزان والأنماط التي تحتاجها.

## الخطوط المستخدمة

تم تكوين الخطوط التالية في ملف `pubspec.yaml`:

1. **Amiri (أميري)**: خط نسخي أنيق مناسب للعناوين والنصوص.
2. **Cairo (القاهرة)**: خط حديث وواضح مناسب للواجهات والأزرار.
3. **Scheherazade (شهرزاد)**: خط نسخي تقليدي مناسب للأبيات الشعرية.
4. **Naskh (نسخ)**: خط نسخي عالي الجودة مناسب للنصوص.
5. **ReemKufi (ريم كوفي)**: خط كوفي حديث مناسب للعناوين الرئيسية.
6. **ArefRuqaa (عارف رقعة)**: خط رقعة أنيق مناسب للعناوين الفرعية.
7. **Tajawal (تجوال)**: خط حديث مناسب للعناوين الصغيرة والتعليقات.
8. **Lateef (لطيف)**: خط لطيف مناسب للاقتباسات.
9. **Markazi (ماركازي)**: خط مناسب للعناوين والنصوص.

## كيفية إضافة الخطوط

### 1. تنزيل ملفات الخطوط

قم بتنزيل ملفات الخطوط من المصادر التالية:

- [Google Fonts](https://fonts.google.com/)
- [Arabic Typography](https://arabicfonts.org/)
- [Fontsc](https://www.fontsc.com/font/tag/arabic)

### 2. إضافة الخطوط إلى المشروع

1. قم بإنشاء مجلد `assets/fonts/` إذا لم يكن موجوداً.
2. انسخ ملفات الخطوط (بصيغة TTF أو OTF) إلى هذا المجلد.
3. تأكد من تسمية الملفات بشكل صحيح، على سبيل المثال:
   - `Amiri-Regular.ttf`
   - `Amiri-Bold.ttf`
   - `Cairo-Regular.ttf`
   - إلخ.

### 3. تكوين الخطوط في ملف pubspec.yaml

قم بتعديل ملف `pubspec.yaml` لتضمين الخطوط:

```yaml
flutter:
  fonts:
    # 1. Amiri
    - family: Amiri
      fonts:
        - asset: assets/fonts/Amiri-Regular.ttf
          weight: 400
        - asset: assets/fonts/Amiri-Bold.ttf
          weight: 700
        - asset: assets/fonts/Amiri-Italic.ttf
          weight: 400
          style: italic
        - asset: assets/fonts/Amiri-BoldItalic.ttf
          weight: 700
          style: italic
    
    # 2. Cairo
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
          weight: 400
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
        - asset: assets/fonts/Cairo-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Cairo-Light.ttf
          weight: 300
    
    # وهكذا لبقية الخطوط...
```

### 4. تحديث الموارد

بعد تعديل ملف `pubspec.yaml`، قم بتنفيذ الأمر التالي:

```
flutter pub get
```

## استخدام الخطوط في التطبيق

تم تعديل ملف `islamic_typography.dart` لاستخدام الخطوط المحلية بدلاً من Google Fonts. يمكنك استخدام الخطوط كما يلي:

```dart
Text(
  'عنوان رئيسي',
  style: IslamicTypography.luxuryTitle(
    isDark: isDarkMode,
    fontSize: 32,
    fontFamily: 'Amiri',
  ),
)
```

## الخطوط والأنماط المتاحة

### 1. luxuryTitle - خط العنوان الرئيسي الفاخر
```dart
IslamicTypography.luxuryTitle(
  isDark: isDarkMode,
  fontSize: 32,
  fontWeight: FontWeight.bold,
  fontFamily: 'Amiri', // يمكن استخدام: 'Amiri', 'Cairo', 'Markazi', 'Raqq', 'Naskh'
)
```

### 2. luxurySubtitle - خط العنوان الفرعي الفاخر
```dart
IslamicTypography.luxurySubtitle(
  isDark: isDarkMode,
  fontSize: 22,
  fontWeight: FontWeight.w600,
)
```

### 3. luxuryVerse - خط الأبيات الشعرية الفاخر
```dart
IslamicTypography.luxuryVerse(
  isDark: isDarkMode,
  fontSize: 20,
  fontWeight: FontWeight.w500,
  fontFamily: 'Scheherazade', // يمكن استخدام: 'Amiri', 'Cairo', 'Markazi', 'Raqq', 'Naskh'
)
```

### 4. luxuryText - خط النص العادي الفاخر
```dart
IslamicTypography.luxuryText(
  isDark: isDarkMode,
  fontSize: 16,
  fontWeight: FontWeight.normal,
  fontFamily: 'Naskh', // يمكن استخدام: 'Amiri', 'Cairo', 'Markazi', 'Raqq', 'Naskh'
)
```

### 5. luxuryCaption - خط العناوين الصغيرة الفاخر
```dart
IslamicTypography.luxuryCaption(
  isDark: isDarkMode,
  fontSize: 14,
  fontWeight: FontWeight.w500,
  fontFamily: 'Tajawal', // يمكن استخدام: 'Amiri', 'Cairo', 'Markazi', 'Raqq', 'Naskh', 'Tajawal'
)
```

### 6. luxuryQuote - خط الاقتباسات الفاخر
```dart
IslamicTypography.luxuryQuote(
  isDark: isDarkMode,
  fontSize: 18,
  fontStyle: FontStyle.italic,
)
```

### 7. luxuryButton - خط الأزرار الفاخر
```dart
IslamicTypography.luxuryButton(
  isDark: isDarkMode,
  fontSize: 16,
  fontWeight: FontWeight.bold,
)
```

### 8. luxuryBookTitle - خط عنوان الكتاب الفاخر
```dart
IslamicTypography.luxuryBookTitle(
  isDark: isDarkMode,
  fontSize: 36,
  fontWeight: FontWeight.bold,
)
```

### 9. luxuryAuthorName - خط اسم المؤلف الفاخر
```dart
IslamicTypography.luxuryAuthorName(
  isDark: isDarkMode,
  fontSize: 24,
  fontWeight: FontWeight.w600,
)
```

### 10. luxuryPoemTitle - خط عنوان القصيدة الفاخر
```dart
IslamicTypography.luxuryPoemTitle(
  isDark: isDarkMode,
  fontSize: 26,
  fontWeight: FontWeight.bold,
)
```

### 11. luxuryCategory - خط التصنيفات الفاخر
```dart
IslamicTypography.luxuryCategory(
  isDark: isDarkMode,
  fontSize: 16,
  fontWeight: FontWeight.w600,
)
```

## حل المشاكل الشائعة

### 1. الخط لا يظهر بشكل صحيح

- تأكد من أن اسم الخط في `fontFamily` يطابق تماماً الاسم المحدد في ملف `pubspec.yaml`.
- تأكد من تنفيذ `flutter pub get` بعد تعديل ملف `pubspec.yaml`.
- تأكد من أن ملفات الخط موجودة في المسار الصحيح.
- جرب إعادة تشغيل التطبيق بالكامل (وليس فقط hot reload).

### 2. الخط يظهر بشكل مختلف على أجهزة مختلفة

بعض الأجهزة قد لا تدعم بعض الخطوط بشكل كامل. في هذه الحالة، يمكنك:

- توفير خط بديل في حالة عدم توفر الخط الأساسي.
- استخدام خطوط أكثر توافقاً مثل Amiri أو Cairo.

### 3. حجم التطبيق كبير جداً

إذا كان حجم التطبيق كبيراً جداً بسبب الخطوط، يمكنك:

- تضمين فقط الأوزان والأنماط التي تحتاجها.
- استخدام أدوات مثل `fonttools` لتقليل حجم ملفات الخط.
- استخدام صيغة WOFF2 بدلاً من TTF إذا كان ذلك ممكناً.

## ملاحظات إضافية

- يمكنك إضافة المزيد من الخطوط حسب الحاجة.
- يمكنك تخصيص الخطوط الموجودة أو إنشاء أنماط جديدة.
- تأكد من اختبار التطبيق على مجموعة متنوعة من الأجهزة للتأكد من توافق الخطوط.
