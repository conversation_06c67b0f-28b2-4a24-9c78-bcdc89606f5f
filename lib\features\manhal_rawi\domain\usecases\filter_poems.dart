import '../../data/models/poem.dart';
import '../../data/repositories/poems_repository.dart';

class FilterPoems {
  final PoemsRepository repository;

  FilterPoems(this.repository);

  Future<List<Poem>> byCategory(int categoryId) async {
    return await repository.getPoemsByCategoryId(categoryId);
  }

  Future<List<Poem>> byPoet(int poetId) async {
    return await repository.getPoemsByPoetId(poetId);
  }
}
