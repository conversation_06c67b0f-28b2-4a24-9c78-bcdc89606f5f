import '../models/poem.dart';
import '../models/poet.dart';
import '../models/category.dart';
import '../models/book_info.dart';
import 'database_helper.dart';

class LocalDataSource {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  Future<BookInfo> getBookInfo() async {
    try {
      return await _databaseHelper.getBookInfo();
    } catch (e) {
      throw Exception('فشل في تحميل معلومات الكتاب: $e');
    }
  }

  Future<List<Poet>> getPoets() async {
    try {
      return await _databaseHelper.getPoets();
    } catch (e) {
      throw Exception('فشل في تحميل الشعراء: $e');
    }
  }

  Future<List<Category>> getCategories() async {
    try {
      return await _databaseHelper.getCategories();
    } catch (e) {
      throw Exception('فشل في تحميل التصنيفات: $e');
    }
  }

  Future<List<Poem>> getPoems() async {
    try {
      return await _databaseHelper.getPoems();
    } catch (e) {
      throw Exception('فشل في تحميل القصائد: $e');
    }
  }

  Future<Poet?> getPoetById(int id) async {
    try {
      final poets = await getPoets();
      return poets.firstWhere((poet) => poet.id == id);
    } catch (e) {
      return null;
    }
  }

  Future<Category?> getCategoryById(int id) async {
    try {
      final categories = await getCategories();
      return categories.firstWhere((category) => category.id == id);
    } catch (e) {
      return null;
    }
  }

  Future<Poem?> getPoemById(int id) async {
    try {
      final poems = await getPoems();
      return poems.firstWhere((poem) => poem.id == id);
    } catch (e) {
      return null;
    }
  }

  Future<List<Poem>> getPoemsByPoetId(int poetId) async {
    try {
      return await _databaseHelper.getPoemsByPoetId(poetId);
    } catch (e) {
      throw Exception('فشل في تحميل قصائد الشاعر: $e');
    }
  }

  Future<List<Poem>> getPoemsByCategoryId(int categoryId) async {
    try {
      return await _databaseHelper.getPoemsByCategoryId(categoryId);
    } catch (e) {
      throw Exception('فشل في تحميل قصائد التصنيف: $e');
    }
  }

  Future<List<Poem>> searchPoems(String query) async {
    try {
      return await _databaseHelper.searchPoems(query);
    } catch (e) {
      throw Exception('فشل في البحث عن القصائد: $e');
    }
  }
}
