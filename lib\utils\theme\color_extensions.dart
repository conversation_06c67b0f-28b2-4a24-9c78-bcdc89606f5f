import 'package:flutter/material.dart';

/// امتداد للألوان لتوفير طريقة withValues
extension ColorExtension on Color {
  /// إنشاء لون جديد مع قيم محددة
  /// يمكن تحديد قيمة alpha أو red أو green أو blue
  Color withValues({
    double? alpha,
    int? red,
    int? green,
    int? blue,
  }) {
    return Color.fromARGB(
      alpha != null ? (alpha * 255).round().clamp(0, 255) : a.toInt(),
      red ?? r.toInt(),
      green ?? g.toInt(),
      blue ?? b.toInt(),
    );
  }
}
