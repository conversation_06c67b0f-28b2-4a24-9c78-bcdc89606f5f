import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../theme/manhal_colors.dart';

/// مكتبة الرسوم المتحركة الفاخرة
class LuxuryAnimations {
  /// تأثير ظهور مع توهج ذهبي
  static Widget goldenReveal({
    required bool isDark,
    required Widget child,
    int durationMs = 1500,
    int delayMs = 0,
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: Duration(milliseconds: durationMs),
      curve: Curves.easeOutCubic,
      builder: (context, value, child) {
        final glowColor = isDark ? ManhalColors.gold500 : ManhalColors.primary;
        final glowOpacity = math.sin(value * math.pi) * 0.5;

        return Opacity(
          opacity: value,
          child: Transform.scale(
            scale: 0.8 + (0.2 * value),
            child: Container(
              decoration: BoxDecoration(
                boxShadow: [
                  BoxShadow(
                    color: glowColor.withValues(alpha: glowOpacity),
                    blurRadius: 30 * value,
                    spreadRadius: 5 * value,
                  ),
                ],
              ),
              child: child,
            ),
          ),
        );
      },
      child: child,
    );
  }

  /// تأثير ظهور الزخارف الإسلامية
  static Widget islamicPatternReveal({
    required bool isDark,
    required Widget child,
    int durationMs = 2000,
  }) {
    return _IslamicPatternRevealAnimation(
      isDark: isDark,
      durationMs: durationMs,
      child: child,
    );
  }

  /// تأثير دوران ثلاثي الأبعاد
  static Widget rotate3D({
    required Widget child,
    required bool isDark,
    int durationMs = 2500,
    String axis = 'y', // استخدام String بدلاً من Axis
    double maxRotation = 0.1,
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: Duration(milliseconds: durationMs),
      curve: Curves.easeInOutBack,
      builder: (context, value, child) {
        final rotation = math.sin(value * math.pi * 2) * maxRotation;

        return Transform(
          alignment: Alignment.center,
          transform: Matrix4.identity()
            ..setEntry(3, 2, 0.001)
            ..rotateX(axis == 'x' ? rotation : 0)
            ..rotateY(axis == 'y' ? rotation : 0)
            ..rotateZ(axis == 'z' ? rotation : 0),
          child: child,
        );
      },
      child: child,
    );
  }

  /// تأثير نبض مع توهج
  static Widget pulseWithGlow({
    required bool isDark,
    required Widget child,
    int durationMs = 3000,
    double minScale = 0.95,
    double maxScale = 1.05,
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.0, end: 1.0),
      duration: Duration(milliseconds: durationMs),
      curve: Curves.linear,
      builder: (context, value, child) {
        final sinValue = math.sin(value * math.pi * 2);
        final scale = minScale + ((sinValue + 1) / 2) * (maxScale - minScale);

        final glowColor = isDark ? ManhalColors.gold500 : ManhalColors.primary;
        final glowOpacity = ((sinValue + 1) / 2) * 0.3;

        return Transform.scale(
          scale: scale,
          child: Container(
            decoration: BoxDecoration(
              boxShadow: [
                BoxShadow(
                  color: glowColor.withValues(alpha: glowOpacity),
                  blurRadius: 20,
                  spreadRadius: 2,
                ),
              ],
            ),
            child: child,
          ),
        );
      },
      child: child,
    );
  }

  /// تأثير ظهور متتابع للعناصر
  static Widget staggeredFadeIn({
    required bool isDark,
    required List<Widget> children,
    int baseDurationMs = 400,
    int staggerMs = 200,
    Curve curve = Curves.easeOutCubic,
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.center,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
  }) {
    return _StaggeredFadeInAnimation(
      isDark: isDark,
      baseDurationMs: baseDurationMs,
      staggerMs: staggerMs,
      curve: curve,
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      children: children,
    );
  }

  /// تأثير ظهور النص مع تأثيرات متعددة
  static Widget luxuryTextReveal({
    required String text,
    required TextStyle style,
    required bool isDark,
    int durationMs = 2000,
    int delayMs = 0,
    TextAlign textAlign = TextAlign.center,
  }) {
    return _LuxuryTextRevealAnimation(
      text: text,
      style: style,
      isDark: isDark,
      durationMs: durationMs,
      delayMs: delayMs,
      textAlign: textAlign,
    );
  }

  /// تأثير ظهور الجسيمات الذهبية
  static Widget goldenParticles({
    required bool isDark,
    required Widget child,
    int durationMs = 5000,
    int particleCount = 20,
  }) {
    return _GoldenParticlesAnimation(
      isDark: isDark,
      durationMs: durationMs,
      particleCount: particleCount,
      child: child,
    );
  }
}

/// مكون تحريك ظهور الزخارف الإسلامية
class _IslamicPatternRevealAnimation extends StatefulWidget {
  final Widget child;
  final bool isDark;
  final int durationMs;

  const _IslamicPatternRevealAnimation({
    required this.child,
    required this.isDark,
    required this.durationMs,
  });

  @override
  State<_IslamicPatternRevealAnimation> createState() =>
      _IslamicPatternRevealAnimationState();
}

class _IslamicPatternRevealAnimationState
    extends State<_IslamicPatternRevealAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: widget.durationMs),
    );

    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutCubic,
    );

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return CustomPaint(
          painter: _IslamicPatternPainter(
            progress: _animation.value,
            isDark: widget.isDark,
          ),
          child: child,
        );
      },
      child: widget.child,
    );
  }
}

/// رسام الزخارف الإسلامية
class _IslamicPatternPainter extends CustomPainter {
  final double progress;
  final bool isDark;

  _IslamicPatternPainter({
    required this.progress,
    required this.isDark,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final baseColor = isDark ? ManhalColors.gold500 : ManhalColors.primary;
    final paint = Paint()
      ..color = baseColor.withValues(alpha: 0.15 * progress)
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;

    final width = size.width;
    final height = size.height;

    // رسم الزخارف في الزوايا
    _drawCornerPattern(canvas, paint, Offset(0, 0), size, progress);
    _drawCornerPattern(canvas, paint, Offset(width, 0), size, progress);
    _drawCornerPattern(canvas, paint, Offset(0, height), size, progress);
    _drawCornerPattern(canvas, paint, Offset(width, height), size, progress);

    // رسم الزخارف في المنتصف
    _drawCenterPattern(
        canvas, paint, Offset(width / 2, height / 2), size, progress);

    // رسم إطار زخرفي
    final borderRect = Rect.fromLTWH(
      width * 0.05,
      height * 0.05,
      width * 0.9,
      height * 0.9,
    );

    final borderPath = Path();
    borderPath.addRRect(RRect.fromRectAndRadius(
      borderRect,
      Radius.circular(20),
    ));

    canvas.drawPath(borderPath, paint);
  }

  void _drawCornerPattern(
      Canvas canvas, Paint paint, Offset corner, Size size, double progress) {
    final patternSize = size.width * 0.25 * progress;
    final center = Offset(
      corner.dx == 0
          ? corner.dx + patternSize / 2
          : corner.dx - patternSize / 2,
      corner.dy == 0
          ? corner.dy + patternSize / 2
          : corner.dy - patternSize / 2,
    );

    // رسم الزخرفة الإسلامية الهندسية
    final path = Path();
    final sides = 8;
    final radius = patternSize / 2;

    for (int i = 0; i < sides; i++) {
      final angle = (i * 2 * math.pi) / sides;
      final point = Offset(
        center.dx + radius * 0.8 * math.cos(angle),
        center.dy + radius * 0.8 * math.sin(angle),
      );

      if (i == 0) {
        path.moveTo(point.dx, point.dy);
      } else {
        path.lineTo(point.dx, point.dy);
      }
    }

    path.close();
    canvas.drawPath(path, paint);

    // رسم الزخرفة الداخلية
    final innerPath = Path();
    final innerRadius = radius * 0.6;

    for (int i = 0; i < sides; i++) {
      final angle = (i * 2 * math.pi) / sides + math.pi / sides;
      final point = Offset(
        center.dx + innerRadius * math.cos(angle),
        center.dy + innerRadius * math.sin(angle),
      );

      if (i == 0) {
        innerPath.moveTo(point.dx, point.dy);
      } else {
        innerPath.lineTo(point.dx, point.dy);
      }
    }

    innerPath.close();
    canvas.drawPath(innerPath, paint);

    // ربط النقاط
    for (int i = 0; i < sides; i++) {
      final outerAngle = (i * 2 * math.pi) / sides;
      final innerAngle = (i * 2 * math.pi) / sides + math.pi / sides;

      final outerPoint = Offset(
        center.dx + radius * 0.8 * math.cos(outerAngle),
        center.dy + radius * 0.8 * math.sin(outerAngle),
      );

      final innerPoint = Offset(
        center.dx + innerRadius * math.cos(innerAngle),
        center.dy + innerRadius * math.sin(innerAngle),
      );

      canvas.drawLine(outerPoint, innerPoint, paint);
    }
  }

  void _drawCenterPattern(
      Canvas canvas, Paint paint, Offset center, Size size, double progress) {
    // تم إزالة رسم النجمة بناءً على طلب المستخدم
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}

/// مكون تحريك ظهور متتابع للعناصر
class _StaggeredFadeInAnimation extends StatefulWidget {
  final List<Widget> children;
  final bool isDark;
  final int baseDurationMs;
  final int staggerMs;
  final Curve curve;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;

  const _StaggeredFadeInAnimation({
    required this.children,
    required this.isDark,
    required this.baseDurationMs,
    required this.staggerMs,
    required this.curve,
    required this.mainAxisAlignment,
    required this.crossAxisAlignment,
  });

  @override
  State<_StaggeredFadeInAnimation> createState() =>
      _StaggeredFadeInAnimationState();
}

class _StaggeredFadeInAnimationState extends State<_StaggeredFadeInAnimation> {
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: widget.mainAxisAlignment,
      crossAxisAlignment: widget.crossAxisAlignment,
      children: List.generate(
        widget.children.length,
        (index) {
          return TweenAnimationBuilder<double>(
            tween: Tween<double>(begin: 0.0, end: 1.0),
            duration: Duration(
                milliseconds:
                    widget.baseDurationMs + (index * widget.staggerMs)),
            curve: widget.curve,
            builder: (context, value, child) {
              return Opacity(
                opacity: value,
                child: Transform.translate(
                  offset: Offset(0, 20 * (1 - value)),
                  child: child,
                ),
              );
            },
            child: widget.children[index],
          );
        },
      ),
    );
  }
}

/// مكون تحريك ظهور النص مع تأثيرات متعددة
class _LuxuryTextRevealAnimation extends StatefulWidget {
  final String text;
  final TextStyle style;
  final bool isDark;
  final int durationMs;
  final int delayMs;
  final TextAlign textAlign;

  const _LuxuryTextRevealAnimation({
    required this.text,
    required this.style,
    required this.isDark,
    required this.durationMs,
    required this.delayMs,
    required this.textAlign,
  });

  @override
  State<_LuxuryTextRevealAnimation> createState() =>
      _LuxuryTextRevealAnimationState();
}

class _LuxuryTextRevealAnimationState extends State<_LuxuryTextRevealAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: widget.durationMs),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
      ),
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.2, 0.7, curve: Curves.easeOutBack),
      ),
    );

    _glowAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.5, 1.0, curve: Curves.easeInOut),
      ),
    );

    if (widget.delayMs > 0) {
      Future.delayed(Duration(milliseconds: widget.delayMs), () {
        if (mounted) {
          _controller.forward();
        }
      });
    } else {
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        final glowColor =
            widget.isDark ? ManhalColors.gold500 : ManhalColors.primary;
        final glowOpacity = math.sin(_glowAnimation.value * math.pi) * 0.3;

        return Opacity(
          opacity: _fadeAnimation.value,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              decoration: BoxDecoration(
                boxShadow: [
                  BoxShadow(
                    color: glowColor.withValues(alpha: glowOpacity),
                    blurRadius: 20,
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: Text(
                widget.text,
                style: widget.style,
                textAlign: widget.textAlign,
                textDirection: TextDirection.rtl,
              ),
            ),
          ),
        );
      },
    );
  }
}

/// مكون تحريك الجسيمات الذهبية
class _GoldenParticlesAnimation extends StatefulWidget {
  final Widget child;
  final bool isDark;
  final int durationMs;
  final int particleCount;

  const _GoldenParticlesAnimation({
    required this.child,
    required this.isDark,
    required this.durationMs,
    required this.particleCount,
  });

  @override
  State<_GoldenParticlesAnimation> createState() =>
      _GoldenParticlesAnimationState();
}

class _GoldenParticlesAnimationState extends State<_GoldenParticlesAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late List<_Particle> _particles;
  final _random = math.Random();

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: widget.durationMs),
    )..repeat();

    // إنشاء الجسيمات
    _particles = List.generate(
      widget.particleCount,
      (_) => _Particle(
        position: Offset(
          _random.nextDouble(),
          _random.nextDouble(),
        ),
        speed: 0.2 + _random.nextDouble() * 0.3,
        size: 1 + _random.nextDouble() * 3,
        angle: _random.nextDouble() * 2 * math.pi,
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return CustomPaint(
          painter: _ParticlesPainter(
            particles: _particles,
            progress: _controller.value,
            isDark: widget.isDark,
          ),
          child: child,
        );
      },
      child: widget.child,
    );
  }
}

/// جسيم ذهبي
class _Particle {
  Offset position; // موقع الجسيم (0-1)
  final double speed; // سرعة الجسيم
  final double size; // حجم الجسيم
  final double angle; // زاوية حركة الجسيم

  _Particle({
    required this.position,
    required this.speed,
    required this.size,
    required this.angle,
  });

  void update(double progress) {
    // تحديث موقع الجسيم بناءً على الزاوية والسرعة
    position = Offset(
      (position.dx + math.cos(angle) * speed * 0.01) % 1.0,
      (position.dy + math.sin(angle) * speed * 0.01) % 1.0,
    );
  }
}

/// رسام الجسيمات الذهبية
class _ParticlesPainter extends CustomPainter {
  final List<_Particle> particles;
  final double progress;
  final bool isDark;

  _ParticlesPainter({
    required this.particles,
    required this.progress,
    required this.isDark,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final baseColor = isDark ? ManhalColors.gold500 : ManhalColors.primary;

    // تحديث وعرض كل جسيم
    for (final particle in particles) {
      particle.update(progress);

      final paint = Paint()
        ..color = baseColor.withValues(
            alpha: 0.3 + math.sin(progress * math.pi * 2) * 0.2)
        ..style = PaintingStyle.fill;

      final position = Offset(
        particle.position.dx * size.width,
        particle.position.dy * size.height,
      );

      canvas.drawCircle(position, particle.size, paint);
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}
