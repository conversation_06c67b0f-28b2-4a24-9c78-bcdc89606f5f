import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../../../../../utils/theme/manhal_colors.dart';

/// مكون للزخارف الإسلامية
class IslamicPattern extends StatelessWidget {
  final double size;
  final Color? color;
  final double strokeWidth;
  final bool isDark;
  final PatternType patternType;
  final double opacity;

  const IslamicPattern({
    super.key,
    this.size = 100,
    this.color,
    this.strokeWidth = 1.5,
    this.isDark = false,
    this.patternType = PatternType.geometric,
    this.opacity = 1.0,
  });

  @override
  Widget build(BuildContext context) {
    final patternColor = color ??
        (isDark
            ? ManhalColors.gold500.withValues(alpha: opacity)
            : ManhalColors.primary.withValues(alpha: opacity));

    return SizedBox(
      width: size,
      height: size,
      child: CustomPaint(
        painter: IslamicPatternPainter(
          color: patternColor,
          strokeWidth: strokeWidth,
          patternType: patternType,
        ),
      ),
    );
  }
}

/// أنواع الزخارف الإسلامية
enum PatternType {
  geometric,
  floral,
  star,
  arabesque,
}

/// رسام الزخارف الإسلامية
class IslamicPatternPainter extends CustomPainter {
  final Color color;
  final double strokeWidth;
  final PatternType patternType;

  IslamicPatternPainter({
    required this.color,
    required this.strokeWidth,
    required this.patternType,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    switch (patternType) {
      case PatternType.geometric:
        _drawGeometricPattern(canvas, size, paint);
        break;
      case PatternType.floral:
        _drawFloralPattern(canvas, size, paint);
        break;
      case PatternType.star:
        _drawStarPattern(canvas, size, paint);
        break;
      case PatternType.arabesque:
        _drawArabesquePattern(canvas, size, paint);
        break;
    }
  }

  /// رسم نمط هندسي
  void _drawGeometricPattern(Canvas canvas, Size size, Paint paint) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = math.min(size.width, size.height) / 2;

    // رسم الدائرة الخارجية
    canvas.drawCircle(center, radius - strokeWidth, paint);

    // رسم المثمن
    final path = Path();
    final sides = 8;

    for (int i = 0; i < sides; i++) {
      final angle = 2 * math.pi * i / sides - math.pi / 8;
      final x = center.dx + radius * 0.8 * math.cos(angle);
      final y = center.dy + radius * 0.8 * math.sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }

    path.close();
    canvas.drawPath(path, paint);

    // رسم النجمة الداخلية
    final starPath = Path();

    for (int i = 0; i < sides; i++) {
      final outerAngle = 2 * math.pi * i / sides - math.pi / 8;
      final innerAngle = 2 * math.pi * (i + 0.5) / sides - math.pi / 8;

      final outerX = center.dx + radius * 0.8 * math.cos(outerAngle);
      final outerY = center.dy + radius * 0.8 * math.sin(outerAngle);

      final innerX = center.dx + radius * 0.4 * math.cos(innerAngle);
      final innerY = center.dy + radius * 0.4 * math.sin(innerAngle);

      if (i == 0) {
        starPath.moveTo(outerX, outerY);
      } else {
        starPath.lineTo(outerX, outerY);
      }

      starPath.lineTo(innerX, innerY);
    }

    starPath.close();
    canvas.drawPath(starPath, paint);

    // رسم الدائرة الداخلية
    canvas.drawCircle(center, radius * 0.2, paint);
  }

  /// رسم نمط زهري
  void _drawFloralPattern(Canvas canvas, Size size, Paint paint) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = math.min(size.width, size.height) / 2;

    // رسم الدائرة الخارجية
    canvas.drawCircle(center, radius - strokeWidth, paint);

    // رسم البتلات
    final petalCount = 8;
    final petalPath = Path();

    for (int i = 0; i < petalCount; i++) {
      final angle = 2 * math.pi * i / petalCount;
      final startAngle = angle - math.pi / petalCount;
      final endAngle = angle + math.pi / petalCount;

      final startX = center.dx + radius * 0.7 * math.cos(startAngle);
      final startY = center.dy + radius * 0.7 * math.sin(startAngle);

      final endX = center.dx + radius * 0.7 * math.cos(endAngle);
      final endY = center.dy + radius * 0.7 * math.sin(endAngle);

      final peakX = center.dx + radius * 0.9 * math.cos(angle);
      final peakY = center.dy + radius * 0.9 * math.sin(angle);

      petalPath.moveTo(center.dx, center.dy);
      petalPath.quadraticBezierTo(startX, startY, peakX, peakY);
      petalPath.quadraticBezierTo(endX, endY, center.dx, center.dy);
    }

    canvas.drawPath(petalPath, paint);

    // رسم الدائرة الداخلية
    canvas.drawCircle(center, radius * 0.3, paint);

    // رسم النقاط الزخرفية
    final dotPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    for (int i = 0; i < petalCount * 2; i++) {
      final angle = 2 * math.pi * i / (petalCount * 2);
      final x = center.dx + radius * 0.5 * math.cos(angle);
      final y = center.dy + radius * 0.5 * math.sin(angle);

      canvas.drawCircle(Offset(x, y), strokeWidth, dotPaint);
    }
  }

  /// رسم نمط نجمي
  void _drawStarPattern(Canvas canvas, Size size, Paint paint) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = math.min(size.width, size.height) / 2;

    // رسم النجمة الثمانية
    final starPath = Path();
    final points = 8;

    for (int i = 0; i < points * 2; i++) {
      final angle = math.pi * i / points;
      final r = i.isEven ? radius : radius * 0.4;

      final x = center.dx + r * math.cos(angle);
      final y = center.dy + r * math.sin(angle);

      if (i == 0) {
        starPath.moveTo(x, y);
      } else {
        starPath.lineTo(x, y);
      }
    }

    starPath.close();
    canvas.drawPath(starPath, paint);

    // رسم الدائرة الداخلية
    canvas.drawCircle(center, radius * 0.2, paint);

    // رسم الخطوط الداخلية
    for (int i = 0; i < points; i++) {
      final angle = math.pi * i / (points / 2);

      final x1 = center.dx + radius * 0.2 * math.cos(angle);
      final y1 = center.dy + radius * 0.2 * math.sin(angle);

      final x2 = center.dx + radius * 0.4 * math.cos(angle);
      final y2 = center.dy + radius * 0.4 * math.sin(angle);

      canvas.drawLine(Offset(x1, y1), Offset(x2, y2), paint);
    }
  }

  /// رسم نمط أرابيسك
  void _drawArabesquePattern(Canvas canvas, Size size, Paint paint) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = math.min(size.width, size.height) / 2;

    // رسم الدائرة الخارجية
    canvas.drawCircle(center, radius - strokeWidth, paint);

    // رسم الزخارف المتموجة
    final wavePath = Path();
    final waveCount = 16;

    for (int i = 0; i < waveCount; i++) {
      final startAngle = 2 * math.pi * i / waveCount;
      final endAngle = 2 * math.pi * (i + 1) / waveCount;

      final startX = center.dx + radius * 0.8 * math.cos(startAngle);
      final startY = center.dy + radius * 0.8 * math.sin(startAngle);

      final endX = center.dx + radius * 0.8 * math.cos(endAngle);
      final endY = center.dy + radius * 0.8 * math.sin(endAngle);

      final midAngle = (startAngle + endAngle) / 2;
      final midRadius = radius * 0.6;

      final controlX = center.dx + midRadius * math.cos(midAngle);
      final controlY = center.dy + midRadius * math.sin(midAngle);

      wavePath.moveTo(startX, startY);
      wavePath.quadraticBezierTo(controlX, controlY, endX, endY);
    }

    canvas.drawPath(wavePath, paint);

    // رسم الدوائر الداخلية
    canvas.drawCircle(center, radius * 0.6, paint);
    canvas.drawCircle(center, radius * 0.4, paint);
    canvas.drawCircle(center, radius * 0.2, paint);

    // رسم الخطوط المتقاطعة
    for (int i = 0; i < 4; i++) {
      final angle = math.pi * i / 4;

      final x1 = center.dx + radius * math.cos(angle);
      final y1 = center.dy + radius * math.sin(angle);

      final x2 = center.dx - radius * math.cos(angle);
      final y2 = center.dy - radius * math.sin(angle);

      canvas.drawLine(Offset(x1, y1), Offset(x2, y2), paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
