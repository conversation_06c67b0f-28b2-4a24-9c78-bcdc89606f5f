import 'package:flutter/material.dart';
import 'manhal_colors.dart';

class ManhalDecorations {
  // زخرفة للبطاقات
  static BoxDecoration cardDecoration({bool isDark = false}) {
    return BoxDecoration(
      color: isDark ? ManhalColors.blue900.withValues(alpha: 0.7) : Colors.white,
      borderRadius: BorderRadius.circular(16),
      boxShadow: [
        BoxShadow(
          color: isDark 
              ? Colors.black.withValues(alpha: 0.3) 
              : Colors.black.withValues(alpha: 0.1),
          blurRadius: 10,
          offset: const Offset(0, 4),
        ),
      ],
      border: Border.all(
        color: isDark 
            ? ManhalColors.gold600.withValues(alpha: 0.3) 
            : ManhalColors.gold300,
        width: 1,
      ),
    );
  }

  // زخرفة للبطاقات الفخمة
  static BoxDecoration luxuryCardDecoration({bool isDark = false}) {
    return BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topRight,
        end: Alignment.bottomLeft,
        colors: isDark 
            ? [ManhalColors.blue900, ManhalColors.blue800] 
            : [ManhalColors.gold100, Colors.white],
      ),
      borderRadius: BorderRadius.circular(16),
      boxShadow: [
        BoxShadow(
          color: isDark 
              ? Colors.black.withValues(alpha: 0.4) 
              : Colors.black.withValues(alpha: 0.1),
          blurRadius: 15,
          offset: const Offset(0, 5),
        ),
      ],
      border: Border.all(
        color: isDark 
            ? ManhalColors.gold500.withValues(alpha: 0.5) 
            : ManhalColors.gold400,
        width: 1.5,
      ),
    );
  }

  // زخرفة للأزرار
  static BoxDecoration buttonDecoration({bool isPrimary = true, bool isDark = false}) {
    return BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topRight,
        end: Alignment.bottomLeft,
        colors: isPrimary 
            ? [ManhalColors.gold500, ManhalColors.gold600] 
            : [ManhalColors.blue500, ManhalColors.blue600],
      ),
      borderRadius: BorderRadius.circular(12),
      boxShadow: [
        BoxShadow(
          color: (isPrimary ? ManhalColors.gold600 : ManhalColors.blue600).withValues(alpha: 0.3),
          blurRadius: 8,
          offset: const Offset(0, 3),
        ),
      ],
    );
  }

  // زخرفة للخلفية
  static BoxDecoration backgroundDecoration({bool isDark = false}) {
    return BoxDecoration(
      color: isDark ? ManhalColors.backgroundDark : ManhalColors.backgroundLight,
      image: DecorationImage(
        image: AssetImage(
          isDark 
              ? 'assets/images/background_pattern_dark.png' 
              : 'assets/images/background_pattern_light.png',
        ),
        opacity: 0.05,
        repeat: ImageRepeat.repeat,
      ),
    );
  }

  // زخرفة لبيت الشعر
  static BoxDecoration verseDecoration({bool isDark = false}) {
    return BoxDecoration(
      color: isDark 
          ? ManhalColors.blue900.withValues(alpha: 0.6) 
          : ManhalColors.gold100.withValues(alpha: 0.7),
      borderRadius: BorderRadius.circular(12),
      border: Border.all(
        color: isDark 
            ? ManhalColors.gold600.withValues(alpha: 0.3) 
            : ManhalColors.gold400.withValues(alpha: 0.5),
        width: 1,
      ),
    );
  }
}
