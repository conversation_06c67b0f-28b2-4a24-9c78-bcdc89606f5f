# دليل التصنيفات المتعددة

## 🎯 نظرة عامة

يدعم تطبيق المنهل الراوي الآن نظام التصنيفات المتعددة، مما يعني أن القصيدة الواحدة يمكن أن تنتمي إلى أكثر من تصنيف واحد.

## 📊 بنية النظام

### 1. قاعدة البيانات

#### جدول `poem_categories` (جديد):
```sql
CREATE TABLE poem_categories(
  poem_id INTEGER,
  category_id INTEGER,
  PRIMARY KEY (poem_id, category_id),
  FOREIGN KEY (poem_id) REFERENCES poems (id) ON DELETE CASCADE,
  FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE CASCADE
)
```

#### جدول `poems` (محدث):
- يحتفظ بـ `category_id` للتوافق مع النظام القديم
- يضاف `categoryIds` في نموذج Dart

### 2. نموذج البيانات

```dart
class Poem {
  final int categoryId;        // التصنيف الأساسي
  final List<int> categoryIds; // جميع التصنيفات
  // باقي الخصائص...
}
```

## 📝 كيفية الاستخدام في ملفات JSON

### 1. قصيدة بتصنيف واحد (الطريقة القديمة):
```json
{
  "id": 1,
  "title": "قصيدة مدح",
  "categoryId": 2,
  "verses": [...]
}
```

### 2. قصيدة بتصنيفات متعددة (الطريقة الجديدة):
```json
{
  "id": 2,
  "title": "قصيدة مدح وحكمة",
  "categoryId": 2,
  "categoryIds": [2, 4],
  "verses": [...]
}
```

### 3. قصيدة مخمسة بتصنيفات متعددة:
```json
{
  "id": 3,
  "title": "مخمس في المدح",
  "categoryId": 2,
  "categoryIds": [2, 5],
  "type": "mukhammas",
  "mukhammasVerses": [...]
}
```

## 🔄 آلية العمل

### 1. تحميل البيانات من JSON:

```dart
// معالجة التصنيفات المتعددة
List<int> categoryIds = [];

// التحقق من وجود categoryIds كقائمة
if (poem['categoryIds'] != null && poem['categoryIds'] is List) {
  categoryIds = (poem['categoryIds'] as List).cast<int>();
} 
// إذا لم توجد categoryIds، استخدم categoryId الواحد
else if (poem['categoryId'] != null) {
  categoryIds = [poem['categoryId'] as int];
}
// إذا لم يوجد أي تصنيف، استخدم التصنيف التلقائي
else {
  categoryIds = [_determineCategoryId(poem)];
}

// إدخال التصنيفات في جدول poem_categories
for (final categoryId in categoryIds) {
  await db.insert('poem_categories', {
    'poem_id': poem['id'],
    'category_id': categoryId,
  });
}
```

### 2. استرجاع القصائد حسب التصنيف:

```sql
SELECT DISTINCT p.*, CASE WHEN f.poem_id IS NULL THEN 0 ELSE 1 END as is_favorite
FROM poems p
INNER JOIN poem_categories pc ON p.id = pc.poem_id
LEFT JOIN favorites f ON p.id = f.poem_id
WHERE pc.category_id = ?
```

### 3. استرجاع التصنيفات لقصيدة معينة:

```dart
Future<List<int>> getCategoryIdsForPoem(int poemId) async {
  final db = await database;
  final List<Map<String, dynamic>> maps = await db.query(
    'poem_categories',
    where: 'poem_id = ?',
    whereArgs: [poemId],
  );

  return maps.map((map) => map['category_id'] as int).toList();
}
```

## 🎯 أمثلة عملية

### مثال 1: قصيدة مدح وحكمة

```json
{
  "id": 100,
  "title": "في مدح الرسول وحكمة الحياة",
  "poetId": 1,
  "categoryId": 2,
  "categoryIds": [2, 4],
  "description": "قصيدة تجمع بين مدح النبي والحكمة",
  "verses": [
    {
      "first": "مدح الحبيب محمد خير الورى",
      "second": "وفيه حكمة للناس قد ظهرت"
    }
  ]
}
```

**النتيجة:**
- ستظهر في تصنيف "مدح" (ID: 2)
- ستظهر في تصنيف "حكمة" (ID: 4)

### مثال 2: قصيدة مخمسة وطنية

```json
{
  "id": 101,
  "title": "مخمس في حب الوطن",
  "poetId": 1,
  "categoryId": 3,
  "categoryIds": [3, 5],
  "type": "mukhammas",
  "mukhammasVerses": [...]
}
```

**النتيجة:**
- ستظهر في تصنيف "وطنية" (ID: 3)
- ستظهر في تصنيف "مخمس" (ID: 5)

### مثال 3: قصيدة بثلاث تصنيفات

```json
{
  "id": 102,
  "title": "مخمس في مدح الوطن",
  "poetId": 1,
  "categoryId": 2,
  "categoryIds": [2, 3, 5],
  "type": "mukhammas",
  "description": "مخمس يجمع بين مدح القادة والوطن"
}
```

**النتيجة:**
- ستظهر في تصنيف "مدح" (ID: 2)
- ستظهر في تصنيف "وطنية" (ID: 3)
- ستظهر في تصنيف "مخمس" (ID: 5)

## 🔧 التوافق مع النظام القديم

### 1. القصائد القديمة:
- إذا لم تحتوِ على `categoryIds`، سيتم استخدام `categoryId` فقط
- سيتم إنشاء سجل واحد في `poem_categories`

### 2. التصنيف التلقائي:
- إذا لم يوجد `categoryId` أو `categoryIds`، سيتم التصنيف التلقائي
- يعتمد على تحليل العنوان والوصف

### 3. الترقية التلقائية:
- عند ترقية قاعدة البيانات، سيتم نقل البيانات القديمة تلقائياً
- لا حاجة لتعديل القصائد الموجودة

## 🎨 واجهة المستخدم

### 1. عرض التصنيفات:
- عند اختيار تصنيف، تظهر جميع القصائد التي تنتمي إليه
- القصيدة الواحدة قد تظهر في عدة تصنيفات

### 2. البحث والتصفية:
- البحث يعمل عبر جميع التصنيفات
- التصفية تعرض القصائد من جميع التصنيفات المحددة

## ⚠️ ملاحظات مهمة

### 1. الأولوية:
- `categoryId` يبقى التصنيف الأساسي
- `categoryIds` تحتوي على جميع التصنيفات (بما في ذلك الأساسي)

### 2. التحديث:
- عند إضافة `categoryIds` لقصيدة موجودة، يجب إعادة تحميل البيانات
- استخدم زر "إعادة تحميل" أو "إعادة تهيئة قاعدة البيانات"

### 3. الأداء:
- استعلامات قاعدة البيانات محسنة باستخدام INNER JOIN
- فهرسة تلقائية على المفاتيح الأساسية

## 🚀 أمثلة متقدمة

### 1. قصيدة شاملة:

```json
{
  "id": 200,
  "title": "ديوان شامل",
  "poetId": 1,
  "categoryId": 1,
  "categoryIds": [1, 2, 3, 4],
  "description": "قصيدة تجمع بين المدح والوطنية والحكمة",
  "verses": [
    {
      "first": "مدح للرسول وحب للوطن",
      "second": "وحكمة في الحياة للإنسان"
    }
  ]
}
```

### 2. مجموعة قصائد متنوعة:

```json
[
  {
    "id": 300,
    "title": "مدح خالص",
    "categoryIds": [2]
  },
  {
    "id": 301,
    "title": "وطنية خالصة",
    "categoryIds": [3]
  },
  {
    "id": 302,
    "title": "مدح وطني",
    "categoryIds": [2, 3]
  },
  {
    "id": 303,
    "title": "مخمس في المدح",
    "categoryIds": [2, 5],
    "type": "mukhammas"
  }
]
```

## 🔍 استكشاف الأخطاء

### 1. القصيدة لا تظهر في التصنيف:
- تحقق من وجود `categoryIds` في JSON
- تأكد من صحة أرقام التصنيفات
- أعد تحميل البيانات

### 2. خطأ في قاعدة البيانات:
- تحقق من إصدار قاعدة البيانات (يجب أن يكون 3 أو أعلى)
- أعد تهيئة قاعدة البيانات من الإعدادات

### 3. التصنيف التلقائي لا يعمل:
- تحقق من وجود الكلمات المفتاحية في العنوان أو الوصف
- راجع دالة `_determineCategoryId`

هذا النظام يوفر مرونة كبيرة في تصنيف القصائد ويحافظ على التوافق مع النظام القديم! 🎉
