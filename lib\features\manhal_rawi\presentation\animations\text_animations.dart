import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

/// حركات النصوص المخصصة للتطبيق
class TextAnimations {
  /// تأثير ظهور النص تدريجياً حرفاً بحرف
  static Widget typewriter({
    required String text,
    required TextStyle style,
    Duration delay = Duration.zero,
    Duration duration = const Duration(milliseconds: 1500),
    TextAlign textAlign = TextAlign.center,
    int? maxLines,
    TextOverflow overflow = TextOverflow.clip,
  }) {
    return Text(
      text,
      style: style,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    )
        .animate(delay: delay)
        .fadeIn(
          duration: const Duration(milliseconds: 500),
        )
        .shimmer(
          duration: duration,
          color: style.color?.withValues(alpha: 0.7) ??
              Colors.white.withValues(alpha: 0.7),
        )
        .animate(onPlay: (controller) => controller.repeat(reverse: true))
        .then(delay: 500.ms)
        .fadeOut(duration: 500.ms);
  }

  /// تأثير ظهور النص بتلاشي
  static Widget fadeIn({
    required String text,
    required TextStyle style,
    Duration delay = Duration.zero,
    Duration duration = const Duration(milliseconds: 800),
    TextAlign textAlign = TextAlign.center,
    int? maxLines,
    TextOverflow overflow = TextOverflow.clip,
  }) {
    return Text(
      text,
      style: style,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    ).animate(delay: delay).fadeIn(
          duration: duration,
          curve: Curves.easeInOut,
        );
  }

  /// تأثير ظهور النص بانزلاق
  static Widget slideIn({
    required String text,
    required TextStyle style,
    Duration delay = Duration.zero,
    Duration duration = const Duration(milliseconds: 800),
    TextAlign textAlign = TextAlign.center,
    int? maxLines,
    TextOverflow overflow = TextOverflow.clip,
    Offset offset = const Offset(0.0, 0.3),
  }) {
    return Text(
      text,
      style: style,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    )
        .animate(delay: delay)
        .slideY(
          begin: offset.dy,
          duration: duration,
          curve: Curves.easeOutCubic,
        )
        .fadeIn(
          duration: duration,
          curve: Curves.easeInOut,
        );
  }

  /// تأثير نبض النص
  static Widget pulse({
    required String text,
    required TextStyle style,
    Duration delay = Duration.zero,
    Duration duration = const Duration(milliseconds: 1500),
    TextAlign textAlign = TextAlign.center,
    int? maxLines,
    TextOverflow overflow = TextOverflow.clip,
  }) {
    return Text(
      text,
      style: style,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    )
        .animate(
          onPlay: (controller) => controller.repeat(reverse: true),
          delay: delay,
        )
        .scale(
          duration: duration,
          begin: const Offset(1.0, 1.0),
          end: const Offset(1.05, 1.05),
          curve: Curves.easeInOut,
        );
  }

  /// تأثير تلألؤ النص
  static Widget shimmer({
    required String text,
    required TextStyle style,
    required Color shimmerColor,
    Duration delay = Duration.zero,
    Duration duration = const Duration(milliseconds: 2000),
    TextAlign textAlign = TextAlign.center,
    int? maxLines,
    TextOverflow overflow = TextOverflow.clip,
  }) {
    return Text(
      text,
      style: style,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    )
        .animate(
          onPlay: (controller) => controller.repeat(),
          delay: delay,
        )
        .shimmer(
          duration: duration,
          color: shimmerColor,
        );
  }
}
