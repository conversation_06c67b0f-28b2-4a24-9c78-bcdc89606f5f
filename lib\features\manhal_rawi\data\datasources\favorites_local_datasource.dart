import 'database_helper.dart';

/// مصدر بيانات محلي للمفضلات
///
/// يستخدم قاعدة بيانات SQLite لتخزين واسترجاع المفضلات
class FavoritesLocalDataSource {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// الحصول على قائمة معرفات القصائد المفضلة
  Future<List<int>> getFavoriteIds() async {
    try {
      final favoritePoems = await _databaseHelper.getFavoritePoems();
      return favoritePoems.map((poem) => poem.id).toList();
    } catch (e) {
      // في حالة حدوث خطأ، نعيد قائمة فارغة
      return [];
    }
  }

  /// إضافة قصيدة إلى المفضلة
  Future<void> addFavorite(int poemId) async {
    await _databaseHelper.addToFavorites(poemId);
  }

  /// إزالة قصيدة من المفضلة
  Future<void> removeFavorite(int poemId) async {
    await _databaseHelper.removeFromFavorites(poemId);
  }

  /// التحقق مما إذا كانت القصيدة مفضلة
  Future<bool> isFavorite(int poemId) async {
    return await _databaseHelper.isFavorite(poemId);
  }

  /// تبديل حالة المفضلة للقصيدة
  Future<bool> toggleFavorite(int poemId) async {
    return await _databaseHelper.toggleFavorite(poemId);
  }
}
