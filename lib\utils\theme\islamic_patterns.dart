import 'dart:math' show pi, cos, sin;
import 'package:flutter/material.dart';
import 'manhal_colors.dart';

/// مكتبة الزخارف الإسلامية للتطبيق
class IslamicPatterns {
  /// رسم زخرفة إسلامية هندسية
  static CustomPainter getGeometricPattern({
    required bool isDark,
    double opacity = 0.15,
    int complexity = 3,
  }) {
    return GeometricPatternPainter(
      isDark: isDark,
      opacity: opacity,
      complexity: complexity,
    );
  }

  /// رسم زخرفة إسلامية نباتية
  static CustomPainter getFloralPattern({
    required bool isDark,
    double opacity = 0.15,
    int complexity = 3,
  }) {
    return FloralPatternPainter(
      isDark: isDark,
      opacity: opacity,
      complexity: complexity,
    );
  }

  /// رسم إطار زخرفي إسلامي
  static BoxDecoration getDecorativeBorder({
    required bool isDark,
    double borderWidth = 2.0,
    BorderRadius? borderRadius,
  }) {
    final baseColor = isDark ? ManhalColors.gold500 : ManhalColors.primary;

    return BoxDecoration(
      borderRadius: borderRadius ?? BorderRadius.circular(16),
      border: Border.all(
        color: baseColor,
        width: borderWidth,
      ),
      boxShadow: [
        BoxShadow(
          color: baseColor.withValues(alpha: 0.3),
          blurRadius: 8,
          spreadRadius: 1,
        ),
      ],
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: isDark
            ? [
                ManhalColors.blue900,
                ManhalColors.blue800,
              ]
            : [
                Colors.white,
                ManhalColors.gold100.withValues(alpha: 0.3),
              ],
      ),
    );
  }

  /// الحصول على زخرفة هلال إسلامي
  static Widget getCrescentDecoration({
    required bool isDark,
    double size = 40,
    double opacity = 0.3,
  }) {
    final color = isDark ? ManhalColors.gold500 : ManhalColors.primary;

    return CustomPaint(
      size: Size(size, size),
      painter: CrescentPainter(
        color: color.withValues(alpha: opacity),
      ),
    );
  }

  /// الحصول على فاصل زخرفي فاخر للأبيات
  ///
  /// يتكيف مع حجم الشاشة وحجم الخط
  ///
  /// [fontSize] حجم الخط المستخدم في النص، يستخدم لتكييف حجم الزخرفة
  /// [textScale] معامل تكبير النص من MediaQuery
  static Widget getLuxuryVerseDivider({
    required bool isDark,
    double width = 100,
    double height = 30,
    double opacity = 0.7,
    double fontSize = 16.0,
    double textScale = 1.0,
  }) {
    final color = isDark ? ManhalColors.gold500 : ManhalColors.primary;

    // تكييف الارتفاع بناءً على حجم الخط ومعامل التكبير
    final adaptiveHeight = height * (fontSize / 16.0) * textScale;

    return LayoutBuilder(
      builder: (context, constraints) {
        // استخدام عرض الشاشة المتاح
        final availableWidth = constraints.maxWidth;

        return CustomPaint(
          size: Size(availableWidth, adaptiveHeight),
          painter: LuxuryDividerPainter(
            color: color.withValues(alpha: opacity),
            isDark: isDark,
          ),
        );
      },
    );
  }
}

/// رسام الزخارف الهندسية الإسلامية
class GeometricPatternPainter extends CustomPainter {
  final bool isDark;
  final double opacity;
  final int complexity;

  GeometricPatternPainter({
    required this.isDark,
    this.opacity = 0.15,
    this.complexity = 3,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final baseColor = isDark ? ManhalColors.gold500 : ManhalColors.primary;
    final paint = Paint()
      ..color = baseColor.withValues(alpha: opacity)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    final centerX = size.width / 2;
    final centerY = size.height / 2;
    final radius = size.width < size.height ? size.width / 3 : size.height / 3;

    // رسم النجمة الإسلامية الثمانية
    final path = Path();
    final points = <Offset>[];
    final sides = 8;

    for (int i = 0; i < sides; i++) {
      final angle = (i * 2 * 3.14159) / sides;
      points.add(Offset(
        centerX + radius * cos(angle),
        centerY + radius * sin(angle),
      ));
    }

    // رسم النجمة الأساسية
    path.moveTo(points[0].dx, points[0].dy);
    for (int i = 1; i < points.length; i++) {
      path.lineTo(points[i].dx, points[i].dy);
    }
    path.close();
    canvas.drawPath(path, paint);

    // رسم الزخارف الداخلية
    if (complexity > 1) {
      final innerPath = Path();
      final innerRadius = radius * 0.7;
      final innerPoints = <Offset>[];

      for (int i = 0; i < sides; i++) {
        final angle = (i * 2 * 3.14159) / sides + 3.14159 / sides;
        innerPoints.add(Offset(
          centerX + innerRadius * cos(angle),
          centerY + innerRadius * sin(angle),
        ));
      }

      innerPath.moveTo(innerPoints[0].dx, innerPoints[0].dy);
      for (int i = 1; i < innerPoints.length; i++) {
        innerPath.lineTo(innerPoints[i].dx, innerPoints[i].dy);
      }
      innerPath.close();
      canvas.drawPath(innerPath, paint);

      // ربط النقاط
      for (int i = 0; i < sides; i++) {
        canvas.drawLine(points[i], innerPoints[i], paint);
      }
    }

    // رسم زخارف إضافية للتعقيد العالي
    if (complexity > 2) {
      final centerPath = Path();
      final centerRadius = radius * 0.4;
      final centerPoints = <Offset>[];

      for (int i = 0; i < sides * 2; i++) {
        final angle = (i * 3.14159) / sides;
        final currentRadius = i % 2 == 0 ? centerRadius : centerRadius * 0.7;
        centerPoints.add(Offset(
          centerX + currentRadius * cos(angle),
          centerY + currentRadius * sin(angle),
        ));
      }

      centerPath.moveTo(centerPoints[0].dx, centerPoints[0].dy);
      for (int i = 1; i < centerPoints.length; i++) {
        centerPath.lineTo(centerPoints[i].dx, centerPoints[i].dy);
      }
      centerPath.close();
      canvas.drawPath(centerPath, paint);
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

/// رسام الزخارف النباتية الإسلامية
class FloralPatternPainter extends CustomPainter {
  final bool isDark;
  final double opacity;
  final int complexity;

  FloralPatternPainter({
    required this.isDark,
    this.opacity = 0.15,
    this.complexity = 3,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final baseColor = isDark ? ManhalColors.gold500 : ManhalColors.primary;
    final paint = Paint()
      ..color = baseColor.withValues(alpha: opacity)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    final width = size.width;
    final height = size.height;

    // رسم الزخارف النباتية في الزوايا
    _drawCornerFloral(canvas, paint, Offset(0, 0), size, 1);
    _drawCornerFloral(canvas, paint, Offset(width, 0), size, 2);
    _drawCornerFloral(canvas, paint, Offset(0, height), size, 3);
    _drawCornerFloral(canvas, paint, Offset(width, height), size, 4);

    // رسم زخارف إضافية في المنتصف
    if (complexity > 1) {
      _drawCenterFloral(canvas, paint, Offset(width / 2, height / 2), size);
    }
  }

  void _drawCornerFloral(
      Canvas canvas, Paint paint, Offset corner, Size size, int quadrant) {
    final radius = size.width < size.height ? size.width / 5 : size.height / 5;
    final path = Path();

    // تحديد نقطة البداية حسب الربع
    Offset start;
    switch (quadrant) {
      case 1: // أعلى اليسار
        start = Offset(corner.dx + radius * 0.2, corner.dy + radius * 0.2);
        break;
      case 2: // أعلى اليمين
        start = Offset(corner.dx - radius * 0.2, corner.dy + radius * 0.2);
        break;
      case 3: // أسفل اليسار
        start = Offset(corner.dx + radius * 0.2, corner.dy - radius * 0.2);
        break;
      case 4: // أسفل اليمين
        start = Offset(corner.dx - radius * 0.2, corner.dy - radius * 0.2);
        break;
      default:
        start = corner;
    }

    // رسم الفرع الرئيسي
    path.moveTo(start.dx, start.dy);

    // تحديد اتجاه المنحنى حسب الربع
    Offset control1, control2, end;
    switch (quadrant) {
      case 1: // أعلى اليسار
        control1 = Offset(start.dx + radius * 0.5, start.dy + radius * 0.1);
        control2 = Offset(start.dx + radius * 0.7, start.dy + radius * 0.5);
        end = Offset(start.dx + radius, start.dy + radius * 0.8);
        break;
      case 2: // أعلى اليمين
        control1 = Offset(start.dx - radius * 0.5, start.dy + radius * 0.1);
        control2 = Offset(start.dx - radius * 0.7, start.dy + radius * 0.5);
        end = Offset(start.dx - radius, start.dy + radius * 0.8);
        break;
      case 3: // أسفل اليسار
        control1 = Offset(start.dx + radius * 0.5, start.dy - radius * 0.1);
        control2 = Offset(start.dx + radius * 0.7, start.dy - radius * 0.5);
        end = Offset(start.dx + radius, start.dy - radius * 0.8);
        break;
      case 4: // أسفل اليمين
        control1 = Offset(start.dx - radius * 0.5, start.dy - radius * 0.1);
        control2 = Offset(start.dx - radius * 0.7, start.dy - radius * 0.5);
        end = Offset(start.dx - radius, start.dy - radius * 0.8);
        break;
      default:
        control1 = start;
        control2 = start;
        end = start;
    }

    path.cubicTo(
      control1.dx,
      control1.dy,
      control2.dx,
      control2.dy,
      end.dx,
      end.dy,
    );

    canvas.drawPath(path, paint);

    // رسم الأوراق
    if (complexity > 1) {
      _drawLeaf(canvas, paint, control1, quadrant);
      _drawLeaf(canvas, paint, control2, quadrant);
    }

    // رسم الزهرة
    if (complexity > 2) {
      _drawFlower(canvas, paint, end, radius * 0.3, quadrant);
    }
  }

  void _drawLeaf(Canvas canvas, Paint paint, Offset position, int quadrant) {
    final leafSize = 10.0;
    final leafPath = Path();

    Offset control1, control2, end;
    switch (quadrant) {
      case 1: // أعلى اليسار
        leafPath.moveTo(position.dx, position.dy);
        control1 = Offset(position.dx + leafSize, position.dy - leafSize / 2);
        control2 = Offset(position.dx + leafSize * 1.5, position.dy);
        end = Offset(position.dx + leafSize, position.dy + leafSize / 2);
        break;
      case 2: // أعلى اليمين
        leafPath.moveTo(position.dx, position.dy);
        control1 = Offset(position.dx - leafSize, position.dy - leafSize / 2);
        control2 = Offset(position.dx - leafSize * 1.5, position.dy);
        end = Offset(position.dx - leafSize, position.dy + leafSize / 2);
        break;
      case 3: // أسفل اليسار
        leafPath.moveTo(position.dx, position.dy);
        control1 = Offset(position.dx + leafSize, position.dy + leafSize / 2);
        control2 = Offset(position.dx + leafSize * 1.5, position.dy);
        end = Offset(position.dx + leafSize, position.dy - leafSize / 2);
        break;
      case 4: // أسفل اليمين
        leafPath.moveTo(position.dx, position.dy);
        control1 = Offset(position.dx - leafSize, position.dy + leafSize / 2);
        control2 = Offset(position.dx - leafSize * 1.5, position.dy);
        end = Offset(position.dx - leafSize, position.dy - leafSize / 2);
        break;
      default:
        control1 = position;
        control2 = position;
        end = position;
    }

    leafPath.cubicTo(
      control1.dx,
      control1.dy,
      control2.dx,
      control2.dy,
      end.dx,
      end.dy,
    );

    leafPath.cubicTo(
      control2.dx,
      control2.dy,
      control1.dx,
      control1.dy,
      position.dx,
      position.dy,
    );

    canvas.drawPath(leafPath, paint);
  }

  void _drawFlower(
      Canvas canvas, Paint paint, Offset center, double radius, int quadrant) {
    final flowerPath = Path();
    final petalCount = 5;

    for (int i = 0; i < petalCount; i++) {
      final angle = (i * 2 * 3.14159) / petalCount + (quadrant * 3.14159 / 4);
      final petalPath = Path();

      final start = Offset(
        center.dx + radius * 0.5 * cos(angle),
        center.dy + radius * 0.5 * sin(angle),
      );

      final end = Offset(
        center.dx + radius * cos(angle),
        center.dy + radius * sin(angle),
      );

      final control1 = Offset(
        center.dx + radius * 0.7 * cos(angle - 0.3),
        center.dy + radius * 0.7 * sin(angle - 0.3),
      );

      final control2 = Offset(
        center.dx + radius * 0.7 * cos(angle + 0.3),
        center.dy + radius * 0.7 * sin(angle + 0.3),
      );

      petalPath.moveTo(start.dx, start.dy);
      petalPath.cubicTo(
        control1.dx,
        control1.dy,
        control2.dx,
        control2.dy,
        end.dx,
        end.dy,
      );
      petalPath.cubicTo(
        control2.dx,
        control2.dy,
        control1.dx,
        control1.dy,
        start.dx,
        start.dy,
      );

      flowerPath.addPath(petalPath, Offset.zero);
    }

    // رسم مركز الزهرة
    flowerPath.addOval(Rect.fromCircle(center: center, radius: radius * 0.3));

    canvas.drawPath(flowerPath, paint);
  }

  void _drawCenterFloral(Canvas canvas, Paint paint, Offset center, Size size) {
    final radius = size.width < size.height ? size.width / 8 : size.height / 8;

    // رسم دائرة مركزية
    canvas.drawCircle(center, radius, paint);

    // رسم الأنماط الزخرفية حول المركز
    final petalCount = 8;
    for (int i = 0; i < petalCount; i++) {
      final angle = (i * 2 * 3.14159) / petalCount;
      final petalPath = Path();

      final start = Offset(
        center.dx + radius * cos(angle),
        center.dy + radius * sin(angle),
      );

      final end = Offset(
        center.dx + radius * 2 * cos(angle),
        center.dy + radius * 2 * sin(angle),
      );

      final control1 = Offset(
        center.dx + radius * 1.5 * cos(angle - 0.2),
        center.dy + radius * 1.5 * sin(angle - 0.2),
      );

      final control2 = Offset(
        center.dx + radius * 1.5 * cos(angle + 0.2),
        center.dy + radius * 1.5 * sin(angle + 0.2),
      );

      petalPath.moveTo(start.dx, start.dy);
      petalPath.cubicTo(
        control1.dx,
        control1.dy,
        control2.dx,
        control2.dy,
        end.dx,
        end.dy,
      );

      canvas.drawPath(petalPath, paint);

      if (complexity > 2) {
        // رسم زخارف إضافية
        final leafAngle = angle + 3.14159 / petalCount;
        final leafStart = Offset(
          center.dx + radius * 1.2 * cos(leafAngle),
          center.dy + radius * 1.2 * sin(leafAngle),
        );

        final leafEnd = Offset(
          center.dx + radius * 1.8 * cos(leafAngle),
          center.dy + radius * 1.8 * sin(leafAngle),
        );

        final leafControl = Offset(
          center.dx + radius * 1.5 * cos(leafAngle + 0.3),
          center.dy + radius * 1.5 * sin(leafAngle + 0.3),
        );

        final leafPath = Path();
        leafPath.moveTo(leafStart.dx, leafStart.dy);
        leafPath.quadraticBezierTo(
          leafControl.dx,
          leafControl.dy,
          leafEnd.dx,
          leafEnd.dy,
        );

        canvas.drawPath(leafPath, paint);
      }
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

/// رسام الهلال الإسلامي
class CrescentPainter extends CustomPainter {
  final Color color;

  CrescentPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final outerRadius = size.width / 2;
    final innerRadius = size.width / 3;
    final center = Offset(size.width / 2, size.height / 2);

    // رسم الدائرة الخارجية
    canvas.drawCircle(center, outerRadius, paint);

    // حذف الدائرة الداخلية (لإنشاء شكل الهلال)
    final innerCenter = Offset(center.dx + outerRadius / 3, center.dy);
    final cutoutPaint = Paint()
      ..color = Colors.transparent
      ..style = PaintingStyle.fill
      ..blendMode = BlendMode.clear;

    canvas.drawCircle(innerCenter, innerRadius, cutoutPaint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

/// رسام الفاصل الزخرفي الفاخر للأبيات
class LuxuryDividerPainter extends CustomPainter {
  final Color color;
  final bool isDark;

  LuxuryDividerPainter({
    required this.color,
    required this.isDark,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;
    final center = Offset(width / 2, height / 2);

    // تعريف الألوان
    final baseColor = color;
    final accentColor = isDark
        ? ManhalColors.gold300.withValues(alpha: 0.9)
        : ManhalColors.gold500.withValues(alpha: 0.9);
    final backgroundColor = isDark ? ManhalColors.blue900 : Colors.white;

    // تحديد سماكة الخطوط بناءً على حجم الفاصل
    final lineThickness = height * 0.03;
    final starStrokeWidth = height * 0.04;

    // رسم خط متدرج أكثر فخامة (خطين متوازيين)
    final gradientPaint1 = Paint()
      ..shader = LinearGradient(
        colors: [
          Colors.transparent,
          baseColor.withValues(alpha: 0.7),
          baseColor.withValues(alpha: 0.7),
          Colors.transparent,
        ],
        stops: const [0.0, 0.3, 0.7, 1.0],
      ).createShader(
          Rect.fromLTWH(0, center.dy - lineThickness, width, lineThickness))
      ..strokeWidth = lineThickness
      ..style = PaintingStyle.stroke;

    final gradientPaint2 = Paint()
      ..shader = LinearGradient(
        colors: [
          Colors.transparent,
          baseColor.withValues(alpha: 0.4),
          baseColor.withValues(alpha: 0.4),
          Colors.transparent,
        ],
        stops: const [0.0, 0.3, 0.7, 1.0],
      ).createShader(
          Rect.fromLTWH(0, center.dy + lineThickness, width, lineThickness))
      ..strokeWidth = lineThickness
      ..style = PaintingStyle.stroke;

    canvas.drawLine(
      Offset(0, center.dy - lineThickness),
      Offset(width, center.dy - lineThickness),
      gradientPaint1,
    );

    canvas.drawLine(
      Offset(0, center.dy + lineThickness),
      Offset(width, center.dy + lineThickness),
      gradientPaint2,
    );

    // رسم الزخرفة المركزية
    final centerSize = height * 0.9; // زيادة حجم الزخرفة المركزية
    final centerRect = Rect.fromCenter(
      center: center,
      width: centerSize,
      height: centerSize,
    );

    // خلفية للزخرفة المركزية (شكل بيضاوي بدلاً من مستطيل)
    final bgPaint = Paint()
      ..color = backgroundColor
      ..style = PaintingStyle.fill;

    canvas.drawOval(
      Rect.fromCenter(
        center: center,
        width: centerSize * 1.3,
        height: centerSize * 0.9,
      ),
      bgPaint,
    );

    // إضافة حدود للخلفية البيضاوية
    final bgBorderPaint = Paint()
      ..color = baseColor.withValues(alpha: 0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = height * 0.01;

    canvas.drawOval(
      Rect.fromCenter(
        center: center,
        width: centerSize * 1.3,
        height: centerSize * 0.9,
      ),
      bgBorderPaint,
    );

    // رسم نجمة إسلامية ثمانية
    final starPath = Path();
    final outerRadius = centerSize / 2;
    final innerRadius = outerRadius * 0.6;
    final points = 8;

    for (int i = 0; i < points * 2; i++) {
      final radius = i.isEven ? outerRadius : innerRadius;
      final angle = (i * pi) / points;
      final x = center.dx + radius * cos(angle);
      final y = center.dy + radius * sin(angle);

      if (i == 0) {
        starPath.moveTo(x, y);
      } else {
        starPath.lineTo(x, y);
      }
    }
    starPath.close();

    // رسم النجمة بتدرج لوني
    final starPaint = Paint()
      ..shader = RadialGradient(
        colors: [
          accentColor,
          baseColor,
        ],
        stops: const [0.3, 1.0],
      ).createShader(centerRect)
      ..style = PaintingStyle.fill;

    canvas.drawPath(starPath, starPaint);

    // إضافة حدود للنجمة
    final starBorderPaint = Paint()
      ..color = isDark
          ? ManhalColors.gold500.withValues(alpha: 0.8)
          : ManhalColors.primary.withValues(alpha: 0.6)
      ..style = PaintingStyle.stroke
      ..strokeWidth = starStrokeWidth;

    canvas.drawPath(starPath, starBorderPaint);

    // إضافة تفاصيل داخلية للنجمة
    final innerStarPath = Path();
    final innerStarRadius = innerRadius * 0.7;

    for (int i = 0; i < points; i++) {
      final angle = (i * 2 * pi) / points + (pi / points);
      final x = center.dx + innerStarRadius * cos(angle);
      final y = center.dy + innerStarRadius * sin(angle);

      if (i == 0) {
        innerStarPath.moveTo(x, y);
      } else {
        innerStarPath.lineTo(x, y);
      }
    }
    innerStarPath.close();

    // رسم النجمة الداخلية
    final innerStarPaint = Paint()
      ..color = backgroundColor
      ..style = PaintingStyle.fill;

    canvas.drawPath(innerStarPath, innerStarPaint);

    // إضافة حدود للنجمة الداخلية
    final innerStarBorderPaint = Paint()
      ..color = accentColor.withValues(alpha: 0.6)
      ..style = PaintingStyle.stroke
      ..strokeWidth = starStrokeWidth * 0.5;

    canvas.drawPath(innerStarPath, innerStarBorderPaint);

    // إضافة نقطة مركزية
    final centerDotPaint = Paint()
      ..shader = RadialGradient(
        colors: [
          accentColor,
          baseColor,
        ],
        stops: const [0.3, 1.0],
      ).createShader(Rect.fromCircle(center: center, radius: innerRadius * 0.3))
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, innerRadius * 0.3, centerDotPaint);

    // إضافة حدود للنقطة المركزية
    final centerDotBorderPaint = Paint()
      ..color = isDark
          ? ManhalColors.gold500.withValues(alpha: 0.8)
          : ManhalColors.primary.withValues(alpha: 0.6)
      ..style = PaintingStyle.stroke
      ..strokeWidth = starStrokeWidth * 0.3;

    canvas.drawCircle(center, innerRadius * 0.3, centerDotBorderPaint);

    // إضافة تفاصيل زخرفية إضافية على جانبي النجمة
    final decorSize = centerSize * 0.3;
    final leftCenter = Offset(center.dx - centerSize * 0.7, center.dy);
    final rightCenter = Offset(center.dx + centerSize * 0.7, center.dy);

    // رسم الزخارف الجانبية
    _drawSideDecoration(canvas, leftCenter, decorSize, baseColor, accentColor,
        backgroundColor, height);
    _drawSideDecoration(canvas, rightCenter, decorSize, baseColor, accentColor,
        backgroundColor, height);
  }

  // رسم زخرفة جانبية
  void _drawSideDecoration(Canvas canvas, Offset center, double size,
      Color baseColor, Color accentColor, Color bgColor, double height) {
    // تحديد سماكة الخطوط بناءً على حجم الفاصل
    final strokeWidth = height * 0.02;

    // رسم زخرفة أكثر فخامة (دوائر متداخلة)
    final outerCirclePaint = Paint()
      ..shader = RadialGradient(
        colors: [
          accentColor.withValues(alpha: 0.7),
          baseColor.withValues(alpha: 0.5),
        ],
        stops: const [0.3, 1.0],
      ).createShader(Rect.fromCircle(center: center, radius: size * 0.5))
      ..style = PaintingStyle.fill;

    // رسم دائرة خارجية
    canvas.drawCircle(center, size * 0.5, outerCirclePaint);

    // إضافة حدود للدائرة الخارجية
    final outerCircleBorderPaint = Paint()
      ..color = baseColor.withValues(alpha: 0.7)
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth;

    canvas.drawCircle(center, size * 0.5, outerCircleBorderPaint);

    // رسم دائرة داخلية
    final innerCirclePaint = Paint()
      ..color = bgColor
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, size * 0.3, innerCirclePaint);

    // إضافة حدود للدائرة الداخلية
    final innerCircleBorderPaint = Paint()
      ..color = accentColor.withValues(alpha: 0.8)
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth * 0.7;

    canvas.drawCircle(center, size * 0.3, innerCircleBorderPaint);

    // إضافة نقطة مركزية
    final centerDotPaint = Paint()
      ..color = accentColor
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, size * 0.15, centerDotPaint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
