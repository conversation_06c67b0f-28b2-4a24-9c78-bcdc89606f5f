import 'package:flutter/material.dart';
import 'manhal_colors.dart';

/// مكتبة الخطوط الإسلامية للتطبيق
class IslamicTypography {
  /// خط العنوان الرئيسي الفاخر
  static TextStyle luxuryTitle({
    required bool isDark,
    double fontSize = 32,
    FontWeight fontWeight = FontWeight.bold,
    String fontFamily = 'Amiri',
  }) {
    // استخدام نوع الخط المحدد
    final shadows = [
      Shadow(
        color: isDark
            ? ManhalColors.gold500.withValues(alpha: 0.3)
            : ManhalColors.primary.withValues(alpha: 0.2),
        offset: const Offset(1, 1),
        blurRadius: 3,
      ),
    ];

    switch (fontFamily) {
      case 'Amiri':
        return TextStyle(
          fontFamily: 'Amiri',
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: isDark ? ManhalColors.gold500 : ManhalColors.primary,
          height: 1.4,
          letterSpacing: 0.5,
          shadows: shadows,
        );
      case 'Cairo':
        return TextStyle(
          fontFamily: 'Cairo',
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: isDark ? ManhalColors.gold500 : ManhalColors.primary,
          height: 1.4,
          letterSpacing: 0.5,
          shadows: shadows,
        );
      case 'Markazi':
        return TextStyle(
          fontFamily: 'Markazi',
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: isDark ? ManhalColors.gold500 : ManhalColors.primary,
          height: 1.4,
          letterSpacing: 0.5,
          shadows: shadows,
        );
      case 'Raqq':
        return TextStyle(
          fontFamily: 'Scheherazade',
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: isDark ? ManhalColors.gold500 : ManhalColors.primary,
          height: 1.4,
          letterSpacing: 0.5,
          shadows: shadows,
        );
      case 'Naskh':
        return TextStyle(
          fontFamily: 'Naskh',
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: isDark ? ManhalColors.gold500 : ManhalColors.primary,
          height: 1.4,
          letterSpacing: 0.5,
          shadows: shadows,
        );
      default:
        return TextStyle(
          fontFamily: 'Amiri',
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: isDark ? ManhalColors.gold500 : ManhalColors.primary,
          height: 1.4,
          letterSpacing: 0.5,
          shadows: shadows,
        );
    }
  }

  /// خط العنوان الفرعي الفاخر
  static TextStyle luxurySubtitle({
    required bool isDark,
    double fontSize = 22,
    FontWeight fontWeight = FontWeight.w600,
  }) {
    return TextStyle(
      fontFamily: 'ArefRuqaa',
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: isDark ? ManhalColors.gold300 : ManhalColors.gold700,
      height: 1.3,
      letterSpacing: 0.3,
    );
  }

  /// خط الأبيات الشعرية الفاخر
  static TextStyle luxuryVerse({
    required bool isDark,
    double fontSize = 20,
    FontWeight fontWeight = FontWeight.w500,
    String fontFamily = 'Scheherazade',
  }) {
    // استخدام نوع الخط المحدد
    switch (fontFamily) {
      case 'Amiri':
        return TextStyle(
          fontFamily: 'Amiri',
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: isDark ? ManhalColors.textLight : ManhalColors.textDark,
          height: 1.3, // تم تقليل ارتفاع النص لجعل الأبيات أكثر تقاربًا
          letterSpacing: 0.2,
        );
      case 'Cairo':
        return TextStyle(
          fontFamily: 'Cairo',
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: isDark ? ManhalColors.textLight : ManhalColors.textDark,
          height: 1.3, // تم تقليل ارتفاع النص لجعل الأبيات أكثر تقاربًا
          letterSpacing: 0.2,
        );
      case 'Markazi':
        return TextStyle(
          fontFamily: 'Markazi',
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: isDark ? ManhalColors.textLight : ManhalColors.textDark,
          height: 1.3, // تم تقليل ارتفاع النص لجعل الأبيات أكثر تقاربًا
          letterSpacing: 0.2,
        );
      case 'Raqq':
        return TextStyle(
          fontFamily: 'Scheherazade',
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: isDark ? ManhalColors.textLight : ManhalColors.textDark,
          height: 1.3, // تم تقليل ارتفاع النص لجعل الأبيات أكثر تقاربًا
          letterSpacing: 0.2,
        );
      case 'Naskh':
        return TextStyle(
          fontFamily: 'Naskh',
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: isDark ? ManhalColors.textLight : ManhalColors.textDark,
          height: 1.3, // تم تقليل ارتفاع النص لجعل الأبيات أكثر تقاربًا
          letterSpacing: 0.2,
        );
      default:
        return TextStyle(
          fontFamily: 'Scheherazade',
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: isDark ? ManhalColors.textLight : ManhalColors.textDark,
          height: 1.3, // تم تقليل ارتفاع النص لجعل الأبيات أكثر تقاربًا
          letterSpacing: 0.2,
        );
    }
  }

  /// خط النص العادي الفاخر
  static TextStyle luxuryBody({
    required bool isDark,
    double fontSize = 16,
    FontWeight fontWeight = FontWeight.normal,
    String fontFamily = 'Naskh',
  }) {
    // استخدام نوع الخط المحدد
    switch (fontFamily) {
      case 'Amiri':
        return TextStyle(
          fontFamily: 'Amiri',
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: isDark ? ManhalColors.textLight : ManhalColors.textDark,
          height: 1.3, // تم تقليل ارتفاع الخط لجعل النص أكثر تقاربًا
        );
      case 'Cairo':
        return TextStyle(
          fontFamily: 'Cairo',
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: isDark ? ManhalColors.textLight : ManhalColors.textDark,
          height: 1.3, // تم تقليل ارتفاع الخط لجعل النص أكثر تقاربًا
        );
      case 'Markazi':
        return TextStyle(
          fontFamily: 'Markazi',
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: isDark ? ManhalColors.textLight : ManhalColors.textDark,
          height: 1.3, // تم تقليل ارتفاع الخط لجعل النص أكثر تقاربًا
        );
      case 'Raqq':
        return TextStyle(
          fontFamily: 'Scheherazade',
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: isDark ? ManhalColors.textLight : ManhalColors.textDark,
          height: 1.3, // تم تقليل ارتفاع الخط لجعل النص أكثر تقاربًا
        );
      case 'Naskh':
        return TextStyle(
          fontFamily: 'Naskh',
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: isDark ? ManhalColors.textLight : ManhalColors.textDark,
          height: 1.3, // تم تقليل ارتفاع الخط لجعل النص أكثر تقاربًا
        );
      default:
        return TextStyle(
          fontFamily: 'Naskh',
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: isDark ? ManhalColors.textLight : ManhalColors.textDark,
          height: 1.3, // تم تقليل ارتفاع الخط لجعل النص أكثر تقاربًا
        );
    }
  }

  /// خط العناوين الصغيرة الفاخر
  static TextStyle luxuryCaption({
    required bool isDark,
    double fontSize = 14,
    FontWeight fontWeight = FontWeight.w500,
    String fontFamily = 'Tajawal',
  }) {
    // استخدام نوع الخط المحدد
    switch (fontFamily) {
      case 'Amiri':
        return TextStyle(
          fontFamily: 'Amiri',
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: isDark ? ManhalColors.gold300 : ManhalColors.gold600,
          height: 1.4,
          letterSpacing: 0.2,
        );
      case 'Cairo':
        return TextStyle(
          fontFamily: 'Cairo',
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: isDark ? ManhalColors.gold300 : ManhalColors.gold600,
          height: 1.4,
          letterSpacing: 0.2,
        );
      case 'Markazi':
        return TextStyle(
          fontFamily: 'Markazi',
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: isDark ? ManhalColors.gold300 : ManhalColors.gold600,
          height: 1.4,
          letterSpacing: 0.2,
        );
      case 'Raqq':
        return TextStyle(
          fontFamily: 'Scheherazade',
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: isDark ? ManhalColors.gold300 : ManhalColors.gold600,
          height: 1.4,
          letterSpacing: 0.2,
        );
      case 'Naskh':
        return TextStyle(
          fontFamily: 'Naskh',
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: isDark ? ManhalColors.gold300 : ManhalColors.gold600,
          height: 1.4,
          letterSpacing: 0.2,
        );
      case 'Tajawal':
      default:
        return TextStyle(
          fontFamily: 'Tajawal',
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: isDark ? ManhalColors.gold300 : ManhalColors.gold600,
          height: 1.4,
          letterSpacing: 0.2,
        );
    }
  }

  /// خط الاقتباسات الفاخر
  static TextStyle luxuryQuote({
    required bool isDark,
    double fontSize = 18,
    FontStyle fontStyle = FontStyle.italic,
  }) {
    return TextStyle(
      fontFamily: 'Lateef',
      fontSize: fontSize,
      fontStyle: fontStyle,
      color: isDark ? ManhalColors.gold400 : ManhalColors.gold600,
      height: 1.5,
    );
  }

  /// خط الأزرار الفاخر
  static TextStyle luxuryButton({
    required bool isDark,
    double fontSize = 16,
    FontWeight fontWeight = FontWeight.bold,
  }) {
    return TextStyle(
      fontFamily: 'Cairo',
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: isDark ? ManhalColors.textLight : Colors.white,
      letterSpacing: 0.5,
    );
  }

  /// خط عنوان الكتاب الفاخر
  static TextStyle luxuryBookTitle({
    required bool isDark,
    double fontSize = 36,
    FontWeight fontWeight = FontWeight.bold,
  }) {
    // محاولة استخدام خط ReemKufi المخصص أولاً
    try {
      return TextStyle(
        fontFamily: 'ReemKufi',
        fontSize: fontSize,
        fontWeight: fontWeight,
        color: isDark ? ManhalColors.gold500 : ManhalColors.primary,
        height: 1.3,
        letterSpacing: 0.5,
        shadows: [
          Shadow(
            color: isDark
                ? ManhalColors.gold500.withValues(alpha: 0.4)
                : ManhalColors.primary.withValues(alpha: 0.3),
            offset: const Offset(1, 1),
            blurRadius: 4,
          ),
        ],
      );
    } catch (e) {
      // استخدام خط Cairo كبديل في حالة عدم توفر خط ReemKufi
      return TextStyle(
        fontFamily: 'Cairo',
        fontSize: fontSize,
        fontWeight: fontWeight,
        color: isDark ? ManhalColors.gold500 : ManhalColors.primary,
        height: 1.3,
        letterSpacing: 0.5,
        shadows: [
          Shadow(
            color: isDark
                ? ManhalColors.gold500.withValues(alpha: 0.4)
                : ManhalColors.primary.withValues(alpha: 0.3),
            offset: const Offset(1, 1),
            blurRadius: 4,
          ),
        ],
      );
    }
  }

  /// خط اسم المؤلف الفاخر
  static TextStyle luxuryAuthorName({
    required bool isDark,
    double fontSize = 24,
    FontWeight fontWeight = FontWeight.w600,
  }) {
    return TextStyle(
      fontFamily: 'ArefRuqaa',
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: isDark ? ManhalColors.gold400 : ManhalColors.gold700,
      height: 1.3,
      letterSpacing: 0.3,
    );
  }

  /// خط عنوان القصيدة الفاخر
  static TextStyle luxuryPoemTitle({
    required bool isDark,
    double fontSize = 26,
    FontWeight fontWeight = FontWeight.bold,
  }) {
    return TextStyle(
      fontFamily: 'Amiri',
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: isDark ? ManhalColors.gold500 : ManhalColors.primary,
      height: 1.4,
      letterSpacing: 0.3,
      decoration: TextDecoration.underline,
      decorationColor: isDark ? ManhalColors.gold300 : ManhalColors.gold400,
      decorationThickness: 1.5,
    );
  }

  /// خط التصنيفات الفاخر
  static TextStyle luxuryCategory({
    required bool isDark,
    double fontSize = 16,
    FontWeight fontWeight = FontWeight.w600,
  }) {
    return TextStyle(
      fontFamily: 'Cairo',
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: isDark ? ManhalColors.gold300 : ManhalColors.gold600,
      letterSpacing: 0.2,
    );
  }
}
