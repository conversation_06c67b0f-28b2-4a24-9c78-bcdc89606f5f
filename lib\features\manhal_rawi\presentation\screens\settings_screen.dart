import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import '../providers/manhal_rawi_provider.dart';
import '../widgets/simple_islamic_background.dart';
import '../widgets/luxury_about_dialog.dart';
import '../../../../utils/theme/manhal_colors.dart';
import '../../../../utils/theme/islamic_typography.dart';
import '../../../../utils/helpers/url_launcher_helper.dart';

import '../../data/datasources/database_helper.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  /// فتح قناة التلجرام
  void _openTelegramChannel() {
    // استخدام اسم قناة افتراضي، يمكن تغييره لاحقاً
    const String channelUsername = 'manhalrawi';

    UrlLauncherHelper.openTelegramChannel(
      channelUsername,
      context: context,
    );
  }

  /// مشاركة التطبيق
  void _shareApp() {
    // معلومات التطبيق للمشاركة
    const String appName = 'المنهل الراوي';
    const String appDescription =
        'تطبيق المنهل الراوي - ديوان الشاعر محمد هزاع باعلوي الحضرمي';

    // رابط التطبيق (يمكن تغييره لاحقاً عندما يتم نشر التطبيق)
    const String appLink =
        'https://play.google.com/store/apps/details?id=com.manhalrawi.app';

    // نص المشاركة
    final String shareText =
        '$appName\n$appDescription\n\nحمّل التطبيق الآن:\n$appLink';

    // مشاركة التطبيق
    Share.share(
      shareText,
      subject: appName,
    ).then((_) {
      // تم المشاركة بنجاح
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تمت مشاركة التطبيق بنجاح',
              style: const TextStyle(
                fontFamily: 'Amiri',
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }).catchError((error) {
      // حدث خطأ أثناء المشاركة
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ أثناء مشاركة التطبيق: $error',
              style: const TextStyle(
                fontFamily: 'Amiri',
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    });
  }

  // إعادة تهيئة قاعدة البيانات
  Future<void> _resetDatabase() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // إعادة تهيئة قاعدة البيانات
      final databaseHelper = DatabaseHelper();
      await databaseHelper.resetDatabase();

      // إعادة تحميل البيانات في Provider
      if (mounted) {
        await context.read<ManhalRawiProvider>().loadInitialData();
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم إعادة تهيئة قاعدة البيانات بنجاح',
              style: const TextStyle(
                fontFamily: 'Amiri',
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'فشل في إعادة تهيئة قاعدة البيانات: $e',
              style: const TextStyle(
                fontFamily: 'Amiri',
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<ManhalRawiProvider>();
    final isDarkMode = provider.isDarkMode;

    return Directionality(
      textDirection: TextDirection.rtl, // تأكيد اتجاه RTL للشاشة بأكملها
      child: Scaffold(
        body: Stack(
          children: [
            // خلفية إسلامية بسيطة
            SimpleIslamicBackground(
              isDark: isDarkMode,
              showPatterns: true,
              child: Container(),
            ),

            // محتوى الشاشة
            SafeArea(
              child: FadeTransition(
                opacity: _animation,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // شريط العنوان
                      _buildAppBar(isDarkMode),

                      const SizedBox(height: 24.0),

                      // عنوان الإعدادات
                      Text(
                        'الإعدادات',
                        style: TextStyle(
                          fontFamily: 'Amiri',
                          fontSize: 24.0,
                          fontWeight: FontWeight.bold,
                          color: isDarkMode
                              ? ManhalColors.gold500
                              : ManhalColors.primary,
                        ),
                      ),

                      const SizedBox(height: 16.0),

                      // قائمة الإعدادات
                      Expanded(
                        child: ListView(
                          children: [
                            // عنوان قسم المظهر
                            _buildSectionTitle('المظهر', isDarkMode),

                            // إعداد الوضع المظلم
                            _buildSettingItem(
                              icon: isDarkMode
                                  ? Icons.light_mode
                                  : Icons.dark_mode,
                              title: 'الوضع المظلم',
                              subtitle: isDarkMode ? 'مفعل' : 'غير مفعل',
                              isDarkMode: isDarkMode,
                              onTap: () {
                                provider.toggleDarkMode();
                              },
                              trailing: Switch(
                                value: isDarkMode,
                                onChanged: (_) {
                                  provider.toggleDarkMode();
                                },
                                activeColor: ManhalColors.gold500,
                                activeTrackColor: ManhalColors.blue800,
                                inactiveThumbColor: Colors.white,
                                inactiveTrackColor: Colors.grey.shade300,
                              ),
                            ),

                            const SizedBox(height: 24),

                            // عنوان قسم قاعدة البيانات
                            _buildSectionTitle('قاعدة البيانات', isDarkMode),

                            // إعادة تهيئة قاعدة البيانات
                            _buildSettingItem(
                              icon: Icons.refresh,
                              title: 'إعادة تهيئة قاعدة البيانات',
                              subtitle: 'إعادة تحميل البيانات من ملف JSON',
                              isDarkMode: isDarkMode,
                              onTap: _isLoading
                                  ? null
                                  : () {
                                      showDialog(
                                        context: context,
                                        builder: (context) => AlertDialog(
                                          backgroundColor: isDarkMode
                                              ? ManhalColors.blue900
                                              : Colors.white,
                                          title: Text(
                                            'تأكيد إعادة التهيئة',
                                            style: IslamicTypography
                                                .luxurySubtitle(
                                              isDark: isDarkMode,
                                              fontSize: 18,
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                          content: Text(
                                            'هل أنت متأكد من إعادة تهيئة قاعدة البيانات؟ سيتم إعادة تحميل البيانات من ملف JSON وستفقد جميع التغييرات التي قمت بها.',
                                            style: IslamicTypography.luxuryBody(
                                              isDark: isDarkMode,
                                              fontSize: 16,
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                          actions: [
                                            TextButton(
                                              onPressed: () {
                                                Navigator.pop(context);
                                              },
                                              child: Text(
                                                'إلغاء',
                                                style: TextStyle(
                                                  color: isDarkMode
                                                      ? ManhalColors.gold300
                                                      : ManhalColors.primary,
                                                ),
                                              ),
                                            ),
                                            TextButton(
                                              onPressed: () {
                                                Navigator.pop(context);
                                                _resetDatabase();
                                              },
                                              child: Text(
                                                'تأكيد',
                                                style: TextStyle(
                                                  color: isDarkMode
                                                      ? ManhalColors.gold500
                                                      : ManhalColors.primary,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                              trailing: _isLoading
                                  ? SizedBox(
                                      width: 24,
                                      height: 24,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                          isDarkMode
                                              ? ManhalColors.gold500
                                              : ManhalColors.primary,
                                        ),
                                      ),
                                    )
                                  : Icon(
                                      Icons.arrow_forward_ios,
                                      size: 16,
                                      color: isDarkMode
                                          ? ManhalColors.gold500
                                          : ManhalColors.primary,
                                    ),
                            ),

                            const SizedBox(height: 24),

                            // عنوان قسم عرض الأبيات
                            _buildSectionTitle('عرض الأبيات', isDarkMode),

                            // إعداد حجم الخط
                            _buildSettingItem(
                              icon: Icons.format_size,
                              title: 'حجم الخط',
                              subtitle: 'تغيير حجم خط عرض الأبيات',
                              isDarkMode: isDarkMode,
                              onTap: () {
                                _showFontSizeDialog(context, isDarkMode);
                              },
                              trailing: Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: isDarkMode
                                      ? ManhalColors.blue900
                                          .withValues(alpha: 0.7)
                                      : ManhalColors.gold100
                                          .withValues(alpha: 0.7),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: isDarkMode
                                        ? ManhalColors.gold500
                                            .withValues(alpha: 0.3)
                                        : ManhalColors.gold300,
                                    width: 1,
                                  ),
                                ),
                                child: Text(
                                  provider.fontSize.toStringAsFixed(1),
                                  style: TextStyle(
                                    fontFamily: 'Amiri',
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                    color: isDarkMode
                                        ? ManhalColors.gold500
                                        : ManhalColors.primary,
                                  ),
                                ),
                              ),
                            ),

                            const SizedBox(height: 16),

                            // إعداد نوع الخط
                            _buildSettingItem(
                              icon: Icons.font_download,
                              title: 'نوع الخط',
                              subtitle: 'اختيار نوع خط عرض الأبيات',
                              isDarkMode: isDarkMode,
                              onTap: () {
                                _showFontFamilyDialog(context, isDarkMode);
                              },
                              trailing: Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: isDarkMode
                                      ? ManhalColors.blue900
                                          .withValues(alpha: 0.7)
                                      : ManhalColors.gold100
                                          .withValues(alpha: 0.7),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: isDarkMode
                                        ? ManhalColors.gold500
                                            .withValues(alpha: 0.3)
                                        : ManhalColors.gold300,
                                    width: 1,
                                  ),
                                ),
                                child: Text(
                                  _getFontFamilyDisplayName(
                                      provider.fontFamily),
                                  style: TextStyle(
                                    fontFamily: provider.fontFamily,
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                    color: isDarkMode
                                        ? ManhalColors.gold500
                                        : ManhalColors.primary,
                                  ),
                                ),
                              ),
                            ),

                            const SizedBox(height: 16),

                            // إعداد نمط عرض الأبيات
                            _buildSettingItem(
                              icon: Icons.book,
                              title: 'نمط عرض الأبيات',
                              subtitle: 'اختيار نمط عرض القصائد العادية',
                              isDarkMode: isDarkMode,
                              onTap: () {
                                _showVerseDisplayTypeDialog(
                                    context, isDarkMode);
                              },
                              trailing: Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: isDarkMode
                                      ? ManhalColors.blue900
                                          .withValues(alpha: 0.7)
                                      : ManhalColors.gold100
                                          .withValues(alpha: 0.7),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: isDarkMode
                                        ? ManhalColors.gold500
                                            .withValues(alpha: 0.3)
                                        : ManhalColors.gold300,
                                    width: 1,
                                  ),
                                ),
                                child: Text(
                                  _getVerseDisplayTypeName(
                                      provider.verseDisplayType),
                                  style: TextStyle(
                                    fontFamily: 'Amiri',
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                    color: isDarkMode
                                        ? ManhalColors.gold500
                                        : ManhalColors.primary,
                                  ),
                                ),
                              ),
                            ),

                            const SizedBox(height: 16),

                            // إعداد عدد الأبيات في الصفحة الواحدة
                            _buildSettingItem(
                              icon: Icons.format_list_numbered,
                              title: 'عدد الأبيات في الصفحة',
                              subtitle:
                                  'تحديد عدد الأبيات المعروضة في الصفحة الواحدة',
                              isDarkMode: isDarkMode,
                              onTap: () {
                                _showVersesPerPageDialog(context, isDarkMode);
                              },
                              trailing: Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: isDarkMode
                                      ? ManhalColors.blue900
                                          .withValues(alpha: 0.7)
                                      : ManhalColors.gold100
                                          .withValues(alpha: 0.7),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: isDarkMode
                                        ? ManhalColors.gold500
                                            .withValues(alpha: 0.3)
                                        : ManhalColors.gold300,
                                    width: 1,
                                  ),
                                ),
                                child: Text(
                                  '${provider.versesPerPage}',
                                  style: TextStyle(
                                    fontFamily: 'Amiri',
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                    color: isDarkMode
                                        ? ManhalColors.gold500
                                        : ManhalColors.primary,
                                  ),
                                ),
                              ),
                            ),

                            const SizedBox(height: 24),

                            // عنوان قسم القصائد المخمسة
                            _buildSectionTitle('القصائد المخمسة', isDarkMode),

                            // إعداد نوع عرض القصائد المخمسة
                            _buildSettingItem(
                              icon: Icons.view_quilt,
                              title: 'نمط عرض القصائد المخمسة',
                              subtitle: 'اختيار نمط عرض القصائد المخمسة',
                              isDarkMode: isDarkMode,
                              onTap: () {
                                _showMukhammasDisplayTypeDialog(
                                    context, isDarkMode);
                              },
                              trailing: Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: isDarkMode
                                      ? ManhalColors.blue900
                                          .withValues(alpha: 0.7)
                                      : ManhalColors.gold100
                                          .withValues(alpha: 0.7),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: isDarkMode
                                        ? ManhalColors.gold500
                                            .withValues(alpha: 0.3)
                                        : ManhalColors.gold300,
                                    width: 1,
                                  ),
                                ),
                                child: Text(
                                  _getMukhammasDisplayTypeName(
                                      provider.mukhammasDisplayType),
                                  style: TextStyle(
                                    fontFamily: 'Amiri',
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                    color: isDarkMode
                                        ? ManhalColors.gold500
                                        : ManhalColors.primary,
                                  ),
                                ),
                              ),
                            ),

                            const SizedBox(height: 24),

                            // عنوان قسم حول التطبيق
                            _buildSectionTitle('حول التطبيق', isDarkMode),

                            // معلومات التطبيق
                            _buildSettingItem(
                              icon: Icons.info_outline,
                              title: 'عن التطبيق',
                              subtitle: 'المنهل الراوي - الإصدار 1.0.7',
                              isDarkMode: isDarkMode,
                              onTap: () {
                                showDialog(
                                  context: context,
                                  builder: (context) =>
                                      const LuxuryAboutDialog(),
                                );
                              },
                            ),

                            const SizedBox(height: 16),

                            // تواصل معنا
                            _buildSettingItem(
                              icon: Icons.telegram,
                              title: 'تواصل معنا',
                              subtitle: 'انضم إلى قناة التطبيق على تلجرام',
                              isDarkMode: isDarkMode,
                              onTap: () {
                                _openTelegramChannel();
                              },
                            ),

                            const SizedBox(height: 16),

                            // مشاركة التطبيق
                            _buildSettingItem(
                              icon: Icons.share,
                              title: 'مشاركة التطبيق',
                              subtitle: 'شارك التطبيق مع الأصدقاء والعائلة',
                              isDarkMode: isDarkMode,
                              onTap: () {
                                _shareApp();
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppBar(bool isDarkMode) {
    return Row(
      children: [
        // زر الرجوع
        Container(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isDarkMode
                ? ManhalColors.blue800.withValues(alpha: 0.7)
                : Colors.white.withValues(alpha: 0.7),
            border: Border.all(
              color: isDarkMode
                  ? ManhalColors.gold500.withValues(alpha: 0.3)
                  : ManhalColors.gold300,
              width: 1.0,
            ),
          ),
          child: IconButton(
            icon: Icon(
              Icons
                  .arrow_back_ios_new, // تغيير من arrow_back_ios إلى arrow_forward_ios للتوافق مع اتجاه RTL
              color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
              size: 20,
            ),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
        ),

        const Spacer(),

        // عنوان الشاشة
        Text(
          'الإعدادات',
          style: TextStyle(
            fontFamily: 'Amiri',
            fontSize: 22,
            fontWeight: FontWeight.bold,
            color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
          ),
        ),

        const Spacer(),

        // زر المساعدة
        Container(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isDarkMode
                ? ManhalColors.blue800.withValues(alpha: 0.7)
                : Colors.white.withValues(alpha: 0.7),
            border: Border.all(
              color: isDarkMode
                  ? ManhalColors.gold500.withValues(alpha: 0.3)
                  : ManhalColors.gold300,
              width: 1,
            ),
          ),
          child: IconButton(
            icon: Icon(
              Icons.help_outline,
              color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
              size: 20,
            ),
            onPressed: () {
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  backgroundColor:
                      isDarkMode ? ManhalColors.blue900 : Colors.white,
                  title: Text(
                    'مساعدة',
                    style: TextStyle(
                      fontFamily: 'Amiri',
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: isDarkMode
                          ? ManhalColors.gold500
                          : ManhalColors.primary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  content: Text(
                    'يمكنك من هذه الشاشة تغيير إعدادات التطبيق مثل الوضع المظلم ونوع الخط وحجمه. تتم مزامنة قاعدة البيانات تلقائيًا عند تثبيت التطبيق أو تحديثه، ويمكنك أيضًا مزامنتها يدويًا من قسم قاعدة البيانات.',
                    style: TextStyle(
                      fontFamily: 'Amiri',
                      fontSize: 16,
                      color: isDarkMode ? Colors.white70 : Colors.black87,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  actions: [
                    TextButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      child: Text(
                        'حسناً',
                        style: TextStyle(
                          color: isDarkMode
                              ? ManhalColors.gold500
                              : ManhalColors.primary,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// بناء عنوان قسم في الإعدادات
  Widget _buildSectionTitle(String title, bool isDarkMode) {
    return Padding(
      padding: const EdgeInsets.only(right: 8, bottom: 8, top: 8),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 20,
            decoration: BoxDecoration(
              color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            title,
            style: TextStyle(
              fontFamily: 'Amiri',
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool isDarkMode,
    required VoidCallback? onTap,
    Widget? trailing,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withValues(alpha: 0.2)
                : Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          splashColor: isDarkMode
              ? ManhalColors.gold500.withValues(alpha: 0.1)
              : ManhalColors.primary.withValues(alpha: 0.1),
          highlightColor: isDarkMode
              ? ManhalColors.gold500.withValues(alpha: 0.05)
              : ManhalColors.primary.withValues(alpha: 0.05),
          child: Ink(
            decoration: BoxDecoration(
              color: isDarkMode
                  ? ManhalColors.blue800.withValues(alpha: 0.7)
                  : Colors.white.withValues(alpha: 0.9),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: isDarkMode
                    ? ManhalColors.gold500.withValues(alpha: 0.3)
                    : ManhalColors.gold300,
                width: 1,
              ),
              image: DecorationImage(
                image: AssetImage(
                  isDarkMode
                      ? 'assets/images/paper_texture_dark.png'
                      : 'assets/images/paper_texture_light.png',
                ),
                opacity: 0.05,
                fit: BoxFit.cover,
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // أيقونة الإعداد
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: isDarkMode
                          ? ManhalColors.blue900.withValues(alpha: 0.8)
                          : ManhalColors.gold100.withValues(alpha: 0.8),
                      border: Border.all(
                        color: isDarkMode
                            ? ManhalColors.gold500.withValues(alpha: 0.3)
                            : ManhalColors.gold300,
                        width: 1,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: isDarkMode
                              ? Colors.black.withValues(alpha: 0.2)
                              : Colors.black.withValues(alpha: 0.05),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Icon(
                      icon,
                      color: isDarkMode
                          ? ManhalColors.gold500
                          : ManhalColors.primary,
                      size: 22,
                    ),
                  ),

                  const SizedBox(width: 16),

                  // عنوان ووصف الإعداد
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: TextStyle(
                            fontFamily: 'Amiri',
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: isDarkMode
                                ? ManhalColors.gold500
                                : ManhalColors.primary,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          subtitle,
                          style: TextStyle(
                            fontFamily: 'Amiri',
                            fontSize: 14,
                            color: isDarkMode ? Colors.white70 : Colors.black54,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // عنصر إضافي (مثل زر التبديل)
                  if (trailing != null)
                    Container(
                      margin: const EdgeInsets.only(right: 4),
                      child: trailing,
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // عرض مربع حوار لتغيير حجم الخط
  void _showFontSizeDialog(BuildContext context, bool isDarkMode) {
    final provider = context.read<ManhalRawiProvider>();
    double currentFontSize = provider.fontSize;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            backgroundColor: isDarkMode ? ManhalColors.blue900 : Colors.white,
            title: Text(
              'تغيير حجم الخط',
              style: TextStyle(
                fontFamily: 'Amiri',
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
              ),
              textAlign: TextAlign.center,
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // عرض مثال للخط
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? ManhalColors.blue800.withValues(alpha: 0.5)
                        : ManhalColors.gold100.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: isDarkMode
                          ? ManhalColors.gold500.withValues(alpha: 0.3)
                          : ManhalColors.gold300,
                      width: 1,
                    ),
                  ),
                  child: Text(
                    'بسم الله الرحمن الرحيم',
                    style: TextStyle(
                      fontFamily: provider.fontFamily,
                      fontSize: currentFontSize,
                      color: isDarkMode ? Colors.white : Colors.black87,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

                const SizedBox(height: 24),

                // شريط تمرير لتغيير الحجم
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'صغير',
                      style: TextStyle(
                        fontFamily: 'Amiri',
                        fontSize: 14,
                        color: isDarkMode ? Colors.white70 : Colors.black54,
                      ),
                    ),
                    Expanded(
                      child: Slider(
                        value: currentFontSize,
                        min: 12.0,
                        max: 24.0,
                        divisions: 6,
                        activeColor: isDarkMode
                            ? ManhalColors.gold500
                            : ManhalColors.primary,
                        inactiveColor: isDarkMode
                            ? ManhalColors.blue700
                            : ManhalColors.gold200,
                        onChanged: (value) {
                          setState(() {
                            currentFontSize = value;
                          });
                        },
                      ),
                    ),
                    Text(
                      'كبير',
                      style: TextStyle(
                        fontFamily: 'Amiri',
                        fontSize: 14,
                        color: isDarkMode ? Colors.white70 : Colors.black54,
                      ),
                    ),
                  ],
                ),

                // عرض الحجم الحالي
                Text(
                  'الحجم الحالي: ${currentFontSize.toStringAsFixed(1)}',
                  style: TextStyle(
                    fontFamily: 'Amiri',
                    fontSize: 14,
                    color: isDarkMode ? Colors.white70 : Colors.black54,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: Text(
                  'إلغاء',
                  style: TextStyle(
                    color: isDarkMode
                        ? ManhalColors.gold300
                        : ManhalColors.primary,
                  ),
                ),
              ),
              TextButton(
                onPressed: () {
                  provider.setFontSize(currentFontSize);
                  Navigator.pop(context);
                },
                child: Text(
                  'تأكيد',
                  style: TextStyle(
                    color: isDarkMode
                        ? ManhalColors.gold500
                        : ManhalColors.primary,
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  // عرض مربع حوار لاختيار عدد الأبيات في الصفحة الواحدة
  void _showVersesPerPageDialog(BuildContext context, bool isDarkMode) {
    final provider = context.read<ManhalRawiProvider>();
    int currentVersesPerPage = provider.versesPerPage;

    // قائمة الخيارات المتاحة لعدد الأبيات
    final List<int> availableOptions = [5, 10, 15, 20, 25, 30];

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return Dialog(
            backgroundColor: isDarkMode ? ManhalColors.blue900 : Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
              side: BorderSide(
                color: isDarkMode
                    ? ManhalColors.gold500.withValues(alpha: 0.3)
                    : ManhalColors.gold300,
                width: 1,
              ),
            ),
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // عنوان الحوار
                    Text(
                      'عدد الأبيات في الصفحة',
                      style: TextStyle(
                        fontFamily: 'Amiri',
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: isDarkMode
                            ? ManhalColors.gold500
                            : ManhalColors.primary,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 16),

                    // شرح الإعداد
                    Text(
                      'اختر عدد الأبيات التي تريد عرضها في الصفحة الواحدة.',
                      style: TextStyle(
                        fontFamily: 'Amiri',
                        fontSize: 14,
                        color: isDarkMode ? Colors.white70 : Colors.black54,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 16),

                    // عرض مثال للأبيات
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: isDarkMode
                            ? ManhalColors.blue800.withValues(alpha: 0.5)
                            : ManhalColors.gold100.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: isDarkMode
                              ? ManhalColors.gold500.withValues(alpha: 0.3)
                              : ManhalColors.gold300,
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.menu_book,
                            size: 24,
                            color: isDarkMode
                                ? ManhalColors.gold500
                                : ManhalColors.primary,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'عدد الأبيات: $currentVersesPerPage',
                            style: TextStyle(
                              fontFamily: provider.fontFamily,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: isDarkMode
                                  ? ManhalColors.gold500
                                  : ManhalColors.primary,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 16),

                    // خيارات عدد الأبيات
                    Wrap(
                      alignment: WrapAlignment.center,
                      spacing: 8,
                      runSpacing: 8,
                      children: availableOptions.map((option) {
                        final isSelected = option == currentVersesPerPage;
                        return InkWell(
                          onTap: () {
                            setState(() {
                              currentVersesPerPage = option;
                            });
                          },
                          child: Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? (isDarkMode
                                      ? ManhalColors.gold500
                                      : ManhalColors.primary)
                                  : (isDarkMode
                                      ? ManhalColors.blue800
                                          .withValues(alpha: 0.5)
                                      : Colors.white),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: isDarkMode
                                    ? ManhalColors.gold500
                                    : ManhalColors.primary,
                                width: 1,
                              ),
                            ),
                            child: Center(
                              child: Text(
                                option.toString(),
                                style: TextStyle(
                                  fontFamily: 'Amiri',
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: isSelected
                                      ? (isDarkMode
                                          ? Colors.black
                                          : Colors.white)
                                      : (isDarkMode
                                          ? ManhalColors.gold500
                                          : ManhalColors.primary),
                                ),
                              ),
                            ),
                          ),
                        );
                      }).toList(),
                    ),

                    const SizedBox(height: 16),

                    // أزرار الإجراءات
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        TextButton(
                          onPressed: () {
                            Navigator.pop(context);
                          },
                          child: Text(
                            'إلغاء',
                            style: TextStyle(
                              color: isDarkMode
                                  ? ManhalColors.gold300
                                  : ManhalColors.primary,
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        TextButton(
                          onPressed: () {
                            provider.setVersesPerPage(currentVersesPerPage);
                            Navigator.pop(context);
                          },
                          child: Text(
                            'تأكيد',
                            style: TextStyle(
                              color: isDarkMode
                                  ? ManhalColors.gold500
                                  : ManhalColors.primary,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  /// الحصول على اسم نوع عرض القصائد المخمسة
  String _getMukhammasDisplayTypeName(MukhammasDisplayType type) {
    switch (type) {
      case MukhammasDisplayType.standard:
        return 'النمط القياسي';
      case MukhammasDisplayType.luxury:
        return 'النمط الفاخر';
      case MukhammasDisplayType.classic:
        return 'النمط الكلاسيكي';
    }
  }

  /// الحصول على اسم نوع عرض القصائد العادية
  String _getVerseDisplayTypeName(VerseDisplayType type) {
    switch (type) {
      case VerseDisplayType.professional:
        return 'النمط الاحترافي';
      case VerseDisplayType.bookStyle:
        return 'نمط الكتاب المفتوح';
      case VerseDisplayType.classic:
        return 'النمط الكلاسيكي';
    }
  }

  /// الحصول على اسم العرض لنوع الخط
  String _getFontFamilyDisplayName(String fontFamily) {
    switch (fontFamily) {
      case 'Amiri':
        return 'أميري';
      case 'Scheherazade':
        return 'شهرزاد';
      case 'Lateef':
        return 'لطيف';
      case 'Noto Naskh Arabic':
        return 'نسخ';
      case 'Noto Kufi Arabic':
        return 'كوفي';
      case 'Cairo':
        return 'القاهرة';
      default:
        return fontFamily;
    }
  }

  /// عرض مربع حوار لاختيار نوع عرض القصائد المخمسة
  void _showMukhammasDisplayTypeDialog(BuildContext context, bool isDarkMode) {
    final provider = context.read<ManhalRawiProvider>();

    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: isDarkMode ? ManhalColors.blue900 : Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(
            color: isDarkMode
                ? ManhalColors.gold500.withValues(alpha: 0.3)
                : ManhalColors.gold300,
            width: 1,
          ),
        ),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // عنوان الحوار
                Text(
                  'اختر نمط عرض القصائد المخمسة',
                  style: IslamicTypography.luxuryTitle(
                    isDark: isDarkMode,
                    fontSize: 18,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 16),

                // النمط القياسي
                _buildDisplayTypeOption(
                  context,
                  isDarkMode,
                  title: 'النمط القياسي',
                  subtitle: 'عرض الأبيات بالطريقة القياسية',
                  value: MukhammasDisplayType.standard,
                  groupValue: provider.mukhammasDisplayType,
                  onChanged: (value) {
                    provider.updateMukhammasDisplayType(value!);
                    Navigator.of(context).pop();
                  },
                ),

                // النمط الفاخر
                _buildDisplayTypeOption(
                  context,
                  isDarkMode,
                  title: 'النمط الفاخر',
                  subtitle: 'عرض الأبيات بطريقة فاخرة ومتطورة',
                  value: MukhammasDisplayType.luxury,
                  groupValue: provider.mukhammasDisplayType,
                  onChanged: (value) {
                    provider.updateMukhammasDisplayType(value!);
                    Navigator.of(context).pop();
                  },
                ),

                // النمط الكلاسيكي
                _buildDisplayTypeOption(
                  context,
                  isDarkMode,
                  title: 'النمط الكلاسيكي',
                  subtitle: 'عرض الأبيات بطريقة كلاسيكية فاخرة',
                  value: MukhammasDisplayType.classic,
                  groupValue: provider.mukhammasDisplayType,
                  onChanged: (value) {
                    provider.updateMukhammasDisplayType(value!);
                    Navigator.of(context).pop();
                  },
                ),

                const SizedBox(height: 8),

                // زر الإلغاء
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: Text(
                    'إلغاء',
                    style: TextStyle(
                      fontFamily: 'Amiri',
                      fontSize: 14,
                      color: isDarkMode
                          ? ManhalColors.gold300
                          : ManhalColors.primary,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// عرض مربع حوار لاختيار نمط عرض القصائد العادية
  void _showVerseDisplayTypeDialog(BuildContext context, bool isDarkMode) {
    final provider = context.read<ManhalRawiProvider>();

    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: isDarkMode ? ManhalColors.blue900 : Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(
            color: isDarkMode
                ? ManhalColors.gold500.withValues(alpha: 0.3)
                : ManhalColors.gold300,
            width: 1,
          ),
        ),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // عنوان الحوار
                Text(
                  'اختر نمط عرض القصائد العادية',
                  style: IslamicTypography.luxuryTitle(
                    isDark: isDarkMode,
                    fontSize: 18,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 16),

                // النمط الاحترافي
                _buildDisplayTypeOption(
                  context,
                  isDarkMode,
                  title: 'النمط الاحترافي',
                  subtitle: 'عرض الأبيات بطريقة احترافية متقابلة',
                  value: VerseDisplayType.professional,
                  groupValue: provider.verseDisplayType,
                  onChanged: (value) {
                    provider.updateVerseDisplayType(value!);
                    Navigator.of(context).pop();
                  },
                ),

                // نمط الكتاب المفتوح
                _buildDisplayTypeOption(
                  context,
                  isDarkMode,
                  title: 'نمط الكتاب المفتوح',
                  subtitle: 'عرض الأبيات على شكل كتاب مفتوح',
                  value: VerseDisplayType.bookStyle,
                  groupValue: provider.verseDisplayType,
                  onChanged: (value) {
                    provider.updateVerseDisplayType(value!);
                    Navigator.of(context).pop();
                  },
                ),

                // النمط الكلاسيكي
                _buildDisplayTypeOption(
                  context,
                  isDarkMode,
                  title: 'النمط الكلاسيكي',
                  subtitle: 'عرض الأبيات بطريقة كلاسيكية فاخرة',
                  value: VerseDisplayType.classic,
                  groupValue: provider.verseDisplayType,
                  onChanged: (value) {
                    provider.updateVerseDisplayType(value!);
                    Navigator.of(context).pop();
                  },
                ),

                const SizedBox(height: 8),

                // زر الإلغاء
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: Text(
                    'إلغاء',
                    style: TextStyle(
                      color: isDarkMode
                          ? ManhalColors.gold300
                          : ManhalColors.primary,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء خيار نمط العرض
  Widget _buildDisplayTypeOption<T>(
    BuildContext context,
    bool isDarkMode, {
    required String title,
    required String subtitle,
    required T value,
    required T groupValue,
    required ValueChanged<T?> onChanged,
  }) {
    return InkWell(
      onTap: () => onChanged(value),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 4),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: value == groupValue
              ? (isDarkMode
                  ? ManhalColors.gold500.withValues(alpha: 0.2)
                  : ManhalColors.primary.withValues(alpha: 0.1))
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: value == groupValue
                ? (isDarkMode ? ManhalColors.gold500 : ManhalColors.primary)
                : Colors.transparent,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Radio<T>(
              value: value,
              groupValue: groupValue,
              activeColor:
                  isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
              onChanged: onChanged,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: IslamicTypography.luxuryBody(
                      isDark: isDarkMode,
                      fontSize: 16,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: IslamicTypography.luxuryCaption(
                      isDark: isDarkMode,
                      fontSize: 13,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // عرض مربع حوار لاختيار نوع الخط
  void _showFontFamilyDialog(BuildContext context, bool isDarkMode) {
    final provider = context.read<ManhalRawiProvider>();
    String currentFontFamily = provider.fontFamily;

    // قائمة الخطوط المتاحة من Google Fonts
    final List<Map<String, String>> availableFonts = [
      {'name': 'أميري', 'family': 'Amiri'},
      {'name': 'القاهرة', 'family': 'Cairo'},
      {'name': 'ماركازي', 'family': 'Markazi'},
      {'name': 'شهرزاد', 'family': 'Raqq'}, // يستخدم Scheherazade New كبديل
      {'name': 'نسخ عربي', 'family': 'Naskh'}, // يستخدم Noto Naskh Arabic كبديل
    ];

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return Dialog(
            backgroundColor: isDarkMode ? ManhalColors.blue900 : Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
              side: BorderSide(
                color: isDarkMode
                    ? ManhalColors.gold500.withValues(alpha: 0.3)
                    : ManhalColors.gold300,
                width: 1,
              ),
            ),
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // عنوان الحوار
                    Text(
                      'اختيار نوع الخط',
                      style: TextStyle(
                        fontFamily: 'Amiri',
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: isDarkMode
                            ? ManhalColors.gold500
                            : ManhalColors.primary,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 16),

                    // قائمة الخطوط
                    ConstrainedBox(
                      constraints: BoxConstraints(
                        maxHeight: MediaQuery.of(context).size.height * 0.4,
                      ),
                      child: ListView.builder(
                        shrinkWrap: true,
                        itemCount: availableFonts.length,
                        itemBuilder: (context, index) {
                          final font = availableFonts[index];
                          final isSelected =
                              font['family'] == currentFontFamily;

                          return InkWell(
                            onTap: () {
                              setState(() {
                                currentFontFamily = font['family']!;
                              });
                            },
                            child: Container(
                              margin: const EdgeInsets.symmetric(vertical: 4),
                              padding: const EdgeInsets.symmetric(
                                vertical: 10,
                                horizontal: 12,
                              ),
                              decoration: BoxDecoration(
                                color: isSelected
                                    ? (isDarkMode
                                        ? ManhalColors.blue800
                                            .withValues(alpha: 0.7)
                                        : ManhalColors.gold100
                                            .withValues(alpha: 0.5))
                                    : Colors.transparent,
                                borderRadius: BorderRadius.circular(8),
                                border: isSelected
                                    ? Border.all(
                                        color: isDarkMode
                                            ? ManhalColors.gold500
                                            : ManhalColors.primary,
                                        width: 1,
                                      )
                                    : null,
                              ),
                              child: Row(
                                children: [
                                  // أيقونة التحديد
                                  Icon(
                                    isSelected
                                        ? Icons.radio_button_checked
                                        : Icons.radio_button_unchecked,
                                    color: isSelected
                                        ? (isDarkMode
                                            ? ManhalColors.gold500
                                            : ManhalColors.primary)
                                        : (isDarkMode
                                            ? Colors.white70
                                            : Colors.black54),
                                    size: 20,
                                  ),

                                  const SizedBox(width: 12),

                                  // اسم الخط
                                  Expanded(
                                    child: Text(
                                      font['name']!,
                                      style: _getFontStyle(
                                        fontFamily: font['family']!,
                                        fontSize: 16,
                                        color: isDarkMode
                                            ? Colors.white
                                            : Colors.black87,
                                      ),
                                    ),
                                  ),

                                  // مثال على الخط
                                  Text(
                                    'أبجد',
                                    style: _getFontStyle(
                                      fontFamily: font['family']!,
                                      fontSize: 14,
                                      color: isDarkMode
                                          ? Colors.white70
                                          : Colors.black54,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),

                    const SizedBox(height: 16),

                    // أزرار الإجراءات
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        TextButton(
                          onPressed: () {
                            Navigator.pop(context);
                          },
                          child: Text(
                            'إلغاء',
                            style: TextStyle(
                              color: isDarkMode
                                  ? ManhalColors.gold300
                                  : ManhalColors.primary,
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        TextButton(
                          onPressed: () {
                            provider.setFontFamily(currentFontFamily);
                            Navigator.pop(context);
                          },
                          child: Text(
                            'تأكيد',
                            style: TextStyle(
                              color: isDarkMode
                                  ? ManhalColors.gold500
                                  : ManhalColors.primary,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  // دالة مساعدة للحصول على نمط الخط المناسب باستخدام الخطوط المحلية
  TextStyle _getFontStyle({
    required String fontFamily,
    required double fontSize,
    required Color color,
    FontWeight fontWeight = FontWeight.normal,
  }) {
    switch (fontFamily) {
      case 'Amiri':
        return TextStyle(
          fontFamily: 'Amiri',
          fontSize: fontSize,
          color: color,
          fontWeight: fontWeight,
        );
      case 'Cairo':
        return TextStyle(
          fontFamily: 'Cairo',
          fontSize: fontSize,
          color: color,
          fontWeight: fontWeight,
        );
      case 'Markazi':
        return TextStyle(
          fontFamily: 'Markazi',
          fontSize: fontSize,
          color: color,
          fontWeight: fontWeight,
        );
      case 'Raqq':
        return TextStyle(
          fontFamily: 'Scheherazade',
          fontSize: fontSize,
          color: color,
          fontWeight: fontWeight,
        );
      case 'Naskh':
        return TextStyle(
          fontFamily: 'Naskh',
          fontSize: fontSize,
          color: color,
          fontWeight: fontWeight,
        );
      default:
        return TextStyle(
          fontFamily: 'Amiri',
          fontSize: fontSize,
          color: color,
          fontWeight: fontWeight,
        );
    }
  }
}
