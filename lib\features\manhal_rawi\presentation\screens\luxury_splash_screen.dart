import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/manhal_rawi_provider.dart';
import 'luxury_home_screen.dart';
import '../../../../utils/theme/manhal_colors.dart';
import '../../../../utils/theme/islamic_typography.dart';
import '../../../../utils/theme/islamic_patterns.dart';
import '../../../../utils/animations/islamic_animations.dart';
import '../../../../utils/helpers/responsive_helper.dart';

/// شاشة البداية الفاخرة للتطبيق
class LuxurySplashScreen extends StatefulWidget {
  const LuxurySplashScreen({super.key});

  @override
  State<LuxurySplashScreen> createState() => _LuxurySplashScreenState();
}

class _LuxurySplashScreenState extends State<LuxurySplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotateAnimation;
  late Animation<double> _patternAnimation;

  // مؤقت للانتقال إلى الشاشة الرئيسية
  Timer? _navigationTimer;

  @override
  void initState() {
    super.initState();

    // إنشاء متحكم الرسوم المتحركة
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 3000),
    );

    // رسوم متحركة للتلاشي
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
      ),
    );

    // رسوم متحركة للتكبير
    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.2, 0.7, curve: Curves.easeOutBack),
      ),
    );

    // رسوم متحركة للدوران
    _rotateAnimation = Tween<double>(begin: 0.0, end: 0.05).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.3, 0.8, curve: Curves.easeInOut),
      ),
    );

    // رسوم متحركة للزخارف
    _patternAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.5, 1.0, curve: Curves.easeOut),
      ),
    );

    // بدء الرسوم المتحركة
    _controller.forward();

    // تحميل البيانات الأولية
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ManhalRawiProvider>().loadInitialData();
    });

    // ضبط مؤقت للانتقال إلى الشاشة الرئيسية
    _navigationTimer = Timer(const Duration(milliseconds: 4500), () {
      if (mounted) {
        Navigator.of(context).pushReplacement(
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) =>
                const LuxuryHomeScreen(),
            transitionsBuilder:
                (context, animation, secondaryAnimation, child) {
              return FadeTransition(
                opacity: animation,
                child: child,
              );
            },
            transitionDuration: const Duration(milliseconds: 800),
          ),
        );
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _navigationTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = context.watch<ManhalRawiProvider>().isDarkMode;
    final size = MediaQuery.of(context).size;

    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: isDarkMode
                ? [
                    ManhalColors.blue900,
                    ManhalColors.blue800,
                  ]
                : [
                    Colors.white,
                    ManhalColors.gold100.withValues(alpha: 0.3),
                  ],
          ),
          // تعليق الصورة الخلفية لتجنب الأخطاء
          // image: DecorationImage(
          //   image: AssetImage(
          //     isDarkMode
          //         ? 'assets/images/islamic_pattern_dark.png'
          //         : 'assets/images/islamic_pattern_light.png',
          //   ),
          //   opacity: 0.05,
          //   fit: BoxFit.cover,
          // ),
        ),
        child: Stack(
          children: [
            // زخارف الخلفية
            AnimatedBuilder(
              animation: _patternAnimation,
              builder: (context, child) {
                return Opacity(
                  opacity: _patternAnimation.value * 0.3,
                  child: CustomPaint(
                    painter: _SplashPatternPainter(
                      isDark: isDarkMode,
                      progress: _patternAnimation.value,
                    ),
                    size: Size.infinite,
                  ),
                );
              },
            ),

            // الهلال العلوي
            Positioned(
              top: size.height * 0.05,
              right: size.width * 0.1,
              child: AnimatedBuilder(
                animation: _fadeAnimation,
                builder: (context, child) {
                  return Opacity(
                    opacity: _fadeAnimation.value * 0.7,
                    child: Transform.rotate(
                      angle: -0.3,
                      child: IslamicPatterns.getCrescentDecoration(
                        isDark: isDarkMode,
                        size: ResponsiveHelper.getResponsiveIconSize(
                          context,
                          defaultSize: 60.0,
                        ),
                        opacity: 0.8,
                      ),
                    ),
                  );
                },
              ),
            ),

            // الهلال السفلي
            Positioned(
              bottom: size.height * 0.05,
              left: size.width * 0.1,
              child: AnimatedBuilder(
                animation: _fadeAnimation,
                builder: (context, child) {
                  return Opacity(
                    opacity: _fadeAnimation.value * 0.7,
                    child: Transform.rotate(
                      angle: 0.3,
                      child: IslamicPatterns.getCrescentDecoration(
                        isDark: isDarkMode,
                        size: ResponsiveHelper.getResponsiveIconSize(
                          context,
                          defaultSize: 40.0,
                        ),
                        opacity: 0.8,
                      ),
                    ),
                  );
                },
              ),
            ),

            // المحتوى الرئيسي
            Center(
              child: AnimatedBuilder(
                animation: _controller,
                builder: (context, child) {
                  return Opacity(
                    opacity: _fadeAnimation.value,
                    child: Transform.scale(
                      scale: _scaleAnimation.value,
                      child: Transform.rotate(
                        angle: _rotateAnimation.value * (isDarkMode ? 1 : -1),
                        child: child,
                      ),
                    ),
                  );
                },
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // الشعار
                    _buildLogo(isDarkMode),

                    SizedBox(
                      height: ResponsiveHelper.getResponsiveSpacing(
                        context,
                        defaultValue: 40.0,
                      ),
                    ),

                    // عنوان التطبيق
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: ResponsiveHelper.getResponsiveSpacing(
                          context,
                          defaultValue: 20.0,
                        ),
                        vertical: ResponsiveHelper.getResponsiveSpacing(
                          context,
                          defaultValue: 10.0,
                        ),
                      ),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(
                          ResponsiveHelper.getResponsiveBorderRadius(
                            context,
                            defaultValue: 20.0,
                          ),
                        ),
                        border: Border.all(
                          color: isDarkMode
                              ? ManhalColors.gold500.withValues(alpha: 0.3)
                              : ManhalColors.primary.withValues(alpha: 0.2),
                          width: ResponsiveHelper.getResponsiveBorderWidth(
                            context,
                            defaultValue: 1.5,
                          ),
                        ),
                        gradient: LinearGradient(
                          begin: Alignment.topRight,
                          end: Alignment.bottomLeft,
                          colors: isDarkMode
                              ? [
                                  ManhalColors.blue800.withValues(alpha: 0.5),
                                  ManhalColors.blue900.withValues(alpha: 0.3),
                                ]
                              : [
                                  Colors.white.withValues(alpha: 0.7),
                                  ManhalColors.gold100.withValues(alpha: 0.3),
                                ],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: (isDarkMode
                                    ? ManhalColors.gold500
                                    : ManhalColors.primary)
                                .withValues(alpha: 0.2),
                            blurRadius: ResponsiveHelper.getResponsiveShadow(
                              context,
                              defaultBlurRadius: 10.0,
                            )[0],
                            spreadRadius: ResponsiveHelper.getResponsiveShadow(
                              context,
                              defaultSpreadRadius: 1.0,
                            )[1],
                            offset: Offset(
                                0,
                                ResponsiveHelper.getResponsiveShadow(
                                  context,
                                  defaultYOffset: 0.0,
                                )[2]),
                          ),
                        ],
                      ),
                      child: IslamicAnimations.arabicTextTyping(
                        text: 'المنهل الراوي',
                        style: IslamicTypography.luxuryBookTitle(
                          isDark: isDarkMode,
                          fontSize: ResponsiveHelper.getResponsiveFontSize(
                            context,
                            fontSize: 40.0,
                          ),
                        ),
                        durationMs: 1500,
                        delayMs: 500,
                      ),
                    ),

                    SizedBox(
                      height: ResponsiveHelper.getResponsiveSpacing(
                        context,
                        defaultValue: 16.0,
                      ),
                    ),

                    // اسم المؤلف
                    AnimatedBuilder(
                      animation: _fadeAnimation,
                      builder: (context, child) {
                        return Opacity(
                          opacity: _fadeAnimation.value,
                          child: child,
                        );
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: ResponsiveHelper.getResponsiveSpacing(
                            context,
                            defaultValue: 16.0,
                          ),
                          vertical: ResponsiveHelper.getResponsiveSpacing(
                            context,
                            defaultValue: 8.0,
                          ),
                        ),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(
                            ResponsiveHelper.getResponsiveBorderRadius(
                              context,
                              defaultValue: 16.0,
                            ),
                          ),
                          color: isDarkMode
                              ? ManhalColors.blue900.withValues(alpha: 0.3)
                              : Colors.white.withValues(alpha: 0.3),
                          border: Border.all(
                            color: isDarkMode
                                ? ManhalColors.gold500.withValues(alpha: 0.2)
                                : ManhalColors.primary.withValues(alpha: 0.1),
                            width: ResponsiveHelper.getResponsiveBorderWidth(
                              context,
                              defaultValue: 1.0,
                            ),
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.person,
                              size: ResponsiveHelper.getResponsiveIconSize(
                                context,
                                defaultSize: 16.0,
                              ),
                              color: isDarkMode
                                  ? ManhalColors.gold300
                                  : ManhalColors.primary,
                            ),
                            SizedBox(
                              width: ResponsiveHelper.getResponsiveSpacing(
                                context,
                                defaultValue: 8.0,
                              ),
                            ),
                            Text(
                              'محمد هزاع باعلوي الحضرمي',
                              style: IslamicTypography.luxurySubtitle(
                                isDark: isDarkMode,
                                fontSize:
                                    ResponsiveHelper.getResponsiveFontSize(
                                  context,
                                  fontSize: 18.0,
                                ),
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),

                    SizedBox(
                      height: ResponsiveHelper.getResponsiveSpacing(
                        context,
                        defaultValue: 60.0,
                      ),
                    ),

                    // مؤشر التحميل
                    AnimatedBuilder(
                      animation: _controller,
                      builder: (context, child) {
                        return Opacity(
                          opacity: _fadeAnimation.value,
                          child: _buildLoadingIndicator(isDarkMode),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء شعار التطبيق
  Widget _buildLogo(bool isDarkMode) {
    final logoSize = 120.0 * ResponsiveHelper.getScaleFactor(context);

    return Container(
      width: logoSize,
      height: logoSize,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          colors: isDarkMode
              ? [
                  ManhalColors.blue800,
                  ManhalColors.blue900,
                ]
              : [
                  Colors.white,
                  ManhalColors.gold100.withValues(alpha: 0.5),
                ],
          radius: 0.8,
        ),
        boxShadow: [
          BoxShadow(
            color: (isDarkMode ? ManhalColors.gold500 : ManhalColors.primary)
                .withValues(alpha: 0.3),
            blurRadius: ResponsiveHelper.getResponsiveShadow(
              context,
              defaultBlurRadius: 20.0,
            )[0],
            spreadRadius: ResponsiveHelper.getResponsiveShadow(
              context,
              defaultSpreadRadius: 2.0,
            )[1],
            offset: Offset(
                0,
                ResponsiveHelper.getResponsiveShadow(
                  context,
                  defaultYOffset: 0.0,
                )[2]),
          ),
        ],
        border: Border.all(
          color: isDarkMode
              ? ManhalColors.gold500.withValues(alpha: 0.5)
              : ManhalColors.primary.withValues(alpha: 0.3),
          width: ResponsiveHelper.getResponsiveBorderWidth(
            context,
            defaultValue: 2.0,
          ),
        ),
      ),
      child: Center(
        child: Icon(
          Icons.auto_stories,
          size: ResponsiveHelper.getResponsiveIconSize(
            context,
            defaultSize: 60.0,
          ),
          color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
        ),
      ),
    );
  }

  /// بناء مؤشر التحميل
  Widget _buildLoadingIndicator(bool isDarkMode) {
    final indicatorSize = 60.0 * ResponsiveHelper.getScaleFactor(context);
    final progressSize = 40.0 * ResponsiveHelper.getScaleFactor(context);

    return Column(
      children: [
        // مؤشر التحميل المخصص
        Container(
          width: indicatorSize,
          height: indicatorSize,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isDarkMode
                ? ManhalColors.blue800.withValues(alpha: 0.3)
                : Colors.white.withValues(alpha: 0.5),
            border: Border.all(
              color: isDarkMode
                  ? ManhalColors.gold500.withValues(alpha: 0.5)
                  : ManhalColors.primary.withValues(alpha: 0.3),
              width: ResponsiveHelper.getResponsiveBorderWidth(
                context,
                defaultValue: 1.0,
              ),
            ),
            boxShadow: [
              BoxShadow(
                color:
                    (isDarkMode ? ManhalColors.gold500 : ManhalColors.primary)
                        .withValues(alpha: 0.2),
                blurRadius: ResponsiveHelper.getResponsiveShadow(
                  context,
                  defaultBlurRadius: 10.0,
                )[0],
                spreadRadius: ResponsiveHelper.getResponsiveShadow(
                  context,
                  defaultSpreadRadius: 1.0,
                )[1],
                offset: Offset(
                    0,
                    ResponsiveHelper.getResponsiveShadow(
                      context,
                      defaultYOffset: 0.0,
                    )[2]),
              ),
            ],
          ),
          child: Center(
            child: SizedBox(
              width: progressSize,
              height: progressSize,
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
                ),
                strokeWidth: 3 * ResponsiveHelper.getScaleFactor(context),
              ),
            ),
          ),
        ),

        SizedBox(
          height: ResponsiveHelper.getResponsiveSpacing(
            context,
            defaultValue: 16.0,
          ),
        ),

        // نص التحميل
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: ResponsiveHelper.getResponsiveSpacing(
              context,
              defaultValue: 16.0,
            ),
            vertical: ResponsiveHelper.getResponsiveSpacing(
              context,
              defaultValue: 8.0,
            ),
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(
              ResponsiveHelper.getResponsiveBorderRadius(
                context,
                defaultValue: 16.0,
              ),
            ),
            color: isDarkMode
                ? ManhalColors.blue900.withValues(alpha: 0.5)
                : Colors.white.withValues(alpha: 0.5),
            border: Border.all(
              color: isDarkMode
                  ? ManhalColors.gold500.withValues(alpha: 0.2)
                  : ManhalColors.primary.withValues(alpha: 0.1),
              width: ResponsiveHelper.getResponsiveBorderWidth(
                context,
                defaultValue: 1.0,
              ),
            ),
          ),
          child: Text(
            'جاري تحميل الديوان...',
            style: IslamicTypography.luxuryCaption(
              isDark: isDarkMode,
              fontSize: ResponsiveHelper.getResponsiveFontSize(
                context,
                fontSize: 16.0,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

/// رسام الزخارف الإسلامية لشاشة البداية
class _SplashPatternPainter extends CustomPainter {
  final bool isDark;
  final double progress;

  _SplashPatternPainter({
    required this.isDark,
    required this.progress,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final baseColor = isDark ? ManhalColors.gold500 : ManhalColors.primary;

    // استخدام قيمة متغيرة تتناسب مع حجم الشاشة
    final scaleFactor = size.width / 375; // قيمة مرجعية للتناسب مع حجم الشاشة
    final strokeWidth = 1.0 * scaleFactor;

    final paint = Paint()
      ..color = baseColor.withValues(alpha: 0.1)
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    final width = size.width;
    final height = size.height;

    // رسم الزخارف في الزوايا
    _drawCornerPattern(canvas, paint, Offset(0, 0), size, progress);
    _drawCornerPattern(canvas, paint, Offset(width, 0), size, progress);
    _drawCornerPattern(canvas, paint, Offset(0, height), size, progress);
    _drawCornerPattern(canvas, paint, Offset(width, height), size, progress);

    // رسم الزخارف في المنتصف
    _drawCenterPattern(
        canvas, paint, Offset(width / 2, height / 2), size, progress);
  }

  void _drawCornerPattern(
      Canvas canvas, Paint paint, Offset corner, Size size, double progress) {
    final patternSize = size.width * 0.2 * progress;
    final center = Offset(
      corner.dx == 0
          ? corner.dx + patternSize / 2
          : corner.dx - patternSize / 2,
      corner.dy == 0
          ? corner.dy + patternSize / 2
          : corner.dy - patternSize / 2,
    );

    // رسم الزخرفة الإسلامية الهندسية
    final path = Path();
    final sides = 8;
    final radius = patternSize / 2;

    for (int i = 0; i < sides; i++) {
      final angle = (i * 2 * 3.14159) / sides;
      final point = Offset(
        center.dx + radius * 0.8 * math.cos(angle),
        center.dy + radius * 0.8 * math.sin(angle),
      );

      if (i == 0) {
        path.moveTo(point.dx, point.dy);
      } else {
        path.lineTo(point.dx, point.dy);
      }
    }

    path.close();
    canvas.drawPath(path, paint);

    // رسم الزخرفة الداخلية
    final innerPath = Path();
    final innerRadius = radius * 0.6;

    for (int i = 0; i < sides; i++) {
      final angle = (i * 2 * 3.14159) / sides + 3.14159 / sides;
      final point = Offset(
        center.dx + innerRadius * math.cos(angle),
        center.dy + innerRadius * math.sin(angle),
      );

      if (i == 0) {
        innerPath.moveTo(point.dx, point.dy);
      } else {
        innerPath.lineTo(point.dx, point.dy);
      }
    }

    innerPath.close();
    canvas.drawPath(innerPath, paint);

    // ربط النقاط
    for (int i = 0; i < sides; i++) {
      final outerAngle = (i * 2 * 3.14159) / sides;
      final innerAngle = (i * 2 * 3.14159) / sides + 3.14159 / sides;

      final outerPoint = Offset(
        center.dx + radius * 0.8 * math.cos(outerAngle),
        center.dy + radius * 0.8 * math.sin(outerAngle),
      );

      final innerPoint = Offset(
        center.dx + innerRadius * math.cos(innerAngle),
        center.dy + innerRadius * math.sin(innerAngle),
      );

      canvas.drawLine(outerPoint, innerPoint, paint);
    }
  }

  void _drawCenterPattern(
      Canvas canvas, Paint paint, Offset center, Size size, double progress) {
    final radius = size.width < size.height ? size.width / 6 : size.height / 6;
    final adjustedRadius = radius * progress;

    // رسم النجمة الإسلامية الثمانية
    final path = Path();
    final points = <Offset>[];
    final sides = 8;

    for (int i = 0; i < sides; i++) {
      final angle = (i * 2 * 3.14159) / sides;
      points.add(Offset(
        center.dx + adjustedRadius * math.cos(angle),
        center.dy + adjustedRadius * math.sin(angle),
      ));
    }

    // رسم النجمة الأساسية
    path.moveTo(points[0].dx, points[0].dy);
    for (int i = 1; i < points.length; i++) {
      path.lineTo(points[i].dx, points[i].dy);
    }
    path.close();

    // رسم النجمة الداخلية
    final innerPoints = <Offset>[];
    final innerRadius = adjustedRadius * 0.6;

    for (int i = 0; i < sides; i++) {
      final angle = (i * 2 * 3.14159) / sides + 3.14159 / sides;
      innerPoints.add(Offset(
        center.dx + innerRadius * math.cos(angle),
        center.dy + innerRadius * math.sin(angle),
      ));
    }

    path.moveTo(innerPoints[0].dx, innerPoints[0].dy);
    for (int i = 1; i < innerPoints.length; i++) {
      path.lineTo(innerPoints[i].dx, innerPoints[i].dy);
    }
    path.close();

    // ربط النقاط
    for (int i = 0; i < sides; i++) {
      path.moveTo(points[i].dx, points[i].dy);
      path.lineTo(innerPoints[i].dx, innerPoints[i].dy);
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}
