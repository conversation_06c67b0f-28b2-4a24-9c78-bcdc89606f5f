#!/usr/bin/env python3
"""
سكريبت لتحديث معرفات التصنيفات في ملف manhal_rawi_poems.json
لتتوافق مع التحديثات الجديدة في قاعدة البيانات
"""

import json
import re

def update_category_ids():
    # قراءة الملف
    with open('assets/data/manhal_rawi_poems.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # خريطة التحويل من القديم إلى الجديد
    # القديم -> الجديد
    category_mapping = {
        1: 2,  # مدح: من 1 إلى 2
        2: 3,  # وطنية: من 2 إلى 3  
        3: 4,  # حكمة: من 3 إلى 4
        4: 5,  # مخمس: من 4 إلى 5
    }
    
    # تحديث القصائد
    poems_updated = 0
    for poem in data.get('poems', []):
        if 'categoryId' in poem:
            old_category = poem['categoryId']
            if old_category in category_mapping:
                poem['categoryId'] = category_mapping[old_category]
                poems_updated += 1
                print(f"تم تحديث القصيدة '{poem['title']}' من التصنيف {old_category} إلى {category_mapping[old_category]}")
    
    # حفظ الملف المحدث
    with open('assets/data/manhal_rawi_poems.json', 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    
    print(f"\nتم تحديث {poems_updated} قصيدة بنجاح!")
    
    # طباعة ملخص التصنيفات الحالية
    print("\nالتصنيفات الحالية:")
    for category in data.get('categories', []):
        print(f"ID {category['id']}: {category['name']}")

if __name__ == "__main__":
    update_category_ids()
