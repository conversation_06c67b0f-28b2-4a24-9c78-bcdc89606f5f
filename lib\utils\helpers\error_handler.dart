import 'package:flutter/material.dart';
import 'error_logger.dart';
import '../theme/manhal_colors.dart';
import '../theme/islamic_typography.dart';
import 'responsive_helper.dart';

/// عرض رسالة للمستخدم
void showErrorMessage(BuildContext context, String message) {
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Text(
        message,
        style: TextStyle(
          fontFamily: 'Amiri',
          fontSize:
              ResponsiveHelper.getResponsiveFontSize(context, fontSize: 14.0),
        ),
        textAlign: TextAlign.center,
      ),
      duration: const Duration(seconds: 2),
      behavior: SnackBarBehavior.floating,
    ),
  );
}

/// آلية موحدة لمعالجة الأخطاء في التطبيق
class ErrorHandler {
  static final ErrorHandler _instance = ErrorHandler._internal();
  factory ErrorHandler() => _instance;
  ErrorHandler._internal();

  final ErrorLogger _logger = ErrorLogger();

  /// معالجة خطأ عام
  void handleError(
    String source,
    String message,
    dynamic error,
    StackTrace? stackTrace,
  ) {
    // تسجيل الخطأ
    _logger.logError(source, message, error, stackTrace);
  }

  /// معالجة خطأ في قاعدة البيانات
  void handleDatabaseError(
    String operation,
    dynamic error,
    StackTrace? stackTrace,
  ) {
    final message = 'فشل في عملية $operation في قاعدة البيانات';
    _logger.logError('Database', message, error, stackTrace);
  }

  /// معالجة خطأ في الشبكة
  void handleNetworkError(
    String operation,
    dynamic error,
    StackTrace? stackTrace,
  ) {
    final message = 'فشل في عملية $operation عبر الشبكة';
    _logger.logError('Network', message, error, stackTrace);
  }

  /// معالجة خطأ في واجهة المستخدم
  void handleUIError(
    String operation,
    dynamic error,
    StackTrace? stackTrace,
  ) {
    final message = 'فشل في عملية $operation في واجهة المستخدم';
    _logger.logError('UI', message, error, stackTrace);
  }

  /// عرض رسالة خطأ للمستخدم
  void showErrorSnackBar(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 3),
    SnackBarAction? action,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: IslamicTypography.luxuryBody(
            isDark: isDarkMode,
            fontSize:
                ResponsiveHelper.getResponsiveFontSize(context, fontSize: 14.0),
          ).copyWith(
            color: Colors.white,
          ),
          textAlign: TextAlign.center,
        ),
        backgroundColor: Colors.red.shade700,
        duration: duration,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
            ResponsiveHelper.getResponsiveBorderRadius(context,
                defaultValue: 10.0),
          ),
        ),
        margin: EdgeInsets.all(
          ResponsiveHelper.getResponsiveSpacing(context, defaultValue: 16.0),
        ),
        action: action,
      ),
    );
  }

  /// عرض حوار خطأ للمستخدم
  Future<void> showErrorDialog(
    BuildContext context,
    String title,
    String message, {
    String? buttonText,
    VoidCallback? onPressed,
  }) async {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: isDarkMode ? ManhalColors.blue900 : Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
            ResponsiveHelper.getResponsiveBorderRadius(context,
                defaultValue: 16.0),
          ),
          side: BorderSide(
            color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
            width: ResponsiveHelper.getResponsiveBorderWidth(context,
                defaultValue: 1.0),
          ),
        ),
        title: Text(
          title,
          style: IslamicTypography.luxurySubtitle(
            isDark: isDarkMode,
            fontSize:
                ResponsiveHelper.getResponsiveFontSize(context, fontSize: 20.0),
          ),
          textAlign: TextAlign.center,
        ),
        content: Text(
          message,
          style: IslamicTypography.luxuryBody(
            isDark: isDarkMode,
          ),
          textAlign: TextAlign.center,
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              if (onPressed != null) {
                onPressed();
              }
            },
            child: Text(
              buttonText ?? 'حسناً',
              style: IslamicTypography.luxuryButton(
                isDark: isDarkMode,
              ).copyWith(
                color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// عرض حوار خطأ مع خيار إعادة المحاولة
  Future<void> showRetryDialog(
    BuildContext context,
    String title,
    String message,
    VoidCallback onRetry, {
    String retryText = 'إعادة المحاولة',
    String cancelText = 'إلغاء',
  }) async {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: isDarkMode ? ManhalColors.blue900 : Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
            ResponsiveHelper.getResponsiveBorderRadius(context,
                defaultValue: 16.0),
          ),
          side: BorderSide(
            color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
            width: ResponsiveHelper.getResponsiveBorderWidth(context,
                defaultValue: 1.0),
          ),
        ),
        title: Text(
          title,
          style: IslamicTypography.luxurySubtitle(
            isDark: isDarkMode,
            fontSize:
                ResponsiveHelper.getResponsiveFontSize(context, fontSize: 20.0),
          ),
          textAlign: TextAlign.center,
        ),
        content: Text(
          message,
          style: IslamicTypography.luxuryBody(
            isDark: isDarkMode,
          ),
          textAlign: TextAlign.center,
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: Text(
              cancelText,
              style: IslamicTypography.luxuryButton(
                isDark: isDarkMode,
              ).copyWith(
                color: isDarkMode
                    ? ManhalColors.textMuted
                    : ManhalColors.textMuted,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              onRetry();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor:
                  isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(
                  ResponsiveHelper.getResponsiveBorderRadius(context,
                      defaultValue: 12.0),
                ),
              ),
            ),
            child: Text(
              retryText,
              style: IslamicTypography.luxuryButton(
                isDark: isDarkMode,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// تحويل رسالة الخطأ إلى رسالة مفهومة للمستخدم
  String getReadableErrorMessage(dynamic error) {
    if (error == null) {
      return 'حدث خطأ غير معروف';
    }

    if (error is String) {
      return error;
    }

    // تحويل أخطاء قاعدة البيانات إلى رسائل مفهومة
    if (error.toString().contains('DatabaseException')) {
      return 'حدث خطأ في قاعدة البيانات. يرجى إعادة تشغيل التطبيق.';
    }

    // تحويل أخطاء الشبكة إلى رسائل مفهومة
    if (error.toString().contains('SocketException') ||
        error.toString().contains('NetworkException')) {
      return 'حدث خطأ في الاتصال بالشبكة. يرجى التحقق من اتصالك بالإنترنت.';
    }

    // تحويل أخطاء التحميل إلى رسائل مفهومة
    if (error.toString().contains('FileSystemException')) {
      return 'حدث خطأ في تحميل الملفات. يرجى التحقق من وجود مساحة كافية على الجهاز.';
    }

    // رسالة افتراضية
    return 'حدث خطأ: ${error.toString()}';
  }
}
