import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../data/models/poem.dart';
import '../../data/models/category.dart';
import '../../data/models/poet.dart';
import '../providers/manhal_rawi_provider.dart';
import '../screens/luxury_poem_details_screen.dart';
import '../../../../utils/theme/manhal_colors.dart';
import '../../../../utils/theme/islamic_typography.dart';
import '../../../../utils/animations/islamic_animations.dart';

/// بطاقة عرض القصيدة بتصميم فاخر
class LuxuryPoemCard extends StatefulWidget {
  final Poem poem;
  final Category? category;
  final bool showAnimation;
  final Poet poet;

  const LuxuryPoemCard({
    super.key,
    required this.poem,
    required this.poet,
    this.category,
    this.showAnimation = true,
  });

  @override
  State<LuxuryPoemCard> createState() => _LuxuryPoemCardState();
}

class _LuxuryPoemCardState extends State<LuxuryPoemCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.03).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onHover(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
    });

    if (isHovered) {
      _controller.forward();
    } else {
      _controller.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<ManhalRawiProvider>();
    final isDarkMode = provider.isDarkMode;

    // تحديد حجم الشاشة لتكييف العناصر
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isLargeScreen = screenSize.width > 600;

    // الحصول على البيت الأول حسب نوع القصيدة
    final bool isMukhammas = widget.poem.isMukhammas;
    final firstVerse = isMukhammas
        ? null
        : (widget.poem.verses.isNotEmpty ? widget.poem.verses.first : null);
    final firstMukhammasVerse = isMukhammas &&
            widget.poem.mukhammasVerses != null &&
            widget.poem.mukhammasVerses!.isNotEmpty
        ? widget.poem.mukhammasVerses!.first
        : null;

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: child,
        );
      },
      child: GestureDetector(
        onTap: () {
          Navigator.push(
            context,
            PageRouteBuilder(
              pageBuilder: (context, animation, secondaryAnimation) =>
                  LuxuryPoemDetailsScreen(poem: widget.poem, poet: widget.poet),
              transitionsBuilder:
                  (context, animation, secondaryAnimation, child) {
                return FadeTransition(
                  opacity: animation,
                  child: child,
                );
              },
              transitionDuration: const Duration(milliseconds: 500),
            ),
          );
        },
        child: MouseRegion(
          onEnter: (_) => _onHover(true),
          onExit: (_) => _onHover(false),
          child: Container(
            margin: EdgeInsets.symmetric(
                horizontal:
                    isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0),
                vertical: isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0)),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topRight,
                end: Alignment.bottomLeft,
                colors: isDarkMode
                    ? [
                        ManhalColors.blue800,
                        ManhalColors.blue900,
                      ]
                    : [
                        Colors.white,
                        ManhalColors.gold100.withValues(alpha: 0.3),
                      ],
              ),
              borderRadius: BorderRadius.circular(
                  isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0)),
              border: Border.all(
                color: _isHovered
                    ? (isDarkMode ? ManhalColors.gold500 : ManhalColors.primary)
                    : (isDarkMode
                        ? ManhalColors.blue700
                        : ManhalColors.gold200),
                width: _isHovered
                    ? (isSmallScreen ? 1.5 : (isLargeScreen ? 2.5 : 2.0))
                    : (isSmallScreen ? 0.5 : (isLargeScreen ? 1.5 : 1.0)),
              ),
              boxShadow: [
                BoxShadow(
                  color: _isHovered
                      ? (isDarkMode
                          ? ManhalColors.gold500.withValues(alpha: 0.3)
                          : ManhalColors.primary.withValues(alpha: 0.2))
                      : Colors.black.withValues(alpha: 0.05),
                  blurRadius: _isHovered
                      ? (isSmallScreen ? 10.0 : (isLargeScreen ? 20.0 : 15.0))
                      : (isSmallScreen ? 3.0 : (isLargeScreen ? 7.0 : 5.0)),
                  spreadRadius: _isHovered
                      ? (isSmallScreen ? 0.5 : (isLargeScreen ? 1.5 : 1.0))
                      : 0,
                  offset: Offset(
                      0, isSmallScreen ? 2.0 : (isLargeScreen ? 4.0 : 3.0)),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // رأس البطاقة مع عنوان القصيدة والتصنيف
                Container(
                  padding: EdgeInsets.all(
                      isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0)),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topRight,
                      end: Alignment.bottomLeft,
                      colors: isDarkMode
                          ? [
                              ManhalColors.blue700,
                              ManhalColors.blue800,
                            ]
                          : [
                              ManhalColors.gold200.withValues(alpha: 0.7),
                              ManhalColors.gold100.withValues(alpha: 0.3),
                            ],
                    ),
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(
                          isSmallScreen ? 11.0 : (isLargeScreen ? 19.0 : 15.0)),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // عنوان القصيدة
                      widget.showAnimation
                          ? IslamicAnimations.fadeSlideIn(
                              child: Text(
                                widget.poem.title,
                                style: IslamicTypography.luxuryPoemTitle(
                                  isDark: isDarkMode,
                                  fontSize: isSmallScreen
                                      ? 18.0
                                      : (isLargeScreen ? 26.0 : 22.0),
                                ),
                                textAlign: TextAlign.center,
                              ),
                              isDark: isDarkMode,
                              durationMs: 600,
                            )
                          : Text(
                              widget.poem.title,
                              style: IslamicTypography.luxuryPoemTitle(
                                isDark: isDarkMode,
                                fontSize: isSmallScreen
                                    ? 18.0
                                    : (isLargeScreen ? 26.0 : 22.0),
                              ),
                              textAlign: TextAlign.center,
                            ),

                      if (widget.category != null) ...[
                        SizedBox(
                            height: isSmallScreen
                                ? 6.0
                                : (isLargeScreen ? 10.0 : 8.0)),

                        // تصنيف القصيدة
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: isSmallScreen
                                ? 8.0
                                : (isLargeScreen ? 16.0 : 12.0),
                            vertical: isSmallScreen
                                ? 3.0
                                : (isLargeScreen ? 5.0 : 4.0),
                          ),
                          decoration: BoxDecoration(
                            color: isDarkMode
                                ? ManhalColors.blue900.withValues(alpha: 0.5)
                                : ManhalColors.gold100.withValues(alpha: 0.5),
                            borderRadius: BorderRadius.circular(isSmallScreen
                                ? 8.0
                                : (isLargeScreen ? 16.0 : 12.0)),
                            border: Border.all(
                              color: isDarkMode
                                  ? ManhalColors.gold600.withValues(alpha: 0.3)
                                  : ManhalColors.gold300,
                              width: isSmallScreen
                                  ? 0.5
                                  : (isLargeScreen ? 1.5 : 1.0),
                            ),
                          ),
                          child: Text(
                            widget.category!.name,
                            style: IslamicTypography.luxuryCategory(
                              isDark: isDarkMode,
                              fontSize: isSmallScreen
                                  ? 12.0
                                  : (isLargeScreen ? 16.0 : 14.0),
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),

                // محتوى البطاقة مع البيت الأول
                Padding(
                  padding: EdgeInsets.all(
                      isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0)),
                  child: Column(
                    children: [
                      if (isMukhammas && firstMukhammasVerse != null)
                        // عرض البيت المخمس
                        Column(
                          children: [
                            // السطر الأول من البيت المخمس
                            Text(
                              firstMukhammasVerse.firstLine1,
                              style: IslamicTypography.luxuryVerse(
                                isDark: isDarkMode,
                                fontSize: isSmallScreen
                                    ? 16.0
                                    : (isLargeScreen ? 20.0 : 18.0),
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),

                            // الفاصل بين الأسطر
                            Padding(
                              padding: EdgeInsets.symmetric(
                                  vertical: isSmallScreen
                                      ? 3.0
                                      : (isLargeScreen ? 5.0 : 4.0)),
                              child: Container(
                                height: isSmallScreen
                                    ? 0.5
                                    : (isLargeScreen ? 1.5 : 1.0),
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      Colors.transparent,
                                      isDarkMode
                                          ? ManhalColors.gold500
                                              .withValues(alpha: 0.7)
                                          : ManhalColors.primary
                                              .withValues(alpha: 0.7),
                                      Colors.transparent,
                                    ],
                                    stops: const [0.0, 0.5, 1.0],
                                  ),
                                ),
                              ),
                            ),

                            // إشارة إلى أنها قصيدة مخمسة
                            Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: isSmallScreen
                                      ? 8.0
                                      : (isLargeScreen ? 16.0 : 12.0),
                                  vertical: isSmallScreen
                                      ? 3.0
                                      : (isLargeScreen ? 5.0 : 4.0)),
                              decoration: BoxDecoration(
                                color: isDarkMode
                                    ? ManhalColors.gold500
                                        .withValues(alpha: 0.1)
                                    : ManhalColors.gold100
                                        .withValues(alpha: 0.3),
                                borderRadius: BorderRadius.circular(
                                    isSmallScreen
                                        ? 8.0
                                        : (isLargeScreen ? 16.0 : 12.0)),
                                border: Border.all(
                                  color: isDarkMode
                                      ? ManhalColors.gold500
                                          .withValues(alpha: 0.3)
                                      : ManhalColors.gold300,
                                  width: isSmallScreen
                                      ? 0.5
                                      : (isLargeScreen ? 1.5 : 1.0),
                                ),
                              ),
                              child: Text(
                                'قصيدة مخمسة',
                                style: IslamicTypography.luxuryCategory(
                                  isDark: isDarkMode,
                                  fontSize: isSmallScreen
                                      ? 10.0
                                      : (isLargeScreen ? 14.0 : 12.0),
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ],
                        )
                      else if (firstVerse != null)
                        // عرض البيت العادي
                        Column(
                          children: [
                            // الشطر الأول
                            Text(
                              firstVerse.first,
                              style: IslamicTypography.luxuryVerse(
                                isDark: isDarkMode,
                                fontSize: isSmallScreen
                                    ? 16.0
                                    : (isLargeScreen ? 20.0 : 18.0),
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),

                            // الفاصل بين الشطرين
                            Padding(
                              padding: EdgeInsets.symmetric(
                                  vertical: isSmallScreen
                                      ? 6.0
                                      : (isLargeScreen ? 10.0 : 8.0)),
                              child: Container(
                                height: isSmallScreen
                                    ? 0.5
                                    : (isLargeScreen ? 1.5 : 1.0),
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      Colors.transparent,
                                      isDarkMode
                                          ? ManhalColors.gold500
                                              .withValues(alpha: 0.7)
                                          : ManhalColors.primary
                                              .withValues(alpha: 0.7),
                                      Colors.transparent,
                                    ],
                                    stops: const [0.0, 0.5, 1.0],
                                  ),
                                ),
                              ),
                            ),

                            // الشطر الثاني
                            Text(
                              firstVerse.second,
                              style: IslamicTypography.luxuryVerse(
                                isDark: isDarkMode,
                                fontSize: isSmallScreen
                                    ? 16.0
                                    : (isLargeScreen ? 20.0 : 18.0),
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        )
                      else
                        // في حالة عدم وجود أبيات
                        Text(
                          'لا توجد أبيات',
                          style: IslamicTypography.luxuryVerse(
                            isDark: isDarkMode,
                            fontSize: isSmallScreen
                                ? 16.0
                                : (isLargeScreen ? 20.0 : 18.0),
                          ),
                          textAlign: TextAlign.center,
                        ),
                    ],
                  ),
                ),

                // تذييل البطاقة مع معلومات إضافية
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal:
                        isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0),
                    vertical:
                        isSmallScreen ? 8.0 : (isLargeScreen ? 16.0 : 12.0),
                  ),
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? ManhalColors.blue900.withValues(alpha: 0.5)
                        : ManhalColors.gold100.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.vertical(
                      bottom: Radius.circular(
                          isSmallScreen ? 11.0 : (isLargeScreen ? 19.0 : 15.0)),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // عدد الأبيات
                      Row(
                        children: [
                          Icon(
                            Icons.format_list_numbered,
                            size: isSmallScreen
                                ? 14.0
                                : (isLargeScreen ? 18.0 : 16.0),
                            color: isDarkMode
                                ? ManhalColors.gold300
                                : ManhalColors.gold600,
                          ),
                          SizedBox(
                              width: isSmallScreen
                                  ? 2.0
                                  : (isLargeScreen ? 6.0 : 4.0)),
                          Text(
                            // عرض عدد الأبيات حسب نوع القصيدة
                            widget.poem.isMukhammas
                                ? (widget.poem.mukhammasVerses != null &&
                                        widget.poem.mukhammasVerses!.isNotEmpty
                                    ? '${widget.poem.mukhammasVerses!.length} بيت مخمس'
                                    : '0 بيت مخمس')
                                : '${widget.poem.verses.length} بيت',
                            style: IslamicTypography.luxuryCaption(
                              isDark: isDarkMode,
                              fontSize: isSmallScreen
                                  ? 12.0
                                  : (isLargeScreen ? 16.0 : 14.0),
                            ),
                          ),
                        ],
                      ),

                      // البحر الشعري
                      Row(
                        children: [
                          Icon(
                            Icons.music_note,
                            size: isSmallScreen
                                ? 14.0
                                : (isLargeScreen ? 18.0 : 16.0),
                            color: isDarkMode
                                ? ManhalColors.gold300
                                : ManhalColors.gold600,
                          ),
                          SizedBox(
                              width: isSmallScreen
                                  ? 2.0
                                  : (isLargeScreen ? 6.0 : 4.0)),
                          Text(
                            widget.poem.meter,
                            style: IslamicTypography.luxuryCaption(
                              isDark: isDarkMode,
                              fontSize: isSmallScreen
                                  ? 12.0
                                  : (isLargeScreen ? 16.0 : 14.0),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
