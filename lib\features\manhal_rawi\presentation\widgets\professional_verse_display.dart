import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../data/models/verse.dart';
import '../providers/manhal_rawi_provider.dart';
import '../../../../utils/theme/manhal_colors.dart';
import '../../../../utils/theme/islamic_typography.dart';
import '../../../../utils/theme/islamic_patterns.dart';
import '../../../../utils/animations/islamic_animations.dart';
import '../../../../utils/helpers/responsive_helper.dart';

/// مكون عرض الأبيات الشعرية بشكل احترافي متقابل كما في الكتب التراثية
class ProfessionalVerseDisplay extends StatefulWidget {
  final List<Verse> verses;
  final int selectedIndex;
  final Function(int)? onVerseSelected;
  final bool showAnimation;
  final bool enableSelection;

  /// دالة يتم استدعاؤها عند تبديل وضع ملء الشاشة
  final Function()? onFullScreenToggle;

  /// حالة وضع ملء الشاشة
  final bool isFullScreen;

  const ProfessionalVerseDisplay({
    super.key,
    required this.verses,
    this.selectedIndex = 0,
    this.onVerseSelected,
    this.showAnimation = true,
    this.enableSelection = true,
    this.onFullScreenToggle,
    this.isFullScreen = false,
  });

  @override
  State<ProfessionalVerseDisplay> createState() =>
      _ProfessionalVerseDisplayState();
}

class _ProfessionalVerseDisplayState extends State<ProfessionalVerseDisplay> {
  late PageController _pageController;
  late int _currentIndex;
  late ScrollController _scrollController;

  // متغيرات لحفظ إعدادات العرض
  late double _fontSize;
  late int _versesPerPage;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.selectedIndex;

    // الحصول على إعدادات العرض من المزود
    final provider = Provider.of<ManhalRawiProvider>(context, listen: false);
    _fontSize = provider.verseDisplaySettings.fontSize;
    _versesPerPage = provider.verseDisplaySettings.versesPerPage;

    // تهيئة متحكمات الصفحة والتمرير
    _pageController =
        PageController(initialPage: _currentIndex ~/ _versesPerPage);
    _scrollController = ScrollController();
  }

  @override
  void didUpdateWidget(ProfessionalVerseDisplay oldWidget) {
    super.didUpdateWidget(oldWidget);

    // تحديث المؤشر الحالي إذا تغير
    if (widget.selectedIndex != _currentIndex) {
      _currentIndex = widget.selectedIndex;

      // تحديث صفحة العرض
      if (_pageController.hasClients) {
        _pageController.animateToPage(
          _currentIndex ~/ _versesPerPage,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      }
    }

    // تحديث إعدادات العرض من المزود
    final provider = Provider.of<ManhalRawiProvider>(context, listen: false);
    final newFontSize = provider.verseDisplaySettings.fontSize;
    final newVersesPerPage = provider.verseDisplaySettings.versesPerPage;

    // إذا تغيرت الإعدادات، نقوم بتحديثها وإعادة بناء الواجهة
    if (_fontSize != newFontSize || _versesPerPage != newVersesPerPage) {
      setState(() {
        _fontSize = newFontSize;
        _versesPerPage = newVersesPerPage;
      });
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  // عرض حوار تغيير حجم الخط
  void _showFontSizeDialog(BuildContext context, ManhalRawiProvider provider) {
    final isDarkMode = provider.isDarkMode;
    double currentFontSize = _fontSize;

    // تحديد ما إذا كانت الشاشة صغيرة أو كبيرة
    final isSmallScreen = ResponsiveHelper.isSmallScreen(context);
    final isLargeScreen = ResponsiveHelper.isLargeScreen(context) ||
        ResponsiveHelper.isExtraLargeScreen(context);

    // تعديل الأحجام بناءً على حجم الشاشة
    final double titleFontSize = ResponsiveHelper.getResponsiveFontSize(
      context,
      fontSize: isSmallScreen ? 18.0 : (isLargeScreen ? 22.0 : 20.0),
    );

    final double buttonFontSize = ResponsiveHelper.getResponsiveFontSize(
      context,
      fontSize: isSmallScreen ? 14.0 : (isLargeScreen ? 18.0 : 16.0),
    );

    final double sliderWidth =
        isSmallScreen ? 200.0 : (isLargeScreen ? 300.0 : 250.0);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: isDarkMode ? ManhalColors.blue900 : Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
            ResponsiveHelper.getResponsiveBorderRadius(
              context,
              defaultValue:
                  isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0),
            ),
          ),
          side: BorderSide(
            color: isDarkMode
                ? ManhalColors.gold500.withValues(alpha: 0.5)
                : ManhalColors.gold300,
            width: ResponsiveHelper.getResponsiveBorderWidth(
              context,
              defaultValue: isSmallScreen ? 0.5 : (isLargeScreen ? 1.5 : 1.0),
            ),
          ),
        ),
        title: Text(
          'تغيير حجم الخط',
          style: TextStyle(
            color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
            fontSize: titleFontSize,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        content: SizedBox(
          width: sliderWidth,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // عرض حجم الخط الحالي
              Text(
                'حجم الخط: ${currentFontSize.toStringAsFixed(1)}',
                style: TextStyle(
                  color: isDarkMode ? Colors.white : Colors.black,
                  fontSize: buttonFontSize,
                ),
              ),

              SizedBox(height: 20),

              // شريط تمرير لتغيير حجم الخط
              SliderTheme(
                data: SliderThemeData(
                  activeTrackColor:
                      isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
                  inactiveTrackColor: isDarkMode
                      ? ManhalColors.gold500.withValues(alpha: 0.3)
                      : ManhalColors.primary.withValues(alpha: 0.3),
                  thumbColor:
                      isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
                  overlayColor: isDarkMode
                      ? ManhalColors.gold500.withValues(alpha: 0.2)
                      : ManhalColors.primary.withValues(alpha: 0.2),
                ),
                child: Slider(
                  min: 12.0,
                  max: 28.0,
                  value: currentFontSize,
                  divisions: 16,
                  onChanged: (value) {
                    currentFontSize = value;
                    // إعادة بناء الحوار لعرض القيمة الجديدة
                    (context as Element).markNeedsBuild();
                  },
                ),
              ),

              SizedBox(height: 20),

              // أزرار الإجراءات
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // زر الإلغاء
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    style: TextButton.styleFrom(
                      foregroundColor:
                          isDarkMode ? Colors.white70 : Colors.black54,
                    ),
                    child: Text(
                      'إلغاء',
                      style: TextStyle(fontSize: buttonFontSize),
                    ),
                  ),

                  // زر التطبيق
                  ElevatedButton(
                    onPressed: () {
                      // تحديث حجم الخط في المزود
                      provider.updateFontSize(currentFontSize);

                      // تحديث حجم الخط في الحالة المحلية
                      setState(() {
                        _fontSize = currentFontSize;
                      });

                      Navigator.of(context).pop();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: isDarkMode
                          ? ManhalColors.gold500
                          : ManhalColors.primary,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(
                          ResponsiveHelper.getResponsiveBorderRadius(
                            context,
                            defaultValue: isSmallScreen
                                ? 8.0
                                : (isLargeScreen ? 16.0 : 12.0),
                          ),
                        ),
                      ),
                    ),
                    child: Text(
                      'تطبيق',
                      style: TextStyle(fontSize: buttonFontSize),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<ManhalRawiProvider>();
    final isDarkMode = provider.isDarkMode;
    final screenSize = MediaQuery.of(context).size;

    // تحديد ما إذا كانت الشاشة صغيرة أو كبيرة باستخدام ResponsiveHelper
    final isSmallScreen = ResponsiveHelper.isSmallScreen(context);
    final isLargeScreen = ResponsiveHelper.isLargeScreen(context) ||
        ResponsiveHelper.isExtraLargeScreen(context);

    // تحديث حجم الخط ونوع الخط من المزود
    _fontSize = provider.verseDisplaySettings.fontSize;
    // نحصل على نوع الخط من المزود ليتم استخدامه في عرض الأبيات

    // تعديل عدد الأبيات في كل صفحة بناءً على حجم الشاشة وحالة ملء الشاشة
    int versesPerPage = _versesPerPage;

    if (widget.isFullScreen) {
      // زيادة عدد الأبيات في وضع ملء الشاشة
      if (isLargeScreen) {
        versesPerPage = versesPerPage + 2;
      } else if (!isSmallScreen) {
        versesPerPage = versesPerPage + 1;
      }
    } else {
      // تعديل عدد الأبيات في الوضع العادي
      if (isSmallScreen) {
        // تقليل عدد الأبيات للشاشات الصغيرة
        versesPerPage = versesPerPage > 2 ? versesPerPage - 1 : versesPerPage;
      } else if (isLargeScreen) {
        // زيادة عدد الأبيات للشاشات الكبيرة
        versesPerPage = versesPerPage + 1;
      }
    }

    final int totalPages = (widget.verses.length / versesPerPage).ceil();

    // بناء شريط الأدوات بتصميم فاخر
    final toolbar = Container(
      padding: ResponsiveHelper.getResponsivePadding(
        context,
        defaultValue: isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0),
      ),
      decoration: BoxDecoration(
        color: isDarkMode
            ? ManhalColors.blue900.withValues(alpha: 0.9)
            : ManhalColors.gold100.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(
          ResponsiveHelper.getResponsiveBorderRadius(
            context,
            defaultValue: isSmallScreen ? 16.0 : (isLargeScreen ? 24.0 : 20.0),
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.grey.withValues(alpha: 0.2),
            blurRadius: isSmallScreen ? 4.0 : (isLargeScreen ? 7.0 : 5.0),
            offset:
                Offset(0, isSmallScreen ? 1.0 : (isLargeScreen ? 3.0 : 2.0)),
          ),
        ],
        border: Border.all(
          color: isDarkMode
              ? ManhalColors.gold500.withValues(alpha: 0.4)
              : ManhalColors.gold300,
          width: ResponsiveHelper.getResponsiveBorderWidth(
            context,
            defaultValue: isSmallScreen ? 0.5 : (isLargeScreen ? 1.5 : 1.0),
          ),
        ),
        image: DecorationImage(
          image: AssetImage(
            isDarkMode
                ? 'assets/images/islamic_pattern_dark.png'
                : 'assets/images/islamic_pattern_light.png',
          ),
          opacity: 0.05,
          fit: BoxFit.cover,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        textDirection:
            TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
        children: [
          // زر تغيير حجم الخط (على اليمين في اللغة العربية)
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 2),
            decoration: BoxDecoration(
              color: isDarkMode
                  ? ManhalColors.gold500.withValues(alpha: 0.1)
                  : ManhalColors.primary.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(
                ResponsiveHelper.getResponsiveBorderRadius(
                  context,
                  defaultValue:
                      isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0),
                ),
              ),
            ),
            child: IconButton(
              icon: Icon(
                Icons.text_fields,
                color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
                size: ResponsiveHelper.getResponsiveIconSize(
                  context,
                  defaultSize:
                      isSmallScreen ? 18.0 : (isLargeScreen ? 24.0 : 20.0),
                ),
              ),
              onPressed: () {
                // تأثير اهتزاز عند النقر
                HapticFeedback.mediumImpact();
                _showFontSizeDialog(context, provider);
              },
              tooltip: 'تغيير حجم الخط',
              padding: EdgeInsets.all(
                ResponsiveHelper.getResponsiveSpacing(
                  context,
                  defaultValue:
                      isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0),
                ),
              ),
              constraints: BoxConstraints(
                minWidth: isSmallScreen ? 32.0 : (isLargeScreen ? 44.0 : 36.0),
                minHeight: isSmallScreen ? 32.0 : (isLargeScreen ? 44.0 : 36.0),
              ),
              splashRadius:
                  isSmallScreen ? 24.0 : (isLargeScreen ? 32.0 : 28.0),
              splashColor: isDarkMode
                  ? ManhalColors.gold500.withValues(alpha: 0.3)
                  : ManhalColors.primary.withValues(alpha: 0.2),
            ),
          ),

          // زر ملء الشاشة (على اليسار في اللغة العربية)
          if (widget.onFullScreenToggle != null)
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 2),
              decoration: BoxDecoration(
                color: isDarkMode
                    ? ManhalColors.gold500.withValues(alpha: 0.1)
                    : ManhalColors.primary.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(
                  ResponsiveHelper.getResponsiveBorderRadius(
                    context,
                    defaultValue:
                        isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0),
                  ),
                ),
              ),
              child: IconButton(
                icon: Icon(
                  widget.isFullScreen
                      ? Icons.fullscreen_exit
                      : Icons.fullscreen,
                  color:
                      isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
                  size: ResponsiveHelper.getResponsiveIconSize(
                    context,
                    defaultSize:
                        isSmallScreen ? 18.0 : (isLargeScreen ? 24.0 : 20.0),
                  ),
                ),
                onPressed: () {
                  // تأثير اهتزاز عند النقر
                  HapticFeedback.mediumImpact();
                  widget.onFullScreenToggle?.call();
                },
                tooltip:
                    widget.isFullScreen ? 'إلغاء ملء الشاشة' : 'ملء الشاشة',
                padding: EdgeInsets.all(
                  ResponsiveHelper.getResponsiveSpacing(
                    context,
                    defaultValue:
                        isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0),
                  ),
                ),
                constraints: BoxConstraints(
                  minWidth:
                      isSmallScreen ? 32.0 : (isLargeScreen ? 44.0 : 36.0),
                  minHeight:
                      isSmallScreen ? 32.0 : (isLargeScreen ? 44.0 : 36.0),
                ),
                splashRadius:
                    isSmallScreen ? 24.0 : (isLargeScreen ? 32.0 : 28.0),
                splashColor: isDarkMode
                    ? ManhalColors.gold500.withValues(alpha: 0.3)
                    : ManhalColors.primary.withValues(alpha: 0.2),
              ),
            ),
        ],
      ),
    );

    // بناء أزرار التحكم في وضع ملء الشاشة بتصميم فاخر
    final fullScreenControls = Positioned(
      top: ResponsiveHelper.getResponsiveSpacing(
        context,
        defaultValue: isSmallScreen ? 12.0 : (isLargeScreen ? 24.0 : 16.0),
      ),
      right: ResponsiveHelper.getResponsiveSpacing(
        context,
        defaultValue: isSmallScreen ? 12.0 : (isLargeScreen ? 24.0 : 16.0),
      ),
      child: Container(
        padding: EdgeInsets.all(
          ResponsiveHelper.getResponsiveSpacing(
            context,
            defaultValue: isSmallScreen ? 4.0 : (isLargeScreen ? 8.0 : 6.0),
          ),
        ),
        decoration: BoxDecoration(
          color: isDarkMode
              ? ManhalColors.blue900.withValues(alpha: 0.9)
              : Colors.white.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(
            ResponsiveHelper.getResponsiveBorderRadius(
              context,
              defaultValue:
                  isSmallScreen ? 16.0 : (isLargeScreen ? 24.0 : 20.0),
            ),
          ),
          boxShadow: [
            BoxShadow(
              color: isDarkMode
                  ? Colors.black.withValues(alpha: 0.3)
                  : Colors.grey.withValues(alpha: 0.2),
              blurRadius: isSmallScreen ? 4.0 : (isLargeScreen ? 8.0 : 6.0),
              spreadRadius: isSmallScreen ? 0.0 : (isLargeScreen ? 1.0 : 0.5),
              offset:
                  Offset(0, isSmallScreen ? 1.0 : (isLargeScreen ? 3.0 : 2.0)),
            ),
          ],
          border: Border.all(
            color: isDarkMode
                ? ManhalColors.gold500.withValues(alpha: 0.4)
                : ManhalColors.gold300,
            width: ResponsiveHelper.getResponsiveBorderWidth(
              context,
              defaultValue: isSmallScreen ? 0.5 : (isLargeScreen ? 1.5 : 1.0),
            ),
          ),
          image: DecorationImage(
            image: AssetImage(
              isDarkMode
                  ? 'assets/images/islamic_pattern_dark.png'
                  : 'assets/images/islamic_pattern_light.png',
            ),
            opacity: 0.05,
            fit: BoxFit.cover,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          textDirection:
              TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
          children: [
            // زر تغيير حجم الخط (على اليمين في اللغة العربية)
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 2),
              decoration: BoxDecoration(
                color: isDarkMode
                    ? ManhalColors.gold500.withValues(alpha: 0.1)
                    : ManhalColors.primary.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(
                  ResponsiveHelper.getResponsiveBorderRadius(
                    context,
                    defaultValue:
                        isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0),
                  ),
                ),
              ),
              child: IconButton(
                icon: Icon(
                  Icons.text_fields,
                  color:
                      isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
                  size: ResponsiveHelper.getResponsiveIconSize(
                    context,
                    defaultSize:
                        isSmallScreen ? 20.0 : (isLargeScreen ? 28.0 : 24.0),
                  ),
                ),
                onPressed: () {
                  // تأثير اهتزاز عند النقر
                  HapticFeedback.mediumImpact();
                  _showFontSizeDialog(context, provider);
                },
                tooltip: 'تغيير حجم الخط',
                padding: EdgeInsets.all(
                  ResponsiveHelper.getResponsiveSpacing(
                    context,
                    defaultValue:
                        isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0),
                  ),
                ),
                constraints: BoxConstraints(
                  minWidth:
                      isSmallScreen ? 36.0 : (isLargeScreen ? 48.0 : 40.0),
                  minHeight:
                      isSmallScreen ? 36.0 : (isLargeScreen ? 48.0 : 40.0),
                ),
                splashRadius:
                    isSmallScreen ? 24.0 : (isLargeScreen ? 32.0 : 28.0),
                splashColor: isDarkMode
                    ? ManhalColors.gold500.withValues(alpha: 0.3)
                    : ManhalColors.primary.withValues(alpha: 0.2),
              ),
            ),

            // زر إغلاق وضع ملء الشاشة (على اليسار في اللغة العربية)
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 2),
              decoration: BoxDecoration(
                color: isDarkMode
                    ? ManhalColors.gold500.withValues(alpha: 0.1)
                    : ManhalColors.primary.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(
                  ResponsiveHelper.getResponsiveBorderRadius(
                    context,
                    defaultValue:
                        isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0),
                  ),
                ),
              ),
              child: IconButton(
                icon: Icon(
                  Icons.fullscreen_exit,
                  color:
                      isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
                  size: ResponsiveHelper.getResponsiveIconSize(
                    context,
                    defaultSize:
                        isSmallScreen ? 20.0 : (isLargeScreen ? 28.0 : 24.0),
                  ),
                ),
                onPressed: () {
                  // تأثير اهتزاز عند النقر
                  HapticFeedback.mediumImpact();
                  widget.onFullScreenToggle?.call();
                },
                tooltip: 'إغلاق وضع ملء الشاشة',
                padding: EdgeInsets.all(
                  ResponsiveHelper.getResponsiveSpacing(
                    context,
                    defaultValue:
                        isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0),
                  ),
                ),
                constraints: BoxConstraints(
                  minWidth:
                      isSmallScreen ? 36.0 : (isLargeScreen ? 48.0 : 40.0),
                  minHeight:
                      isSmallScreen ? 36.0 : (isLargeScreen ? 48.0 : 40.0),
                ),
                splashRadius:
                    isSmallScreen ? 24.0 : (isLargeScreen ? 32.0 : 28.0),
                splashColor: isDarkMode
                    ? ManhalColors.gold500.withValues(alpha: 0.3)
                    : ManhalColors.primary.withValues(alpha: 0.2),
              ),
            ),
          ],
        ),
      ),
    );

    // بناء محتوى العرض الاحترافي
    Widget mainContent = SingleChildScrollView(
      controller: _scrollController,
      child: Column(
        children: [
          // عنوان الصفحة مع رقم الصفحة
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              textDirection:
                  TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
              children: [
                // الجانب الأيمن (في اللغة العربية)
                Row(
                  textDirection: TextDirection
                      .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 4),
                      decoration: BoxDecoration(
                        color: isDarkMode
                            ? ManhalColors.blue900.withValues(alpha: 0.7)
                            : ManhalColors.gold100.withValues(alpha: 0.7),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: isDarkMode
                              ? ManhalColors.gold600.withValues(alpha: 0.3)
                              : ManhalColors.gold300,
                          width: 1,
                        ),
                      ),
                      child: Text(
                        'البيت ${_currentIndex + 1} من ${widget.verses.length}',
                        style: IslamicTypography.luxuryCategory(
                          isDark: isDarkMode,
                          fontSize: 14,
                        ),
                        textDirection: TextDirection
                            .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                      ),
                    ),

                    if (!widget.isFullScreen) const SizedBox(width: 8),

                    // إظهار شريط الأدوات فقط في الوضع العادي
                    if (!widget.isFullScreen) toolbar,
                  ],
                ),

                // الجانب الأيسر (في اللغة العربية)
                Text(
                  'صفحة ${(_pageController.hasClients ? _pageController.page?.toInt() : 0) ?? 0 + 1} من $totalPages',
                  style: IslamicTypography.luxuryCaption(
                    isDark: isDarkMode,
                    fontSize: 14,
                  ),
                  textDirection: TextDirection
                      .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                ),
              ],
            ),
          ),

          // عرض الأبيات في صفحات بتصميم فاخر
          Container(
            height: widget.isFullScreen
                ? screenSize.height *
                    (isSmallScreen ? 0.8 : (isLargeScreen ? 0.9 : 0.85))
                : isSmallScreen
                    ? 350.0
                    : (isLargeScreen ? 450.0 : 400.0),
            margin: EdgeInsets.symmetric(
              horizontal: ResponsiveHelper.getResponsiveSpacing(
                context,
                defaultValue:
                    isSmallScreen ? 12.0 : (isLargeScreen ? 24.0 : 16.0),
              ),
            ),
            decoration: BoxDecoration(
              color: isDarkMode
                  ? ManhalColors.blue900.withValues(alpha: 0.7)
                  : Colors.white.withValues(alpha: 0.95),
              borderRadius: BorderRadius.circular(
                ResponsiveHelper.getResponsiveBorderRadius(
                  context,
                  defaultValue:
                      isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0),
                ),
              ),
              border: Border.all(
                color: isDarkMode
                    ? ManhalColors.gold500.withValues(alpha: 0.4)
                    : ManhalColors.gold300,
                width: ResponsiveHelper.getResponsiveBorderWidth(
                  context,
                  defaultValue:
                      isSmallScreen ? 1.0 : (isLargeScreen ? 2.0 : 1.5),
                ),
              ),
              boxShadow: [
                BoxShadow(
                  color: isDarkMode
                      ? Colors.black.withValues(alpha: 0.3)
                      : Colors.black.withValues(alpha: 0.15),
                  blurRadius:
                      isSmallScreen ? 8.0 : (isLargeScreen ? 15.0 : 10.0),
                  spreadRadius:
                      isSmallScreen ? 0.5 : (isLargeScreen ? 2.0 : 1.0),
                  offset: Offset(
                      0, isSmallScreen ? 2.0 : (isLargeScreen ? 5.0 : 3.0)),
                ),
              ],
              image: DecorationImage(
                image: AssetImage(
                  isDarkMode
                      ? 'assets/images/paper_texture_dark.png'
                      : 'assets/images/paper_texture_light.png',
                ),
                fit: BoxFit.cover,
                opacity: isDarkMode ? 0.08 : 0.12,
              ),
            ),
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: (pageIndex) {
                setState(() {
                  _currentIndex = pageIndex * versesPerPage;
                });
                if (widget.onVerseSelected != null) {
                  widget.onVerseSelected!(_currentIndex);
                }
              },
              itemCount: totalPages,
              itemBuilder: (context, pageIndex) {
                // حساب نطاق الأبيات في هذه الصفحة
                final startIndex = pageIndex * versesPerPage;
                final endIndex =
                    (startIndex + versesPerPage) < widget.verses.length
                        ? startIndex + versesPerPage
                        : widget.verses.length;

                return _buildVersePage(
                  verses: widget.verses.sublist(startIndex, endIndex),
                  pageIndex: pageIndex,
                  startIndex: startIndex,
                  isDarkMode: isDarkMode,
                );
              },
            ),
          ),

          const SizedBox(height: 8), // تم تقليل المسافة

          // مؤشرات الصفحات
          if (totalPages > 1) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              textDirection:
                  TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
              children: List.generate(
                totalPages,
                (index) => _buildPageIndicator(index, isDarkMode),
              ),
            ),
          ],
        ],
      ),
    );

    // إذا كان في وضع ملء الشاشة، نعرض المحتوى في شاشة كاملة
    if (widget.isFullScreen) {
      return AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        height: screenSize.height,
        width: screenSize.width,
        color: isDarkMode ? ManhalColors.blue900 : Colors.white,
        child: Stack(
          children: [
            // عارض الأبيات
            mainContent,

            // أزرار التحكم
            fullScreenControls,
          ],
        ),
      );
    }

    // وإلا نعرض المحتوى العادي
    return mainContent;
  }

  Widget _buildVersePage({
    required List<Verse> verses,
    required int pageIndex,
    required int startIndex,
    required bool isDarkMode,
  }) {
    return Stack(
      children: [
        // زخارف الصفحة
        Positioned(
          top: 10,
          left: 10,
          child: CustomPaint(
            painter: IslamicPatterns.getFloralPattern(
              isDark: isDarkMode,
              opacity: 0.2,
              complexity: 1,
            ),
            size: const Size(40, 40),
          ),
        ),
        Positioned(
          top: 10,
          right: 10,
          child: CustomPaint(
            painter: IslamicPatterns.getFloralPattern(
              isDark: isDarkMode,
              opacity: 0.2,
              complexity: 1,
            ),
            size: const Size(40, 40),
          ),
        ),

        // محتوى الصفحة
        Padding(
          padding:
              const EdgeInsets.fromLTRB(12, 16, 12, 12), // تم تقليل المسافات
          child: Column(
            children: [
              // عنوان الصفحة
              Container(
                width: double.infinity,
                padding:
                    const EdgeInsets.symmetric(vertical: 4), // تم تقليل المسافة
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: isDarkMode
                          ? ManhalColors.gold500.withValues(alpha: 0.3)
                          : ManhalColors.gold300,
                      width: 1,
                    ),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  textDirection: TextDirection
                      .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                  children: [
                    // زخرفة إسلامية
                    if (widget.isFullScreen)
                      CustomPaint(
                        painter: IslamicPatterns.getFloralPattern(
                          isDark: isDarkMode,
                          opacity: 0.3,
                          complexity: 1,
                        ),
                        size: const Size(24, 24),
                      ),

                    if (widget.isFullScreen) const SizedBox(width: 8),

                    Text(
                      'أبيات القصيدة',
                      style: IslamicTypography.luxurySubtitle(
                        isDark: isDarkMode,
                        fontSize: widget.isFullScreen ? 22 : 18,
                      ),
                      textAlign: TextAlign.center,
                      textDirection: TextDirection
                          .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                    ),

                    if (widget.isFullScreen) const SizedBox(width: 8),

                    // زخرفة إسلامية
                    if (widget.isFullScreen)
                      CustomPaint(
                        painter: IslamicPatterns.getFloralPattern(
                          isDark: isDarkMode,
                          opacity: 0.3,
                          complexity: 1,
                        ),
                        size: const Size(24, 24),
                      ),
                  ],
                ),
              ),

              const SizedBox(height: 8), // تم تقليل المسافة

              // الأبيات
              Expanded(
                child: ListView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.symmetric(
                      vertical: 4), // تم إضافة مسافة صغيرة
                  itemCount: verses.length,
                  itemBuilder: (context, index) {
                    final verseIndex = startIndex + index;
                    final verse = verses[index];
                    final isSelected = verseIndex == _currentIndex;

                    return _buildProfessionalVerse(
                      verse: verse,
                      verseIndex: verseIndex,
                      isSelected: isSelected,
                      isDarkMode: isDarkMode,
                    );
                  },
                ),
              ),
            ],
          ),
        ),

        // ترقيم الصفحة
        Positioned(
          bottom: 8,
          left: 0,
          right: 0,
          child: Center(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
              decoration: BoxDecoration(
                color: isDarkMode
                    ? ManhalColors.blue800.withValues(alpha: 0.7)
                    : ManhalColors.gold100.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isDarkMode
                      ? ManhalColors.gold500.withValues(alpha: 0.2)
                      : ManhalColors.gold300,
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: isDarkMode
                        ? Colors.black.withValues(alpha: 0.2)
                        : Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Text(
                '${pageIndex + 1}',
                style: IslamicTypography.luxuryCaption(
                  isDark: isDarkMode,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
                textDirection:
                    TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProfessionalVerse({
    required Verse verse,
    required int verseIndex,
    required bool isSelected,
    required bool isDarkMode,
  }) {
    // Obtener el proveedor para acceder al tipo de fuente
    final provider = Provider.of<ManhalRawiProvider>(context, listen: false);
    // تحديد حجم الشاشة لتكييف العناصر
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isLargeScreen = screenSize.width > 600;

    // استخدام حجم الخط من المزود مع تعديله بناءً على حجم الشاشة ووضع ملء الشاشة
    final double baseFontSize = _fontSize; // استخدام حجم الخط من المزود

    // تعديل حجم الخط بناءً على وضع ملء الشاشة
    final double adjustedFontSize = widget.isFullScreen
        ? baseFontSize * 1.1 // زيادة حجم الخط في وضع ملء الشاشة
        : baseFontSize;

    // تعديل حجم الخط بناءً على حجم الشاشة
    final double fontSize = isSmallScreen
        ? adjustedFontSize * 0.9 // تقليل حجم الخط للشاشات الصغيرة
        : (isLargeScreen
            ? adjustedFontSize * 1.1 // زيادة حجم الخط للشاشات الكبيرة
            : adjustedFontSize);

    // تم تعليق المتغيرات التالية لأنها غير مستخدمة بعد تعليق رقم البيت
    /*
    final double circleSize = isSmallScreen
        ? (widget.isFullScreen ? 30.0 : 26.0)
        : (isLargeScreen
            ? (widget.isFullScreen ? 42.0 : 36.0)
            : (widget.isFullScreen ? 36.0 : 30.0));

    final double circleFontSize = isSmallScreen
        ? (widget.isFullScreen ? 12.0 : 10.0)
        : (isLargeScreen
            ? (widget.isFullScreen ? 16.0 : 14.0)
            : (widget.isFullScreen ? 14.0 : 12.0));

    // تقليل المسافات العمودية لجعل الأبيات أكثر تقاربًا
    final double verticalSpacing = isSmallScreen
        ? (widget.isFullScreen ? 6.0 : 4.0) // تم تقليل المسافة
        : (isLargeScreen
            ? (widget.isFullScreen ? 10.0 : 8.0) // تم تقليل المسافة
            : (widget.isFullScreen ? 8.0 : 6.0)); // تم تقليل المسافة
    */

    final double containerPadding = isSmallScreen
        ? (widget.isFullScreen ? 6.0 : 4.0) // تم تقليل المسافة
        : (isLargeScreen
            ? (widget.isFullScreen ? 10.0 : 8.0) // تم تقليل المسافة
            : (widget.isFullScreen ? 8.0 : 6.0)); // تم تقليل المسافة

    final double versePadding =
        isSmallScreen ? 4.0 : (isLargeScreen ? 6.0 : 5.0); // تم تقليل المسافة

    // تعديل المسافة بين الأبيات لتحسين المظهر بعد إزالة رقم البيت
    final double bottomMargin = isSmallScreen
        ? 10.0
        : (isLargeScreen ? 14.0 : 12.0); // تم زيادة المسافة قليلاً

    final double borderRadius =
        isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0);

    final double borderWidth =
        isSmallScreen ? 0.5 : (isLargeScreen ? 1.5 : 1.0);

    return GestureDetector(
      // منع انتشار الحدث للعناصر الأب
      behavior: HitTestBehavior.opaque,
      onTap: widget.enableSelection
          ? () {
              setState(() {
                _currentIndex = verseIndex;
              });
              if (widget.onVerseSelected != null) {
                widget.onVerseSelected!(verseIndex);
              }
              // لا نقوم بأي عملية نسخ هنا، فقط تحديد البيت
            }
          : null,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        margin: EdgeInsets.only(bottom: bottomMargin),
        // تحسين مظهر الأبيات بعد إزالة رقم البيت
        decoration: BoxDecoration(
          color: isSelected
              ? (isDarkMode
                  ? ManhalColors.blue800.withValues(alpha: 0.3)
                  : ManhalColors.gold100.withValues(alpha: 0.3))
              : Colors.transparent,
          borderRadius: BorderRadius.circular(borderRadius),
          border: Border.all(
            color: isSelected
                ? (isDarkMode ? ManhalColors.gold500 : ManhalColors.primary)
                : (isDarkMode
                    ? ManhalColors.gold500.withValues(alpha: 0.1)
                    : ManhalColors.gold300.withValues(alpha: 0.1)),
            width: isSelected ? borderWidth : borderWidth * 0.5,
          ),
          boxShadow: [
            BoxShadow(
              color: isDarkMode
                  ? (isSelected
                      ? ManhalColors.gold500.withValues(alpha: 0.15)
                      : Colors.black.withValues(alpha: 0.05))
                  : (isSelected
                      ? ManhalColors.primary.withValues(alpha: 0.15)
                      : Colors.black.withValues(alpha: 0.03)),
              blurRadius: isSmallScreen ? 4.0 : (isLargeScreen ? 8.0 : 6.0),
              spreadRadius: isSmallScreen ? 0.0 : (isLargeScreen ? 1.0 : 0.5),
              offset: Offset(0, 1),
            ),
          ],
        ),
        child: Padding(
          padding: EdgeInsets.all(containerPadding),
          child: Column(
            children: [
              // تم تعليق رقم البيت لجعل العرض أكثر احترافية وتناسقًا
              /*
              Container(
                width: circleSize,
                height: circleSize,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: isDarkMode
                      ? ManhalColors.blue800.withValues(alpha: 0.7)
                      : ManhalColors.gold100.withValues(alpha: 0.7),
                  border: Border.all(
                    color: isDarkMode
                        ? ManhalColors.gold500.withValues(alpha: 0.3)
                        : ManhalColors.gold300,
                    width: borderWidth,
                  ),
                  boxShadow: widget.isFullScreen
                      ? [
                          BoxShadow(
                            color: isDarkMode
                                ? Colors.black.withValues(alpha: 0.2)
                                : Colors.black.withValues(alpha: 0.1),
                            blurRadius: isSmallScreen
                                ? 3.0
                                : (isLargeScreen ? 5.0 : 4.0),
                            spreadRadius: isSmallScreen
                                ? 0.0
                                : (isLargeScreen ? 1.0 : 0.0),
                          ),
                        ]
                      : null,
                ),
                child: Center(
                  child: Text(
                    '${verseIndex + 1}',
                    style: IslamicTypography.luxuryCaption(
                      isDark: isDarkMode,
                      fontSize: circleFontSize,
                      fontWeight: FontWeight.bold,
                    ),
                    textDirection: TextDirection
                        .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                  ),
                ),
              ),

              SizedBox(height: 8.0), // استخدام قيمة ثابتة بدلاً من المتغير المعلق
              */

              // الشطرين متقابلين
              Row(
                textDirection:
                    TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                children: [
                  // الشطر الأول (يمين)
                  Expanded(
                    child: Container(
                      padding: EdgeInsets.all(versePadding),
                      decoration: BoxDecoration(
                        border: Border(
                          left: BorderSide(
                            color: isDarkMode
                                ? ManhalColors.gold500.withValues(alpha: 0.2)
                                : ManhalColors.gold300.withValues(alpha: 0.5),
                            width: borderWidth,
                          ),
                        ),
                      ),
                      child: widget.showAnimation && isSelected
                          ? IslamicAnimations.arabicTextTyping(
                              text: verse.first,
                              style: IslamicTypography.luxuryVerse(
                                isDark: isDarkMode,
                                fontSize: fontSize,
                                fontWeight: isSelected
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                                fontFamily: provider.fontFamily,
                              ),
                              durationMs: 800,
                              textAlign: TextAlign.center,
                            )
                          : Text(
                              verse.first,
                              style: IslamicTypography.luxuryVerse(
                                isDark: isDarkMode,
                                fontSize: fontSize,
                                fontWeight: isSelected
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                                fontFamily: provider.fontFamily,
                              ),
                              textAlign: TextAlign.center,
                            ),
                    ),
                  ),

                  // الشطر الثاني (يسار)
                  Expanded(
                    child: Container(
                      padding: EdgeInsets.all(versePadding),
                      decoration: BoxDecoration(
                        border: Border(
                          right: BorderSide(
                            color: isDarkMode
                                ? ManhalColors.gold500.withValues(alpha: 0.2)
                                : ManhalColors.gold300.withValues(alpha: 0.5),
                            width: borderWidth,
                          ),
                        ),
                      ),
                      child: widget.showAnimation && isSelected
                          ? IslamicAnimations.arabicTextTyping(
                              text: verse.second,
                              style: IslamicTypography.luxuryVerse(
                                isDark: isDarkMode,
                                fontSize: fontSize,
                                fontWeight: isSelected
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                                fontFamily: provider.fontFamily,
                              ),
                              durationMs: 800,
                              delayMs: 400,
                              textAlign: TextAlign.center,
                            )
                          : Text(
                              verse.second,
                              style: IslamicTypography.luxuryVerse(
                                isDark: isDarkMode,
                                fontSize: fontSize,
                                fontWeight: isSelected
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                                fontFamily: provider.fontFamily,
                              ),
                              textAlign: TextAlign.center,
                            ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPageIndicator(int index, bool isDarkMode) {
    final isActive = index ==
        (_pageController.hasClients ? _pageController.page?.toInt() : 0);

    // تحديد ما إذا كانت الشاشة صغيرة أو كبيرة باستخدام ResponsiveHelper
    final isSmallScreen = ResponsiveHelper.isSmallScreen(context);
    final isLargeScreen = ResponsiveHelper.isLargeScreen(context) ||
        ResponsiveHelper.isExtraLargeScreen(context);

    // تعديل الأحجام بناءً على حجم الشاشة ووضع ملء الشاشة
    final double indicatorHeight = ResponsiveHelper.getResponsiveSpacing(
      context,
      defaultValue: widget.isFullScreen
          ? (isSmallScreen ? 8.0 : (isLargeScreen ? 12.0 : 10.0))
          : (isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0)),
    );

    final double activeWidth = ResponsiveHelper.getResponsiveSpacing(
      context,
      defaultValue: widget.isFullScreen
          ? (isSmallScreen ? 24.0 : (isLargeScreen ? 36.0 : 30.0))
          : (isSmallScreen ? 20.0 : (isLargeScreen ? 30.0 : 24.0)),
    );

    final double inactiveWidth = ResponsiveHelper.getResponsiveSpacing(
      context,
      defaultValue: widget.isFullScreen
          ? (isSmallScreen ? 8.0 : (isLargeScreen ? 12.0 : 10.0))
          : (isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0)),
    );

    final double horizontalMargin = ResponsiveHelper.getResponsiveSpacing(
      context,
      defaultValue: isSmallScreen ? 2.0 : (isLargeScreen ? 6.0 : 4.0),
    );

    final double borderRadius = ResponsiveHelper.getResponsiveBorderRadius(
      context,
      defaultValue: widget.isFullScreen
          ? (isSmallScreen ? 4.0 : (isLargeScreen ? 6.0 : 5.0))
          : (isSmallScreen ? 3.0 : (isLargeScreen ? 5.0 : 4.0)),
    );

    // تعديل قيم الظل بناءً على حجم الشاشة ووضع ملء الشاشة
    final double blurRadius = isSmallScreen
        ? (widget.isFullScreen ? 4.0 : 3.0)
        : (isLargeScreen
            ? (widget.isFullScreen ? 8.0 : 6.0)
            : (widget.isFullScreen ? 6.0 : 4.0));

    final double spreadRadius = isSmallScreen
        ? (widget.isFullScreen ? 1.0 : 0.5)
        : (isLargeScreen
            ? (widget.isFullScreen ? 3.0 : 2.0)
            : (widget.isFullScreen ? 2.0 : 1.0));

    return GestureDetector(
      onTap: () {
        // تأثير اهتزاز عند النقر
        HapticFeedback.selectionClick();

        // الانتقال إلى الصفحة المحددة بتأثير متحرك
        _pageController.animateToPage(
          index,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeOutCubic,
        );
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        margin: EdgeInsets.symmetric(horizontal: horizontalMargin),
        height: indicatorHeight,
        width: isActive ? activeWidth : inactiveWidth,
        decoration: BoxDecoration(
          color: isActive
              ? (isDarkMode ? ManhalColors.gold500 : ManhalColors.primary)
              : (isDarkMode ? ManhalColors.blue700 : ManhalColors.gold200),
          borderRadius: BorderRadius.circular(borderRadius),
          boxShadow: isActive
              ? [
                  BoxShadow(
                    color: isDarkMode
                        ? ManhalColors.gold500.withValues(alpha: 0.3)
                        : ManhalColors.primary.withValues(alpha: 0.3),
                    blurRadius: blurRadius,
                    spreadRadius: spreadRadius,
                  ),
                ]
              : null,
          border: Border.all(
            color: isActive
                ? (isDarkMode
                    ? ManhalColors.gold500.withValues(alpha: 0.8)
                    : ManhalColors.primary.withValues(alpha: 0.8))
                : Colors.transparent,
            width: 0.5,
          ),
        ),
        child: isActive
            ? Center(
                child: Icon(
                  Icons.circle,
                  size: indicatorHeight * 0.5,
                  color: isDarkMode
                      ? Colors.black.withValues(alpha: 0.3)
                      : Colors.white.withValues(alpha: 0.7),
                ),
              )
            : null,
      ),
    );
  }
}
