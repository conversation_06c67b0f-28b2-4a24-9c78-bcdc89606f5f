import 'package:flutter_test/flutter_test.dart';
import 'package:manhal_rawi/utils/helpers/arabic_text_helper.dart';

void main() {
  group('ArabicTextHelper', () {
    test('removeDiacritics should remove all diacritics from Arabic text', () {
      // Arrange
      const String textWithDiacritics = 'مُحَمَّدٌ هَزَّاعُ بَاعَلَوِي الحَضْرَمِي';
      const String expectedText = 'محمد هزاع باعلوي الحضرمي';

      // Act
      final result = ArabicTextHelper.removeDiacritics(textWithDiacritics);

      // Assert
      expect(result, equals(expectedText));
    });

    test('normalizeArabicText should normalize Arabic text', () {
      // Arrange
      const String textWithVariations = 'أَحْمَد إِبْرَاهِيم آدَم';
      const String expectedText = 'احمد ابراهيم ادم';

      // Act
      final result = ArabicTextHelper.normalizeArabicText(textWithVariations);

      // Assert
      expect(result, equals(expectedText));
    });

    test('compareTextsIgnoringDiacritics should return true for matching texts', () {
      // Arrange
      const String text1 = 'مُحَمَّدٌ';
      const String text2 = 'محمد';

      // Act
      final result = ArabicTextHelper.compareTextsIgnoringDiacritics(text1, text2);

      // Assert
      expect(result, isTrue);
    });

    test('compareTextsIgnoringDiacritics should return false for non-matching texts', () {
      // Arrange
      const String text1 = 'مُحَمَّدٌ';
      const String text2 = 'أحمد';

      // Act
      final result = ArabicTextHelper.compareTextsIgnoringDiacritics(text1, text2);

      // Assert
      expect(result, isFalse);
    });

    test('containsTextIgnoringDiacritics should return true when text contains query', () {
      // Arrange
      const String source = 'قَصِيدَةُ المَنْهَلِ الرَّاوِي';
      const String query = 'المنهل';

      // Act
      final result = ArabicTextHelper.containsTextIgnoringDiacritics(source, query);

      // Assert
      expect(result, isTrue);
    });

    test('containsTextIgnoringDiacritics should return false when text does not contain query', () {
      // Arrange
      const String source = 'قَصِيدَةُ المَنْهَلِ الرَّاوِي';
      const String query = 'البحر';

      // Act
      final result = ArabicTextHelper.containsTextIgnoringDiacritics(source, query);

      // Assert
      expect(result, isFalse);
    });

    test('normalizeArabicText should handle different forms of alef', () {
      // Arrange
      const String text = 'أإآٱا';
      const String expected = 'ااااا';

      // Act
      final result = ArabicTextHelper.normalizeArabicText(text);

      // Assert
      expect(result, equals(expected));
    });

    test('normalizeArabicText should handle different forms of yaa', () {
      // Arrange
      const String text = 'يىئ';
      const String expected = 'ييي';

      // Act
      final result = ArabicTextHelper.normalizeArabicText(text);

      // Assert
      expect(result, equals(expected));
    });

    test('normalizeArabicText should handle taa marbouta', () {
      // Arrange
      const String text = 'قصيدة';
      const String expected = 'قصيده';

      // Act
      final result = ArabicTextHelper.normalizeArabicText(text);

      // Assert
      expect(result, equals(expected));
    });

    test('normalizeArabicText should handle hamza on waw', () {
      // Arrange
      const String text = 'مؤمن';
      const String expected = 'مومن';

      // Act
      final result = ArabicTextHelper.normalizeArabicText(text);

      // Assert
      expect(result, equals(expected));
    });
  });
}
