import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../data/models/poem.dart';
import '../../data/models/poet.dart';
import '../providers/manhal_rawi_provider.dart';
import '../../../../utils/theme/manhal_colors.dart';
import '../../../../utils/theme/typography.dart';
import '../../../../utils/theme/decorations.dart';

class PoemCard extends StatelessWidget {
  final Poem poem;
  final Poet poet;
  final VoidCallback onTap;

  const PoemCard({
    Key? key,
    required this.poem,
    required this.poet,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = context.watch<ManhalRawiProvider>().isDarkMode;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: ManhalDecorations.cardDecoration(isDark: isDarkMode),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // عنوان القصيدة
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: isDarkMode ? ManhalColors.blue800 : ManhalColors.gold100,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
                border: Border(
                  bottom: BorderSide(
                    color: isDarkMode
                        ? ManhalColors.gold600.withValues(alpha: 0.3)
                        : ManhalColors.gold300,
                    width: 1,
                  ),
                ),
              ),
              child: Text(
                poem.title,
                style: ManhalTypography.headingSmall.copyWith(
                  color: isDarkMode
                      ? ManhalColors.textLight
                      : ManhalColors.textDark,
                ),
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
              ),
            ),

            // محتوى البطاقة
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // اسم الشاعر
                  Row(
                    children: [
                      Icon(
                        Icons.person,
                        size: 18,
                        color: isDarkMode
                            ? ManhalColors.gold500
                            : ManhalColors.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        poet.name,
                        style: ManhalTypography.poetName.copyWith(
                          color: isDarkMode
                              ? ManhalColors.gold500
                              : ManhalColors.primary,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  // مقتطف من القصيدة
                  Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 12, vertical: 10),
                    decoration:
                        ManhalDecorations.verseDecoration(isDark: isDarkMode),
                    child: Text(
                      // عرض مقتطف مناسب حسب نوع القصيدة
                      poem.isMukhammas
                          ? (poem.mukhammasVerses != null &&
                                  poem.mukhammasVerses!.isNotEmpty
                              ? poem.mukhammasVerses!.first.firstLine1
                              : 'قصيدة مخمسة')
                          : (poem.verses.isNotEmpty
                              ? poem.verses.first.first
                              : 'قصيدة'),
                      style: ManhalTypography.verseText.copyWith(
                        color: isDarkMode
                            ? ManhalColors.textLight
                            : ManhalColors.textDark,
                        fontSize: 18,
                      ),
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),

                  const SizedBox(height: 12),

                  // معلومات إضافية
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // البحر الشعري
                      Row(
                        children: [
                          Icon(
                            Icons.music_note,
                            size: 16,
                            color: isDarkMode
                                ? ManhalColors.textMuted
                                : ManhalColors.textMuted,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            poem.meter,
                            style: ManhalTypography.bodySmall.copyWith(
                              color: isDarkMode
                                  ? ManhalColors.textMuted
                                  : ManhalColors.textMuted,
                            ),
                          ),
                        ],
                      ),

                      // عدد الأبيات
                      Row(
                        children: [
                          Icon(
                            Icons.format_list_numbered,
                            size: 16,
                            color: isDarkMode
                                ? ManhalColors.textMuted
                                : ManhalColors.textMuted,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            // عرض عدد الأبيات حسب نوع القصيدة
                            poem.isMukhammas
                                ? (poem.mukhammasVerses != null &&
                                        poem.mukhammasVerses!.isNotEmpty
                                    ? '${poem.mukhammasVerses!.length} بيت مخمس'
                                    : '0 بيت مخمس')
                                : '${poem.verses.length} بيت',
                            style: ManhalTypography.bodySmall.copyWith(
                              color: isDarkMode
                                  ? ManhalColors.textMuted
                                  : ManhalColors.textMuted,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
