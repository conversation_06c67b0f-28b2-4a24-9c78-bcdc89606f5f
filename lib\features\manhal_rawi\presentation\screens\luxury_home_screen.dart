import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../data/models/poem.dart';
import '../../data/models/poet.dart';
import '../../data/models/category.dart';
import '../../data/models/book_info.dart';
import '../providers/manhal_rawi_provider.dart';
import '../widgets/luxury_poem_card.dart';
import '../widgets/luxury_author_card.dart';
import '../widgets/animated_islamic_background.dart';
import '../widgets/alphabetical_index.dart';
import '../widgets/error_display.dart';
import '../widgets/luxury_welcome_message.dart';
import 'luxury_poem_details_screen.dart';
import 'luxury_author_details_screen.dart';
import 'search_screen.dart';
import 'favorites_screen.dart';
import 'settings_screen.dart';
import '../../../../utils/theme/manhal_colors.dart';
import '../../../../utils/theme/islamic_typography.dart';
import '../../../../utils/animations/islamic_animations.dart';
import '../../../../utils/theme/islamic_patterns.dart';

/// الشاشة الرئيسية الفاخرة للتطبيق
class LuxuryHomeScreen extends StatefulWidget {
  const LuxuryHomeScreen({super.key});

  @override
  State<LuxuryHomeScreen> createState() => _LuxuryHomeScreenState();
}

class _LuxuryHomeScreenState extends State<LuxuryHomeScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _headerAnimation;
  late Animation<double> _contentAnimation;
  // تم إزالة _selectedCategoryIndex لأننا نستخدم selectedCategoryId من ManhalRawiProvider

  // متغير للتحكم في ظهور رسالة الترحيب
  bool _showWelcomeMessage = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _headerAnimation = CurvedAnimation(
      parent: _controller,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    );

    _contentAnimation = CurvedAnimation(
      parent: _controller,
      curve: const Interval(0.3, 1.0, curve: Curves.easeOut),
    );

    // تحميل البيانات
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      try {
        final provider = context.read<ManhalRawiProvider>();
        await provider.loadInitialData();

        // تحديث حالة المفضلة لجميع القصائد
        await provider.updatePoemsWithFavoriteStatus();

        // التحقق من حالة رسالة الترحيب
        setState(() {
          _showWelcomeMessage = !provider.hasSeenWelcomeMessage;
        });
      } catch (e) {
        // تجاهل الأخطاء هنا، سيتم التعامل معها في provider
      }
    });

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<ManhalRawiProvider>();
    final isDarkMode = provider.isDarkMode;
    final status = provider.status;
    final bookInfo = provider.bookInfo;
    final categories = provider.categories;
    final poems = provider.filteredPoems;
    final poets = provider.poets;

    return Directionality(
      textDirection: TextDirection.rtl, // تأكيد اتجاه RTL للشاشة بأكملها
      child: Scaffold(
        body: Stack(
          children: [
            // المحتوى الرئيسي
            AnimatedIslamicBackground(
              child: SafeArea(
                child: Column(
                  children: [
                    _buildAppBar(context),
                    Expanded(
                      child: RefreshIndicator(
                        onRefresh: () => provider.loadInitialData(),
                        color: isDarkMode
                            ? ManhalColors.gold500
                            : ManhalColors.primary,
                        backgroundColor:
                            isDarkMode ? ManhalColors.blue800 : Colors.white,
                        child: status == ManhalRawiStatus.loading
                            ? _buildLoadingState(isDarkMode)
                            : status == ManhalRawiStatus.error
                                ? _buildErrorState(
                                    provider.errorMessage, isDarkMode)
                                : CustomScrollView(
                                    slivers: [
                                      SliverToBoxAdapter(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            // معلومات الكتاب
                                            if (bookInfo != null)
                                              _buildBookInfoSection(
                                                  bookInfo, isDarkMode),

                                            // المؤلف
                                            if (poets.isNotEmpty)
                                              _buildAuthorSection(
                                                  poets.first, isDarkMode),

                                            // التصنيفات
                                            if (categories.isNotEmpty)
                                              _buildCategoriesSection(
                                                  categories, isDarkMode),

                                            // الفهرس الهجائي
                                            _buildAlphabeticalIndex(isDarkMode),

                                            // عنوان قسم القصائد
                                            _buildPoemsSectionHeader(
                                                poems.length, isDarkMode),
                                          ],
                                        ),
                                      ),

                                      // قائمة القصائد
                                      _buildPoemsList(
                                          poems,
                                          poets.isNotEmpty ? poets.first : null,
                                          categories,
                                          isDarkMode),

                                      // مساحة إضافية في النهاية
                                      const SliverToBoxAdapter(
                                        child: SizedBox(height: 32),
                                      ),
                                    ],
                                  ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // رسالة الترحيب
            if (_showWelcomeMessage)
              LuxuryWelcomeMessage(
                onDismiss: () {
                  setState(() {
                    _showWelcomeMessage = false;
                  });
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppBar(BuildContext context) {
    final provider = context.watch<ManhalRawiProvider>();
    final isDarkMode = provider.isDarkMode;

    return AnimatedBuilder(
      animation: _headerAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _headerAnimation.value,
          child: child,
        );
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.transparent,
          boxShadow: [
            BoxShadow(
              color: isDarkMode
                  ? Colors.black.withValues(alpha: 0.1)
                  : Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: LayoutBuilder(
          builder: (context, constraints) {
            // تحديد ما إذا كانت الشاشة صغيرة
            final isSmallScreen = constraints.maxWidth < 360;

            // تعديل حجم الأيقونات بناءً على حجم الشاشة
            final iconSize = isSmallScreen ? 18.0 : 20.0;
            final iconPadding = isSmallScreen ? 6.0 : 8.0;

            return Row(
              children: [
                // زر البحث
                Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isDarkMode
                        ? ManhalColors.blue800.withValues(alpha: 0.7)
                        : Colors.white.withValues(alpha: 0.7),
                    border: Border.all(
                      color: isDarkMode
                          ? ManhalColors.gold500.withValues(alpha: 0.3)
                          : ManhalColors.gold300,
                      width: 1,
                    ),
                  ),
                  child: IconButton(
                    icon: Icon(
                      Icons.search,
                      color: isDarkMode
                          ? ManhalColors.gold500
                          : ManhalColors.primary,
                      size: iconSize,
                    ),
                    padding: EdgeInsets.all(iconPadding),
                    constraints: BoxConstraints(
                      minWidth: isSmallScreen ? 32.0 : 40.0,
                      minHeight: isSmallScreen ? 32.0 : 40.0,
                    ),
                    onPressed: () {
                      // إعادة تعيين نتائج البحث قبل الانتقال إلى شاشة البحث
                      final provider = context.read<ManhalRawiProvider>();
                      provider.clearFilters();

                      // تخزين مرجع للـ provider
                      final manhalProvider = provider;

                      Navigator.push(
                        context,
                        PageRouteBuilder(
                          pageBuilder:
                              (context, animation, secondaryAnimation) =>
                                  const SearchScreen(),
                          transitionsBuilder:
                              (context, animation, secondaryAnimation, child) {
                            return FadeTransition(
                              opacity: animation,
                              child: child,
                            );
                          },
                          transitionDuration: const Duration(milliseconds: 500),
                        ),
                      ).then((_) {
                        // إعادة تعيين نتائج البحث بعد العودة من شاشة البحث
                        if (mounted) {
                          manhalProvider.clearFilters();
                        }
                      });
                    },
                  ),
                ),

                // المساحة المرنة قبل العنوان
                const Spacer(flex: 1),

                // عنوان التطبيق
                Column(
                  children: [
                    IslamicAnimations.arabicTextTyping(
                      text: 'المنهل الروي',
                      style: IslamicTypography.luxuryBookTitle(
                        isDark: isDarkMode,
                      ),
                      durationMs: 1500,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'محمد هزاع باعلوي الحضرمي',
                      style: IslamicTypography.luxuryCaption(
                        isDark: isDarkMode,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),

                // المساحة المرنة بعد العنوان
                const Spacer(flex: 1),

                // زر المفضلة
                Container(
                  margin: EdgeInsets.only(
                      right: isSmallScreen
                          ? 4.0
                          : 8.0), // تغيير من left إلى right للتوافق مع اتجاه RTL
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isDarkMode
                        ? ManhalColors.blue800.withValues(alpha: 0.7)
                        : Colors.white.withValues(alpha: 0.7),
                    border: Border.all(
                      color: isDarkMode
                          ? ManhalColors.gold500.withValues(alpha: 0.3)
                          : ManhalColors.gold300,
                      width: 1,
                    ),
                  ),
                  child: IconButton(
                    icon: Icon(
                      Icons.favorite,
                      color: Colors.red,
                      size: iconSize,
                    ),
                    padding: EdgeInsets.all(iconPadding),
                    constraints: BoxConstraints(
                      minWidth: isSmallScreen ? 32.0 : 40.0,
                      minHeight: isSmallScreen ? 32.0 : 40.0,
                    ),
                    onPressed: () {
                      Navigator.push(
                        context,
                        PageRouteBuilder(
                          pageBuilder:
                              (context, animation, secondaryAnimation) =>
                                  const FavoritesScreen(),
                          transitionsBuilder:
                              (context, animation, secondaryAnimation, child) {
                            return FadeTransition(
                              opacity: animation,
                              child: child,
                            );
                          },
                          transitionDuration: const Duration(milliseconds: 500),
                        ),
                      );
                    },
                  ),
                ),

                // زر الإعدادات
                Container(
                  margin: EdgeInsets.only(
                      right: isSmallScreen
                          ? 4.0
                          : 8.0), // تغيير من left إلى right للتوافق مع اتجاه RTL
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isDarkMode
                        ? ManhalColors.blue800.withValues(alpha: 0.7)
                        : Colors.white.withValues(alpha: 0.7),
                    border: Border.all(
                      color: isDarkMode
                          ? ManhalColors.gold500.withValues(alpha: 0.3)
                          : ManhalColors.gold300,
                      width: 1,
                    ),
                  ),
                  child: IconButton(
                    icon: Icon(
                      Icons.settings,
                      color: isDarkMode
                          ? ManhalColors.gold500
                          : ManhalColors.primary,
                      size: iconSize,
                    ),
                    padding: EdgeInsets.all(iconPadding),
                    constraints: BoxConstraints(
                      minWidth: isSmallScreen ? 32.0 : 40.0,
                      minHeight: isSmallScreen ? 32.0 : 40.0,
                    ),
                    onPressed: () {
                      Navigator.push(
                        context,
                        PageRouteBuilder(
                          pageBuilder:
                              (context, animation, secondaryAnimation) =>
                                  const SettingsScreen(),
                          transitionsBuilder:
                              (context, animation, secondaryAnimation, child) {
                            return FadeTransition(
                              opacity: animation,
                              child: child,
                            );
                          },
                          transitionDuration: const Duration(milliseconds: 500),
                        ),
                      );
                    },
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildLoadingState(bool isDarkMode) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // أيقونة التحميل
          IslamicAnimations.pulsatingEffect(
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isDarkMode
                    ? ManhalColors.blue800.withValues(alpha: 0.7)
                    : Colors.white.withValues(alpha: 0.7),
                border: Border.all(
                  color:
                      isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: isDarkMode
                        ? ManhalColors.gold500.withValues(alpha: 0.3)
                        : ManhalColors.primary.withValues(alpha: 0.3),
                    blurRadius: 10,
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: Icon(
                Icons.auto_stories,
                color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
                size: 40,
              ),
            ),
            isDark: isDarkMode,
          ),

          const SizedBox(height: 24),

          // نص التحميل
          Text(
            'جاري تحميل المنهل الروي...',
            style: IslamicTypography.luxurySubtitle(
              isDark: isDarkMode,
              fontSize: 20,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String errorMessage, bool isDarkMode) {
    return ErrorDisplay(
      message: 'حدث خطأ أثناء تحميل البيانات: $errorMessage',
      onRetry: () {
        context.read<ManhalRawiProvider>().loadInitialData();
      },
      retryText: 'إعادة المحاولة',
      icon: Icons.error_outline_rounded,
    );
  }

  Widget _buildBookInfoSection(BookInfo bookInfo, bool isDarkMode) {
    return AnimatedBuilder(
      animation: _contentAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _contentAnimation.value,
          child: Transform.translate(
            offset: Offset(0, 30 * (1 - _contentAnimation.value)),
            child: child,
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.fromLTRB(16, 16, 16, 24),
        decoration: IslamicPatterns.getDecorativeBorder(
          isDark: isDarkMode,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Column(
          children: [
            // صورة الغلاف (استخدام صورة احتياطية دائمًا لتجنب الأخطاء)
            ClipRRect(
              borderRadius:
                  const BorderRadius.vertical(top: Radius.circular(20)),
              child: Container(
                width: double.infinity,
                height: 180,
                color: isDarkMode ? ManhalColors.blue800 : ManhalColors.gold100,
                child: Center(
                  child: Icon(
                    Icons.book,
                    size: 64,
                    color: isDarkMode
                        ? ManhalColors.gold500
                        : ManhalColors.primary,
                  ),
                ),
              ),
            ),

            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // عنوان الكتاب
                  Text(
                    bookInfo.title,
                    style: IslamicTypography.luxuryBookTitle(
                      isDark: isDarkMode,
                      fontSize: 32,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 8),

                  // العنوان الفرعي
                  Text(
                    bookInfo.subtitle,
                    style: IslamicTypography.luxurySubtitle(
                      isDark: isDarkMode,
                      fontSize: 20,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 16),

                  // سنة النشر
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                    decoration: BoxDecoration(
                      color: isDarkMode
                          ? ManhalColors.blue800.withValues(alpha: 0.7)
                          : ManhalColors.gold200.withValues(alpha: 0.7),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: isDarkMode
                            ? ManhalColors.gold600.withValues(alpha: 0.3)
                            : ManhalColors.gold300,
                        width: 1,
                      ),
                    ),
                    child: Text(
                      "سنة النشر: ${bookInfo.publishYear}",
                      style: IslamicTypography.luxuryCategory(
                        isDark: isDarkMode,
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // وصف الكتاب
                  Text(
                    bookInfo.description,
                    style: IslamicTypography.luxuryBody(
                      isDark: isDarkMode,
                    ),
                    textAlign: TextAlign.justify,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAuthorSection(Poet poet, bool isDarkMode) {
    return AnimatedBuilder(
      animation: _contentAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _contentAnimation.value,
          child: Transform.translate(
            offset: Offset(0, 30 * (1 - _contentAnimation.value)),
            child: child,
          ),
        );
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
            child: Row(
              children: [
                // زخرفة إسلامية
                CustomPaint(
                  painter: IslamicPatterns.getFloralPattern(
                    isDark: isDarkMode,
                    opacity: 0.3,
                    complexity: 2,
                  ),
                  size: const Size(30, 30),
                ),

                const SizedBox(width: 8),

                // عنوان القسم
                Text(
                  'المؤلف',
                  style: IslamicTypography.luxurySubtitle(
                    isDark: isDarkMode,
                    fontSize: 22,
                  ),
                ),
              ],
            ),
          ),

          // بطاقة المؤلف
          Center(
            child: GestureDetector(
              onTap: () {
                Navigator.push(
                  context,
                  PageRouteBuilder(
                    pageBuilder: (context, animation, secondaryAnimation) =>
                        LuxuryAuthorDetailsScreen(poet: poet),
                    transitionsBuilder:
                        (context, animation, secondaryAnimation, child) {
                      return FadeTransition(
                        opacity: animation,
                        child: child,
                      );
                    },
                    transitionDuration: const Duration(milliseconds: 500),
                  ),
                );
              },
              child: LuxuryAuthorCard(
                poet: poet,
                showAnimation: true,
              ),
            ),
          ),

          const SizedBox(height: 24),
        ],
      ),
    );
  }

  // بناء الفهرس الهجائي
  Widget _buildAlphabeticalIndex(bool isDarkMode) {
    return AnimatedBuilder(
      animation: _contentAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _contentAnimation.value,
          child: Transform.translate(
            offset: Offset(0, 30 * (1 - _contentAnimation.value)),
            child: child,
          ),
        );
      },
      child: Padding(
        padding: const EdgeInsets.only(bottom: 16),
        child: AlphabeticalIndex(
          selectedLetter: context.watch<ManhalRawiProvider>().selectedLetter,
          onLetterSelected: (letter) {
            context.read<ManhalRawiProvider>().filterByFirstLetter(letter);
          },
          onClearFilter: () {
            context.read<ManhalRawiProvider>().clearFilters();
          },
        ),
      ),
    );
  }

  Widget _buildCategoriesSection(List<Category> categories, bool isDarkMode) {
    return AnimatedBuilder(
      animation: _contentAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _contentAnimation.value,
          child: Transform.translate(
            offset: Offset(0, 30 * (1 - _contentAnimation.value)),
            child: child,
          ),
        );
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
            child: Row(
              children: [
                // زخرفة إسلامية
                CustomPaint(
                  painter: IslamicPatterns.getFloralPattern(
                    isDark: isDarkMode,
                    opacity: 0.3,
                    complexity: 2,
                  ),
                  size: const Size(30, 30),
                ),

                const SizedBox(width: 8),

                // عنوان القسم
                Text(
                  'التصنيفات',
                  style: IslamicTypography.luxurySubtitle(
                    isDark: isDarkMode,
                    fontSize: 22,
                  ),
                ),

                const SizedBox(height: 8),

                // زر تصفية المفضلات
                GestureDetector(
                  onTap: () {
                    context.read<ManhalRawiProvider>().filterFavorites();
                  },
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: isDarkMode
                          ? ManhalColors.blue900.withValues(alpha: 0.7)
                          : ManhalColors.gold100.withValues(alpha: 0.7),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: isDarkMode
                            ? ManhalColors.gold600.withValues(alpha: 0.3)
                            : ManhalColors.gold300,
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.favorite,
                          color: Colors.red,
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'المفضلة',
                          style: IslamicTypography.luxuryCategory(
                            isDark: isDarkMode,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),

          // قائمة التصنيفات
          SizedBox(
            height: 50,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 8),
              // إضافة عنصر "الكل" في بداية القائمة
              itemCount: categories.length + 1,
              itemBuilder: (context, index) {
                // عنصر "الكل"
                if (index == 0) {
                  final isSelected =
                      context.watch<ManhalRawiProvider>().selectedCategoryId ==
                          null;

                  return GestureDetector(
                    onTap: () {
                      // إعادة تعيين التصفية
                      context.read<ManhalRawiProvider>().clearFilters();
                    },
                    child: Container(
                      margin: const EdgeInsets.symmetric(horizontal: 8),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topRight,
                          end: Alignment.bottomLeft,
                          colors: isSelected
                              ? (isDarkMode
                                  ? [
                                      ManhalColors.gold600
                                          .withValues(alpha: 0.3),
                                      ManhalColors.blue800,
                                    ]
                                  : [
                                      ManhalColors.primary
                                          .withValues(alpha: 0.2),
                                      Colors.white,
                                    ])
                              : (isDarkMode
                                  ? [
                                      ManhalColors.blue800,
                                      ManhalColors.blue900,
                                    ]
                                  : [
                                      Colors.white,
                                      ManhalColors.gold100
                                          .withValues(alpha: 0.3),
                                    ]),
                        ),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: isSelected
                              ? (isDarkMode
                                  ? ManhalColors.gold500
                                  : ManhalColors.primary)
                              : (isDarkMode
                                  ? ManhalColors.blue700
                                  : ManhalColors.gold200),
                          width: isSelected ? 2 : 1,
                        ),
                        boxShadow: isSelected
                            ? [
                                BoxShadow(
                                  color: isDarkMode
                                      ? ManhalColors.gold500
                                          .withValues(alpha: 0.3)
                                      : ManhalColors.primary
                                          .withValues(alpha: 0.2),
                                  blurRadius: 8,
                                  spreadRadius: 1,
                                  offset: const Offset(0, 2),
                                ),
                              ]
                            : null,
                      ),
                      child: Center(
                        child: Text(
                          'الكل',
                          style: IslamicTypography.luxuryCategory(
                            isDark: isDarkMode,
                            fontWeight: isSelected
                                ? FontWeight.bold
                                : FontWeight.normal,
                          ),
                        ),
                      ),
                    ),
                  );
                }

                // عناصر التصنيفات
                final category = categories[index - 1];
                final isSelected =
                    context.watch<ManhalRawiProvider>().selectedCategoryId ==
                        category.id;

                return GestureDetector(
                  onTap: () {
                    // إذا كان التصنيف محدداً بالفعل، نقوم بإعادة تعيين التصفية
                    if (isSelected) {
                      context.read<ManhalRawiProvider>().clearFilters();
                    } else {
                      // تحديد التصنيف
                      context
                          .read<ManhalRawiProvider>()
                          .filterByCategory(category.id);
                    }
                  },
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 8),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topRight,
                        end: Alignment.bottomLeft,
                        colors: isSelected
                            ? (isDarkMode
                                ? [
                                    ManhalColors.gold600.withValues(alpha: 0.3),
                                    ManhalColors.blue800,
                                  ]
                                : [
                                    ManhalColors.primary.withValues(alpha: 0.2),
                                    Colors.white,
                                  ])
                            : (isDarkMode
                                ? [
                                    ManhalColors.blue800,
                                    ManhalColors.blue900,
                                  ]
                                : [
                                    Colors.white,
                                    ManhalColors.gold100.withValues(alpha: 0.3),
                                  ]),
                      ),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: isSelected
                            ? (isDarkMode
                                ? ManhalColors.gold500
                                : ManhalColors.primary)
                            : (isDarkMode
                                ? ManhalColors.blue700
                                : ManhalColors.gold200),
                        width: isSelected ? 2 : 1,
                      ),
                      boxShadow: isSelected
                          ? [
                              BoxShadow(
                                color: isDarkMode
                                    ? ManhalColors.gold500
                                        .withValues(alpha: 0.3)
                                    : ManhalColors.primary
                                        .withValues(alpha: 0.2),
                                blurRadius: 8,
                                spreadRadius: 1,
                                offset: const Offset(0, 2),
                              ),
                            ]
                          : null,
                    ),
                    child: Center(
                      child: Text(
                        category.name,
                        style: IslamicTypography.luxuryCategory(
                          isDark: isDarkMode,
                          fontWeight:
                              isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),

          const SizedBox(height: 16),
        ],
      ),
    );
  }

  // عنوان قسم القصائد
  Widget _buildPoemsSectionHeader(int poemsCount, bool isDarkMode) {
    return AnimatedBuilder(
      animation: _contentAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _contentAnimation.value,
          child: Transform.translate(
            offset: Offset(0, 30 * (1 - _contentAnimation.value)),
            child: child,
          ),
        );
      },
      child: Padding(
        padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
        child: Row(
          children: [
            // زخرفة إسلامية
            CustomPaint(
              painter: IslamicPatterns.getFloralPattern(
                isDark: isDarkMode,
                opacity: 0.3,
                complexity: 2,
              ),
              size: const Size(30, 30),
            ),

            const SizedBox(width: 8),

            // عنوان القسم
            Text(
              'القصائد',
              style: IslamicTypography.luxurySubtitle(
                isDark: isDarkMode,
                fontSize: 22,
              ),
            ),

            const Spacer(),

            // عدد القصائد
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              decoration: BoxDecoration(
                color: isDarkMode
                    ? ManhalColors.blue900.withValues(alpha: 0.7)
                    : ManhalColors.gold100.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isDarkMode
                      ? ManhalColors.gold600.withValues(alpha: 0.3)
                      : ManhalColors.gold300,
                  width: 1,
                ),
              ),
              child: Text(
                '$poemsCount قصيدة',
                style: IslamicTypography.luxuryCategory(
                  isDark: isDarkMode,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // قائمة القصائد
  Widget _buildPoemsList(List<Poem> poems, Poet? poet,
      List<Category> categories, bool isDarkMode) {
    if (poet == null) {
      return const SliverToBoxAdapter(child: SizedBox.shrink());
    }

    // استخدام SliverList مباشرة بدلاً من SliverOpacity لتجنب مشاكل التخطيط
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          final poem = poems[index];

          // البحث عن التصنيف المناسب
          Category? category;
          if (categories.isNotEmpty) {
            category = categories.firstWhere(
              (cat) => cat.id == poem.categoryId,
              orElse: () => categories.first,
            );
          }

          return GestureDetector(
            onTap: () {
              Navigator.push(
                context,
                PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      LuxuryPoemDetailsScreen(poem: poem, poet: poet),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return FadeTransition(
                      opacity: animation,
                      child: child,
                    );
                  },
                  transitionDuration: const Duration(milliseconds: 500),
                ),
              );
            },
            child: LuxuryPoemCard(
              poem: poem,
              poet: poet,
              category: category,
              showAnimation:
                  index < 3, // تفعيل الرسوم المتحركة للقصائد الثلاث الأولى فقط
            ),
          );
        },
        childCount: poems.length,
      ),
    );
  }
}
