import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:provider/provider.dart';

import '../../data/models/poet.dart';
import '../../data/models/poem.dart';
import '../providers/manhal_rawi_provider.dart';
import '../widgets/luxury_poem_card.dart';
import '../widgets/animated_islamic_background.dart';
import 'luxury_poem_details_screen.dart';
import 'settings_screen.dart';

import '../../../../utils/theme/manhal_colors.dart';
import '../../../../utils/theme/islamic_typography.dart';
import '../../../../utils/theme/islamic_patterns.dart';
import '../../../../utils/theme/decorations.dart';

/// شاشة تفاصيل المؤلف بتصميم فاخر
class LuxuryAuthorDetailsScreen extends StatefulWidget {
  final Poet poet;

  const LuxuryAuthorDetailsScreen({
    super.key,
    required this.poet,
  });

  @override
  State<LuxuryAuthorDetailsScreen> createState() =>
      _LuxuryAuthorDetailsScreenState();
}

class _LuxuryAuthorDetailsScreenState extends State<LuxuryAuthorDetailsScreen>
    with SingleTickerProviderStateMixin {
  List<Poem> _poetPoems = [];
  bool _isLoading = true;
  late AnimationController _animationController;
  late Animation<double> _headerAnimation;
  late Animation<double> _contentAnimation;
  late Animation<double> _poemsAnimation;

  final ScrollController _scrollController = ScrollController();
  bool _showFloatingButton = false;

  @override
  void initState() {
    super.initState();

    // إنشاء متحكم الرسوم المتحركة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1800),
    );

    _headerAnimation = CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    );

    _contentAnimation = CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.3, 0.8, curve: Curves.easeOut),
    );

    _poemsAnimation = CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.5, 1.0, curve: Curves.easeOut),
    );

    // إضافة مستمع للتمرير لإظهار/إخفاء زر التمرير للأعلى
    _scrollController.addListener(() {
      if (_scrollController.offset > 300 && !_showFloatingButton) {
        setState(() {
          _showFloatingButton = true;
        });
      } else if (_scrollController.offset <= 300 && _showFloatingButton) {
        setState(() {
          _showFloatingButton = false;
        });
      }
    });

    _loadPoetPoems();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadPoetPoems() async {
    setState(() {
      _isLoading = true;
    });

    // استخدام SchedulerBinding لتأخير استدعاء filterByPoet حتى يكتمل بناء الواجهة
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      final provider = context.read<ManhalRawiProvider>();
      await provider.filterByPoet(widget.poet.id);

      if (mounted) {
        setState(() {
          _poetPoems = provider.filteredPoems;
          _isLoading = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<ManhalRawiProvider>();
    final isDarkMode = provider.isDarkMode;

    // الحصول على أبعاد الشاشة للتوافق مع مختلف الأحجام
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isLargeScreen = screenSize.width > 600;

    return Directionality(
      textDirection: TextDirection.rtl, // تأكيد اتجاه RTL للشاشة بأكملها
      child: Scaffold(
        body: AnimatedIslamicBackground(
          showPatterns: true,
          patternsOpacity: 0.05,
          child: SafeArea(
            child: Stack(
              children: [
                // المحتوى الرئيسي
                Column(
                  children: [
                    _buildAppBar(context),
                    Expanded(
                      child: _isLoading
                          ? Center(
                              child: CircularProgressIndicator(
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  isDarkMode
                                      ? ManhalColors.gold500
                                      : ManhalColors.primary,
                                ),
                              ),
                            )
                          : ListView(
                              controller: _scrollController,
                              children: [
                                const SizedBox(height: 16),

                                // بطاقة معلومات المؤلف
                                AnimatedBuilder(
                                  animation: _headerAnimation,
                                  builder: (context, child) {
                                    return Opacity(
                                      opacity: _headerAnimation.value,
                                      child: Transform.translate(
                                        offset: Offset(0,
                                            30 * (1 - _headerAnimation.value)),
                                        child: child,
                                      ),
                                    );
                                  },
                                  child: _buildAuthorHeader(
                                      context, isSmallScreen, isLargeScreen),
                                ),

                                const SizedBox(height: 24),

                                // معلومات المؤلف التفصيلية
                                AnimatedBuilder(
                                  animation: _contentAnimation,
                                  builder: (context, child) {
                                    return Opacity(
                                      opacity: _contentAnimation.value,
                                      child: Transform.translate(
                                        offset: Offset(0,
                                            30 * (1 - _contentAnimation.value)),
                                        child: child,
                                      ),
                                    );
                                  },
                                  child: _buildAuthorDetails(
                                      context, isSmallScreen, isLargeScreen),
                                ),

                                const SizedBox(height: 24),

                                // قائمة قصائد المؤلف
                                AnimatedBuilder(
                                  animation: _poemsAnimation,
                                  builder: (context, child) {
                                    return Opacity(
                                      opacity: _poemsAnimation.value,
                                      child: Transform.translate(
                                        offset: Offset(0,
                                            30 * (1 - _poemsAnimation.value)),
                                        child: child,
                                      ),
                                    );
                                  },
                                  child: _buildPoemsList(
                                      context, isSmallScreen, isLargeScreen),
                                ),

                                const SizedBox(height: 32),
                              ],
                            ),
                    ),
                  ],
                ),

                // زر التمرير للأعلى
                if (_showFloatingButton)
                  Positioned(
                    bottom: 20,
                    left: 20, // تغيير من right إلى left للتوافق مع اتجاه RTL
                    child: AnimatedOpacity(
                      opacity: _showFloatingButton ? 1.0 : 0.0,
                      duration: const Duration(milliseconds: 300),
                      child: FloatingActionButton(
                        backgroundColor: isDarkMode
                            ? ManhalColors.gold500
                            : ManhalColors.primary,
                        foregroundColor: Colors.white,
                        mini: isSmallScreen,
                        onPressed: () {
                          _scrollController.animateTo(
                            0,
                            duration: const Duration(milliseconds: 500),
                            curve: Curves.easeOutQuad,
                          );
                        },
                        child: const Icon(Icons.arrow_upward),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar(BuildContext context) {
    final isDarkMode = context.watch<ManhalRawiProvider>().isDarkMode;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.transparent,
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withValues(alpha: 0.1)
                : Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // زر الإعدادات (الآن في الجانب الأيسر)
          Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isDarkMode
                  ? ManhalColors.blue800.withValues(alpha: 0.7)
                  : Colors.white.withValues(alpha: 0.7),
              border: Border.all(
                color: isDarkMode
                    ? ManhalColors.gold500.withValues(alpha: 0.3)
                    : ManhalColors.gold300,
                width: 1,
              ),
            ),
            child: IconButton(
              icon: Icon(
                Icons.settings,
                color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
                size: 20,
              ),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SettingsScreen(),
                  ),
                );
              },
              tooltip: 'الإعدادات',
            ),
          ),

          const Spacer(),

          // زر الرجوع (الآن في الجانب الأيمن للتوافق مع اتجاه RTL)
          Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isDarkMode
                  ? ManhalColors.blue800.withValues(alpha: 0.7)
                  : Colors.white.withValues(alpha: 0.7),
              border: Border.all(
                color: isDarkMode
                    ? ManhalColors.gold500.withValues(alpha: 0.3)
                    : ManhalColors.gold300,
                width: 1,
              ),
            ),
            child: IconButton(
              icon: Icon(
                Icons
                    .arrow_forward_ios, // أيقونة تشير إلى اليمين للتوافق مع اتجاه RTL
                color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
                size: 20,
              ),
              onPressed: () {
                Navigator.pop(context);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAuthorHeader(
      BuildContext context, bool isSmallScreen, bool isLargeScreen) {
    final isDarkMode = context.watch<ManhalRawiProvider>().isDarkMode;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: isSmallScreen ? 8 : 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
          colors: isDarkMode
              ? [
                  ManhalColors.blue800,
                  ManhalColors.blue900,
                ]
              : [
                  ManhalColors.gold100.withValues(alpha: 0.7),
                  Colors.white,
                ],
        ),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.1),
            blurRadius: 15,
            spreadRadius: 1,
            offset: const Offset(0, 5),
          ),
        ],
        border: Border.all(
          color: isDarkMode
              ? ManhalColors.gold500.withValues(alpha: 0.5)
              : ManhalColors.gold300,
          width: 2,
        ),
      ),
      child: Stack(
        children: [
          // زخارف الخلفية
          Positioned.fill(
            child: CustomPaint(
              painter: IslamicPatterns.getGeometricPattern(
                isDark: isDarkMode,
                opacity: 0.05,
                complexity: 2,
              ),
            ),
          ),

          // المحتوى
          Padding(
            padding: EdgeInsets.all(isSmallScreen ? 16 : 24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // صورة المؤلف
                _buildAuthorImage(isDarkMode, isSmallScreen, isLargeScreen),

                const SizedBox(height: 20),

                // اسم المؤلف
                Text(
                  widget.poet.fullName,
                  style: IslamicTypography.luxuryTitle(
                    isDark: isDarkMode,
                    fontSize: isSmallScreen ? 24 : 30,
                  ),
                  textAlign: TextAlign.center,
                ),

                if (widget.poet.title != null) ...[
                  const SizedBox(height: 8),

                  // لقب المؤلف
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                    decoration: BoxDecoration(
                      color: isDarkMode
                          ? ManhalColors.blue900.withValues(alpha: 0.7)
                          : ManhalColors.gold100.withValues(alpha: 0.7),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: isDarkMode
                            ? ManhalColors.gold600.withValues(alpha: 0.3)
                            : ManhalColors.gold300,
                        width: 1,
                      ),
                    ),
                    child: Text(
                      widget.poet.title!,
                      style: IslamicTypography.luxuryCategory(
                        isDark: isDarkMode,
                        fontSize: isSmallScreen ? 14 : 16,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],

                const SizedBox(height: 16),

                // العصر الأدبي
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.history_edu,
                      color: isDarkMode
                          ? ManhalColors.gold500
                          : ManhalColors.primary,
                      size: isSmallScreen ? 18 : 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      widget.poet.era,
                      style: IslamicTypography.luxurySubtitle(
                        isDark: isDarkMode,
                        fontSize: isSmallScreen ? 16 : 18,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // سنوات الحياة
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildYearItem(
                      context: context,
                      label: 'الولادة',
                      year: widget.poet.birthYear,
                      isSmallScreen: isSmallScreen,
                    ),
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 16),
                      height: 1,
                      width: 40,
                      color: isDarkMode
                          ? ManhalColors.gold600.withValues(alpha: 0.3)
                          : ManhalColors.gold400.withValues(alpha: 0.5),
                    ),
                    _buildYearItem(
                      context: context,
                      label: 'الوفاة',
                      year: widget.poet.deathYear,
                      isSmallScreen: isSmallScreen,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAuthorImage(
      bool isDarkMode, bool isSmallScreen, bool isLargeScreen) {
    final imageSize = isSmallScreen ? 120.0 : (isLargeScreen ? 180.0 : 150.0);

    return Stack(
      alignment: Alignment.center,
      children: [
        // الزخرفة الخلفية
        CustomPaint(
          painter: IslamicPatterns.getGeometricPattern(
            isDark: isDarkMode,
            opacity: 0.2,
            complexity: 3,
          ),
          size: Size(imageSize + 20, imageSize + 20),
        ),

        // الإطار الخارجي
        Container(
          width: imageSize + 16,
          height: imageSize + 16,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: isDarkMode
                  ? [
                      ManhalColors.gold600,
                      ManhalColors.gold500,
                    ]
                  : [
                      ManhalColors.gold300,
                      ManhalColors.gold400,
                    ],
            ),
          ),
        ),

        // الإطار الداخلي
        Container(
          width: imageSize + 8,
          height: imageSize + 8,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isDarkMode ? ManhalColors.blue900 : Colors.white,
          ),
        ),

        // صورة المؤلف أو الأيقونة الافتراضية
        Container(
          width: imageSize,
          height: imageSize,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isDarkMode ? ManhalColors.blue800 : ManhalColors.gold100,
            boxShadow: [
              BoxShadow(
                color:
                    (isDarkMode ? ManhalColors.gold500 : ManhalColors.primary)
                        .withValues(alpha: 0.3),
                blurRadius: 10,
                spreadRadius: 2,
              ),
            ],
            image: widget.poet.imageUrl != null
                ? DecorationImage(
                    image: AssetImage(widget.poet.imageUrl!),
                    fit: BoxFit.cover,
                  )
                : null,
          ),
          child: widget.poet.imageUrl == null
              ? Icon(
                  Icons.person,
                  size: imageSize / 2,
                  color:
                      isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
                )
              : null,
        ),

        // تأثير التوهج
        if (widget.poet.imageUrl != null)
          Container(
            width: imageSize,
            height: imageSize,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: RadialGradient(
                colors: [
                  Colors.transparent,
                  isDarkMode
                      ? ManhalColors.gold500.withValues(alpha: 0.1)
                      : ManhalColors.primary.withValues(alpha: 0.1),
                ],
                stops: const [0.7, 1.0],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildAuthorDetails(
      BuildContext context, bool isSmallScreen, bool isLargeScreen) {
    final isDarkMode = context.watch<ManhalRawiProvider>().isDarkMode;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: isSmallScreen ? 8 : 16),
      decoration: IslamicPatterns.getDecorativeBorder(
        isDark: isDarkMode,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Padding(
        padding: EdgeInsets.all(isSmallScreen ? 16 : 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            Row(
              children: [
                // زخرفة إسلامية
                CustomPaint(
                  painter: IslamicPatterns.getFloralPattern(
                    isDark: isDarkMode,
                    opacity: 0.3,
                    complexity: 2,
                  ),
                  size: Size(isSmallScreen ? 24 : 30, isSmallScreen ? 24 : 30),
                ),

                const SizedBox(width: 8),

                // عنوان القسم
                Text(
                  'نبذة عن المؤلف',
                  style: IslamicTypography.luxurySubtitle(
                    isDark: isDarkMode,
                    fontSize: isSmallScreen ? 20 : 22,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // نبذة عن المؤلف
            Text(
              widget.poet.bio,
              style: IslamicTypography.luxuryBody(
                isDark: isDarkMode,
                fontSize: isSmallScreen ? 15 : 16,
              ),
              textAlign: TextAlign.justify,
            ),

            // إنجازات المؤلف
            if (widget.poet.achievements != null &&
                widget.poet.achievements!.isNotEmpty) ...[
              const SizedBox(height: 20),

              // عنوان الإنجازات
              Row(
                children: [
                  Icon(
                    Icons.emoji_events,
                    color: isDarkMode
                        ? ManhalColors.gold500
                        : ManhalColors.primary,
                    size: isSmallScreen ? 20 : 24,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'إنجازات المؤلف',
                    style: IslamicTypography.luxurySubtitle(
                      isDark: isDarkMode,
                      fontSize: isSmallScreen ? 18 : 20,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // قائمة الإنجازات
              ...widget.poet.achievements!
                  .map((achievement) => _buildAchievementItem(
                        achievement,
                        isDarkMode,
                        isSmallScreen,
                      )),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAchievementItem(
      String achievement, bool isDarkMode, bool isSmallScreen) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رمز النقطة
          Container(
            margin: const EdgeInsets.only(top: 6, left: 8, right: 8),
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
            ),
          ),

          // نص الإنجاز
          Expanded(
            child: Text(
              achievement,
              style: IslamicTypography.luxuryBody(
                isDark: isDarkMode,
                fontSize: isSmallScreen ? 14 : 15,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPoemsList(
      BuildContext context, bool isSmallScreen, bool isLargeScreen) {
    final isDarkMode = context.watch<ManhalRawiProvider>().isDarkMode;

    if (_poetPoems.isEmpty) {
      return Container(
        margin: EdgeInsets.symmetric(horizontal: isSmallScreen ? 8 : 16),
        padding: const EdgeInsets.all(24),
        decoration: ManhalDecorations.cardDecoration(isDark: isDarkMode),
        child: Center(
          child: Text(
            'لا توجد قصائد لهذا المؤلف',
            style: IslamicTypography.luxuryBody(
              isDark: isDarkMode,
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان القسم
        Padding(
          padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 8 : 16),
          child: Row(
            children: [
              // زخرفة إسلامية
              CustomPaint(
                painter: IslamicPatterns.getFloralPattern(
                  isDark: isDarkMode,
                  opacity: 0.3,
                  complexity: 2,
                ),
                size: Size(isSmallScreen ? 24 : 30, isSmallScreen ? 24 : 30),
              ),

              const SizedBox(width: 8),

              // عنوان القسم
              Text(
                'قصائد المؤلف',
                style: IslamicTypography.luxurySubtitle(
                  isDark: isDarkMode,
                  fontSize: isSmallScreen ? 20 : 22,
                ),
              ),

              const SizedBox(width: 8),

              // عدد القصائد
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: isDarkMode
                      ? ManhalColors.blue900.withValues(alpha: 0.7)
                      : ManhalColors.gold100.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isDarkMode
                        ? ManhalColors.gold600.withValues(alpha: 0.3)
                        : ManhalColors.gold300,
                    width: 1,
                  ),
                ),
                child: Text(
                  _poetPoems.length.toString(),
                  style: IslamicTypography.luxuryCategory(
                    isDark: isDarkMode,
                    fontSize: isSmallScreen ? 12 : 14,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // قائمة القصائد
        ...List.generate(_poetPoems.length, (index) {
          final poem = _poetPoems[index];

          // تطبيق تأخير متزايد للرسوم المتحركة
          final delay = index * 0.1;
          final animationValue =
              (_poemsAnimation.value - delay).clamp(0.0, 1.0);

          return Opacity(
            opacity: animationValue,
            child: Transform.translate(
              offset: Offset(0, 20 * (1 - animationValue)),
              child: Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: isSmallScreen ? 8 : 16,
                  vertical: 6,
                ),
                child: GestureDetector(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LuxuryPoemDetailsScreen(
                          poem: poem,
                          poet: widget.poet,
                        ),
                      ),
                    );
                  },
                  child: LuxuryPoemCard(
                    poem: poem,
                    poet: widget.poet,
                  ),
                ),
              ),
            ),
          );
        }),
      ],
    );
  }

  Widget _buildYearItem({
    required BuildContext context,
    required String label,
    required int? year,
    required bool isSmallScreen,
  }) {
    final isDarkMode = context.watch<ManhalRawiProvider>().isDarkMode;

    return Column(
      children: [
        // العنوان
        Text(
          label,
          style: IslamicTypography.luxuryCaption(
            isDark: isDarkMode,
            fontSize: isSmallScreen ? 12 : 14,
          ),
        ),

        const SizedBox(height: 4),

        // السنة
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
          decoration: BoxDecoration(
            color: isDarkMode
                ? ManhalColors.blue900.withValues(alpha: 0.7)
                : ManhalColors.gold100.withValues(alpha: 0.7),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isDarkMode
                  ? ManhalColors.gold600.withValues(alpha: 0.3)
                  : ManhalColors.gold300,
              width: 1,
            ),
          ),
          child: Text(
            year != null ? year.toString() : 'حتى الآن',
            style: TextStyle(
              fontFamily: 'Cairo',
              fontSize: isSmallScreen ? 12 : 14,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? ManhalColors.gold500 : ManhalColors.primary,
            ),
          ),
        ),
      ],
    );
  }
}
