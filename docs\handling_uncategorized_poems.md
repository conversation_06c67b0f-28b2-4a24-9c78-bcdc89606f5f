# التعامل مع القصائد بدون تصنيف

## نظرة عامة

يوفر تطبيق المنهل الراوي نظاماً ذكياً للتعامل مع القصائد التي لا تحتوي على تصنيف محدد في ملفات JSON. يتم تصنيف هذه القصائد تلقائياً بناءً على محتواها أو وضعها في تصنيف "متنوعة".

## التصنيفات المتاحة

1. **متنوعة** (ID: 1) - للقصائد بدون تصنيف محدد
2. **مدح** (ID: 2) - قصائد المدح والثناء
3. **وطنية** (ID: 3) - القصائد الوطنية
4. **حكمة** (ID: 4) - قصائد الحكمة والمواعظ

## كيفية عمل النظام

### 1. التصنيف التلقائي

عندما تكون القصيدة بدون `categoryId` في ملف JSON، يقوم النظام بتحليل:
- عنوان القصيدة
- وصف القصيدة

ويبحث عن الكلمات المفتاحية التالية:

#### كلمات مفتاحية للمدح:
- مدح، مديح، ثناء، إطراء، تمجيد، تعظيم

#### كلمات مفتاحية للوطنية:
- وطن، وطني، بلد، أرض، تراب، علم، نشيد

#### كلمات مفتاحية للحكمة:
- حكمة، موعظة، عبرة، درس، نصيحة، إرشاد، تعليم

### 2. التصنيف الافتراضي

إذا لم يجد النظام أي كلمات مفتاحية مطابقة، يتم وضع القصيدة في تصنيف "متنوعة".

## أمثلة على ملفات JSON

### قصيدة بدون تصنيف (ستوضع في "متنوعة"):
```json
{
  "id": 100,
  "title": "قصيدة جميلة",
  "poetId": 1,
  "description": "قصيدة في الجمال",
  "meter": "الطويل",
  "firstLetter": "ق",
  "type": "regular",
  "verses": [...]
}
```

### قصيدة ستصنف تلقائياً كـ "مدح":
```json
{
  "id": 101,
  "title": "مدح الأصدقاء",
  "poetId": 1,
  "description": "قصيدة في مدح الأصدقاء",
  "meter": "الوافر",
  "firstLetter": "م",
  "type": "regular",
  "verses": [...]
}
```

### قصيدة ستصنف تلقائياً كـ "وطنية":
```json
{
  "id": 102,
  "title": "نشيد للوطن",
  "poetId": 1,
  "description": "قصيدة وطنية في حب الوطن",
  "meter": "الكامل",
  "firstLetter": "ن",
  "type": "regular",
  "verses": [...]
}
```

### قصيدة ستصنف تلقائياً كـ "حكمة":
```json
{
  "id": 103,
  "title": "حكمة في الحياة",
  "poetId": 1,
  "description": "موعظة وحكمة في الحياة",
  "meter": "البسيط",
  "firstLetter": "ح",
  "type": "regular",
  "verses": [...]
}
```

## كيفية إضافة قصيدة بتصنيف محدد

إذا كنت تريد تحديد التصنيف بشكل صريح، أضف `categoryId` إلى ملف JSON:

```json
{
  "id": 104,
  "title": "قصيدة محددة التصنيف",
  "poetId": 1,
  "categoryId": 2,
  "description": "قصيدة مع تصنيف محدد",
  "meter": "الطويل",
  "firstLetter": "ق",
  "type": "regular",
  "verses": [...]
}
```

## الوظائف المتاحة في الكود

### في ManhalRawiProvider:

```dart
// تصفية القصائد بدون تصنيف (متنوعة)
await provider.filterByUncategorized();

// تصفية القصائد حسب تصنيف محدد
await provider.filterByCategory(categoryId);
```

### في DatabaseHelper:

```dart
// تحديد التصنيف المناسب للقصيدة تلقائياً
int categoryId = _determineCategoryId(poemData);
```

## ملاحظات مهمة

1. **الأولوية للتصنيف المحدد**: إذا كان هناك `categoryId` محدد في ملف JSON، سيتم استخدامه بدلاً من التصنيف التلقائي.

2. **حساسية البحث**: البحث عن الكلمات المفتاحية غير حساس لحالة الأحرف (case-insensitive).

3. **التحديث التلقائي**: عند إعادة تحميل البيانات من ملفات JSON، سيتم إعادة تقييم التصنيفات التلقائية.

4. **المرونة**: يمكن إضافة كلمات مفتاحية جديدة أو تصنيفات جديدة بسهولة في دالة `_determineCategoryId`.

## استكشاف الأخطاء

إذا لم يتم تصنيف القصيدة بالشكل المتوقع:

1. تحقق من وجود الكلمات المفتاحية في العنوان أو الوصف
2. تأكد من صحة كتابة الكلمات المفتاحية
3. راجع قائمة الكلمات المفتاحية في دالة `_determineCategoryId`
4. استخدم `categoryId` صريح إذا لزم الأمر
