import 'dart:async';
import 'dart:math'; // إضافة استيراد لفئة Random
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../data/models/verse.dart';
import '../providers/manhal_rawi_provider.dart';
import '../../../../utils/theme/manhal_colors.dart';
import '../../../../utils/theme/islamic_typography.dart';
import '../../../../utils/theme/islamic_patterns.dart';
import '../../../../utils/animations/islamic_animations.dart';

/// مكون عرض الأبيات الشعرية بتنسيق كتاب مفتوح مع تكيف ديناميكي لحجم الأبيات
class BookStyleVerseDisplay extends StatefulWidget {
  final List<Verse> verses;
  final int selectedIndex;
  final Function(int)? onVerseSelected;
  final bool showAnimation;
  final bool enableSelection;
  final Function()? onFullScreenToggle;
  final bool isFullScreen;
  final bool hideControls; // إضافة خاصية جديدة للتحكم في إخفاء عناصر التحكم

  const BookStyleVerseDisplay({
    super.key,
    required this.verses,
    this.selectedIndex = 0,
    this.onVerseSelected,
    this.showAnimation = true,
    this.enableSelection = true,
    this.onFullScreenToggle,
    this.isFullScreen = false,
    this.hideControls = false, // إضافة معلمة جديدة للتحكم في إخفاء عناصر التحكم
  });

  @override
  State<BookStyleVerseDisplay> createState() => _BookStyleVerseDisplayState();
}

// نموذج لتخزين معلومات الأبيات المتزامنة
class SynchronizedVersePair {
  final int index;
  final Verse verse;

  // إنشاء زوج أبيات جديد
  SynchronizedVersePair({required this.index, required this.verse});
}

class _BookStyleVerseDisplayState extends State<BookStyleVerseDisplay>
    with TickerProviderStateMixin {
  // تغيير إلى TickerProviderStateMixin لدعم أكثر من AnimationController
  late PageController _pageController;
  late int _currentIndex;
  late AnimationController _animationController;

  // متغير للتحكم في ظهور واختفاء زر ملء الشاشة
  late AnimationController _buttonAnimationController;
  late Animation<double> _buttonOpacityAnimation;
  bool _isButtonVisible = true; // نبدأ بإظهار الزر
  Timer? _buttonVisibilityTimer;
  Timer? _periodicShowButtonTimer; // مؤقت لإظهار الزر بشكل دوري

  // متغير لتخزين مزود البيانات
  late ManhalRawiProvider _provider;

  // متغير لتخزين وحدة تحكم التمرير المشتركة
  final ScrollController _sharedScrollController = ScrollController();

  // لا نحتاج لمتغيرات المزامنة في هذا الحل الجديد

  // متغيرات لتتبع حالة السحب
  bool _isDragging = false;
  double _dragStartX = 0;
  double _dragPosition = 0;

  // متغير لتخزين عدد الأبيات في كل صفحة (سيتم حسابه ديناميكياً)
  int _versesPerPage = 0;

  // مؤقت لتأخير حساب ارتفاعات الأبيات
  Timer? _resizeTimer;

  @override
  void initState() {
    super.initState();
    _currentIndex =
        widget.selectedIndex ~/ 2; // تقسيم على 2 لأن كل صفحة تحتوي على صفحتين
    _pageController = PageController(initialPage: _currentIndex);

    // إعداد التحريك للصفحات
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    // إعداد تحريك زر ملء الشاشة
    _buttonAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
      value: 1.0, // نبدأ بالقيمة 1.0 (ظاهر بالكامل)
    );

    _buttonOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _buttonAnimationController,
      curve: Curves.easeInOut,
    ));

    // تهيئة مزود البيانات
    _provider = Provider.of<ManhalRawiProvider>(context, listen: false);

    // حساب عدد الأبيات في كل صفحة بناءً على حجم الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _calculateVersesPerPage();
      _initializeSynchronizedVerses();

      // تأكد من أن الزر ظاهر عند بدء التشغيل
      _showButton();

      // إضافة مؤقت لإظهار الزر بشكل دوري للتأكد من أنه مرئي دائمًا
      _periodicShowButtonTimer =
          Timer.periodic(const Duration(seconds: 5), (timer) {
        if (mounted) {
          _showButton();
        } else {
          // إلغاء المؤقت إذا تم التخلص من الويدجت
          timer.cancel();
        }
      });
    });

    // إضافة مستمع للتمرير لإخفاء/إظهار الزر
    _sharedScrollController.addListener(_handleScroll);
  }

  // دالة للتعامل مع حدث التمرير - تم تحسينها لضمان عودة الزر
  void _handleScroll() {
    // إلغاء المؤقت الحالي إذا كان نشطًا
    if (_buttonVisibilityTimer?.isActive ?? false) {
      _buttonVisibilityTimer!.cancel();
    }

    // إذا كان المستخدم يقوم بالتمرير، نخفي الزر
    if (_sharedScrollController.position.isScrollingNotifier.value) {
      _hideButton();

      // إضافة مؤقت لإظهار الزر بعد فترة زمنية حتى لو استمر التمرير
      // هذا يضمن أن الزر سيظهر مرة أخرى حتى لو استمر المستخدم في التمرير لفترة طويلة
      _buttonVisibilityTimer = Timer(const Duration(seconds: 3), () {
        _showButton();
      });
    } else {
      // إذا توقف التمرير، نظهر الزر بعد تأخير قصير
      _buttonVisibilityTimer = Timer(const Duration(milliseconds: 500), () {
        _showButton();
      });
    }
  }

  // دالة لإظهار زر ملء الشاشة - تم تحسينها لتكون أكثر موثوقية
  void _showButton() {
    // تأكد من أن الزر سيظهر حتى لو كان مرئيًا بالفعل
    if (mounted) {
      setState(() {
        _isButtonVisible = true;
      });

      // تأكد من أن الرسوم المتحركة تبدأ من البداية
      if (_buttonAnimationController.status == AnimationStatus.dismissed) {
        _buttonAnimationController.forward();
      } else if (_buttonAnimationController.status == AnimationStatus.reverse) {
        // إذا كانت الرسوم المتحركة في حالة الإخفاء، قم بعكسها
        _buttonAnimationController.forward();
      }
    }
  }

  // دالة لإخفاء زر ملء الشاشة - تم تحسينها لتكون أكثر موثوقية
  void _hideButton() {
    if (_isButtonVisible && mounted) {
      // تقليل مدة الرسوم المتحركة لإخفاء الزر بشكل أسرع
      _buttonAnimationController.duration = const Duration(milliseconds: 200);
      _buttonAnimationController.reverse().then((_) {
        if (mounted) {
          setState(() {
            _isButtonVisible = false;
          });

          // إعادة تعيين مدة الرسوم المتحركة إلى القيمة الأصلية
          _buttonAnimationController.duration =
              const Duration(milliseconds: 300);
        }
      });
    }
  }

  // تهيئة الأبيات المتزامنة - لم تعد ضرورية بعد التعديلات
  void _initializeSynchronizedVerses() {
    // لم نعد نحتاج إلى تخزين الأبيات في قائمة عامة
    // لأننا نقوم بإنشاء أزواج جديدة لكل صفحة

    // جدولة حساب ارتفاعات الأبيات بعد رسم الإطار
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _synchronizeVerseHeights();
    });
  }

  // مزامنة عرض الأبيات
  void _synchronizeVerseHeights() {
    if (_resizeTimer != null) {
      _resizeTimer!.cancel();
    }

    // تأخير قصير للتأكد من أن جميع العناصر قد تم رسمها
    _resizeTimer = Timer(const Duration(milliseconds: 50), () {
      if (!mounted) return;

      // تحديث واجهة المستخدم
      setState(() {});
    });
  }

  // لا نحتاج لدالة مزامنة التمرير في هذا الحل الجديد

  // Variable para controlar si el cambio de índice debe provocar navegación
  bool _allowPageNavigation = true;

  @override
  void didUpdateWidget(BookStyleVerseDisplay oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Solo permitimos la navegación automática si no proviene de un clic en un verso
    if (_allowPageNavigation) {
      final newPageIndex = widget.selectedIndex ~/ 2;
      if (newPageIndex != _currentIndex &&
          oldWidget.selectedIndex != widget.selectedIndex) {
        _currentIndex = newPageIndex;

        // Animación suave a la nueva página
        _pageController.animateToPage(
          _currentIndex,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      }
    }

    // تحديث حالة العرض بملء الشاشة أو تغير حجم الخط أو تغير البيت المحدد
    if (widget.isFullScreen != oldWidget.isFullScreen ||
        _provider.fontSize !=
            Provider.of<ManhalRawiProvider>(context, listen: false).fontSize ||
        _provider.fontFamily !=
            Provider.of<ManhalRawiProvider>(context, listen: false)
                .fontFamily ||
        widget.selectedIndex != oldWidget.selectedIndex) {
      // تحديث مزود البيانات
      _provider = Provider.of<ManhalRawiProvider>(context, listen: false);

      // إعادة حساب عدد الأبيات في كل صفحة
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _calculateVersesPerPage();

        // إعادة مزامنة عرض الأبيات
        _synchronizeVerseHeights();
      });
    }

    // Restauramos el permiso de navegación para futuros cambios externos
    _allowPageNavigation = true;
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    _buttonAnimationController.dispose();
    _sharedScrollController.dispose();
    _sharedScrollController.removeListener(_handleScroll);

    // إلغاء المؤقتات إذا كانت نشطة
    _resizeTimer?.cancel();
    _buttonVisibilityTimer?.cancel();
    _periodicShowButtonTimer?.cancel(); // إلغاء المؤقت الدوري

    super.dispose();
  }

  // حساب عدد الأبيات في كل صفحة بناءً على إعدادات المستخدم وحجم الشاشة
  void _calculateVersesPerPage() {
    // استخدام عدد الأبيات المحدد من إعدادات المستخدم
    final provider = context.read<ManhalRawiProvider>();
    final userVersesPerPage = provider.versesPerPage;

    // تعديل عدد الأبيات بناءً على حالة ملء الشاشة
    int versesPerPage = userVersesPerPage;

    // في حالة ملء الشاشة، نزيد عدد الأبيات لاستغلال المساحة
    if (widget.isFullScreen) {
      // زيادة عدد الأبيات في وضع ملء الشاشة بنسبة 20%
      versesPerPage = (versesPerPage * 1.2).round();
    }

    // التأكد من أن عدد الأبيات لا يقل عن 2
    versesPerPage = versesPerPage.clamp(2, 50);

    setState(() {
      _versesPerPage = versesPerPage;
    });
  }

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<ManhalRawiProvider>();
    final isDarkMode = provider.isDarkMode;
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isLargeScreen = screenSize.width > 600;

    // إذا لم يتم حساب عدد الأبيات بعد، نستخدم قيمة افتراضية
    // تعديل عدد الأبيات بناءً على حجم الشاشة
    if (_versesPerPage == 0) {
      if (isSmallScreen) {
        _versesPerPage = 2; // عدد أقل للشاشات الصغيرة
      } else if (isLargeScreen) {
        _versesPerPage = 5; // عدد أكبر للشاشات الكبيرة
      } else {
        _versesPerPage = 3; // القيمة الافتراضية للشاشات المتوسطة
      }
    }

    // حساب عدد الصفحات
    final int totalPages = (widget.verses.length / _versesPerPage).ceil();

    // إذا كان في وضع ملء الشاشة، نعرض المحتوى في شاشة كاملة
    if (widget.isFullScreen) {
      return Container(
        // خلفية أكثر فخامة لوضع ملء الشاشة
        decoration: BoxDecoration(
          // تدرج لوني أكثر عمقًا وفخامة
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: isDarkMode
                ? [
                    ManhalColors.blue900.withValues(alpha: 1.0),
                    ManhalColors.blue800.withValues(alpha: 0.95),
                    ManhalColors.blue900.withValues(alpha: 1.0),
                  ]
                : [
                    ManhalColors.gold100.withValues(alpha: 0.2),
                    Colors.white,
                    ManhalColors.gold100.withValues(alpha: 0.3),
                  ],
          ),
          // إضافة صورة خلفية فاخرة بشفافية منخفضة
          image: DecorationImage(
            image: AssetImage(
              isDarkMode
                  ? 'assets/images/paper_texture_dark.png'
                  : 'assets/images/paper_texture_light.png',
            ),
            fit: BoxFit.cover,
            opacity: isDarkMode ? 0.05 : 0.1,
          ),
        ),
        child: Column(
          children: [
            // عنوان فاخر في وضع ملء الشاشة - يظهر فقط إذا لم يتم طلب إخفاء عناصر التحكم
            if (!widget.hideControls)
              Padding(
                padding: const EdgeInsets.only(top: 16, bottom: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  textDirection: TextDirection.rtl,
                  children: [
                    // زخرفة إسلامية
                    CustomPaint(
                      painter: IslamicPatterns.getFloralPattern(
                        isDark: isDarkMode,
                        opacity: 0.5,
                        complexity: 2,
                      ),
                      size: const Size(28, 28),
                    ),

                    const SizedBox(width: 12),

                    // عنوان الكتاب
                    Text(
                      'المنهل الروي',
                      style: IslamicTypography.luxuryTitle(
                        isDark: isDarkMode,
                        fontSize: 24,
                      ),
                    ),

                    const SizedBox(width: 12),

                    // زخرفة إسلامية
                    CustomPaint(
                      painter: IslamicPatterns.getFloralPattern(
                        isDark: isDarkMode,
                        opacity: 0.5,
                        complexity: 2,
                      ),
                      size: const Size(28, 28),
                    ),
                  ],
                ),
              ),

            // عرض الكتاب المفتوح في وضع ملء الشاشة
            GestureDetector(
              onHorizontalDragStart: (details) {
                setState(() {
                  _isDragging = true;
                  _dragStartX = details.localPosition.dx;
                  _dragPosition = 0;
                });
                // إظهار الزر عند بدء السحب
                _showButton();
              },
              // إضافة معالج النقر لإظهار الزر
              onTap: () {
                _showButton();
              },
              onHorizontalDragUpdate: (details) {
                if (_isDragging) {
                  setState(() {
                    _dragPosition = details.localPosition.dx - _dragStartX;
                  });
                }
              },
              onHorizontalDragEnd: (details) {
                if (_isDragging) {
                  final velocity = details.primaryVelocity ?? 0;
                  final pageWidth = MediaQuery.of(context).size.width;

                  // تحسين شروط التنقل بين الصفحات لجعلها أكثر تحكمًا
                  // زيادة عتبة المسافة والسرعة لتجنب التنقل العرضي
                  if (_dragPosition.abs() >
                          pageWidth / 3 || // زيادة العتبة من 1/4 إلى 1/3
                      velocity.abs() > 300) {
                    // زيادة عتبة السرعة من 200 إلى 300

                    // تأكد من أن المستخدم يسحب بوضوح في اتجاه معين
                    // في اللغة العربية، السحب لليسار يعني الصفحة السابقة والسحب لليمين يعني الصفحة التالية
                    final isLeftSwipe =
                        _dragPosition < 0 && velocity < -50; // سحب لليسار
                    final isRightSwipe =
                        _dragPosition > 0 && velocity > 50; // سحب لليمين

                    if (isLeftSwipe) {
                      // سحب لليسار - الصفحة السابقة (في اللغة العربية)
                      if (_currentIndex > 0) {
                        _pageController.previousPage(
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeOut,
                        );
                      }
                    } else if (isRightSwipe) {
                      // سحب لليمين - الصفحة التالية (في اللغة العربية)
                      if (_currentIndex < totalPages - 1) {
                        _pageController.nextPage(
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeOut,
                        );
                      }
                    }
                  }

                  setState(() {
                    _isDragging = false;
                    _dragPosition = 0;
                  });
                }
              },
              child: Container(
                // زيادة ارتفاع الكتاب ليشغل مساحة أكبر من الشاشة
                height: MediaQuery.of(context).size.height * 0.95,
                // تقليل الهوامش لاستغلال مساحة أكبر
                margin: const EdgeInsets.symmetric(horizontal: 12),
                decoration: BoxDecoration(
                  color: Colors.transparent,
                  // تقليل نصف قطر الزوايا لمظهر أكثر كلاسيكية
                  borderRadius: BorderRadius.circular(12),
                  // تحسين الظلال لإضفاء عمق أكبر
                  boxShadow: [
                    // ظل أساسي
                    BoxShadow(
                      color: isDarkMode
                          ? Colors.black.withValues(alpha: 0.5)
                          : Colors.black.withValues(alpha: 0.15),
                      blurRadius: 20,
                      spreadRadius: 2,
                      offset: const Offset(0, 5),
                    ),
                    // ظل ثانوي لإضافة عمق
                    BoxShadow(
                      color: isDarkMode
                          ? ManhalColors.gold500.withValues(alpha: 0.05)
                          : ManhalColors.gold300.withValues(alpha: 0.1),
                      blurRadius: 30,
                      spreadRadius: -2,
                      offset: const Offset(0, 2),
                    ),
                  ],
                  // إضافة حدود رقيقة للكتاب
                  border: Border.all(
                    color: isDarkMode
                        ? ManhalColors.gold500.withValues(alpha: 0.1)
                        : ManhalColors.gold300.withValues(alpha: 0.2),
                    width: 0.5,
                  ),
                ),
                child: Stack(
                  children: [
                    // خلفية الكتاب المحسنة
                    Container(
                      decoration: BoxDecoration(
                        borderRadius:
                            BorderRadius.circular(12), // تناسق مع حاوية الكتاب
                        // تدرج لوني محسن للكتاب المفتوح
                        gradient: LinearGradient(
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                          colors: isDarkMode
                              ? [
                                  // ألوان أكثر عمقًا وفخامة للوضع المظلم
                                  ManhalColors.blue900.withValues(alpha: 0.95),
                                  ManhalColors.blue800.withValues(alpha: 0.9),
                                  ManhalColors.blue800.withValues(alpha: 0.9),
                                  ManhalColors.blue900.withValues(alpha: 0.95),
                                ]
                              : [
                                  // ألوان أكثر دفئًا وفخامة للوضع الفاتح
                                  ManhalColors.gold100.withValues(alpha: 0.5),
                                  Colors.white.withValues(alpha: 0.98),
                                  Colors.white.withValues(alpha: 0.98),
                                  ManhalColors.gold100.withValues(alpha: 0.5),
                                ],
                          stops: const [
                            0.0,
                            0.3,
                            0.7,
                            1.0
                          ], // توزيع أفضل للألوان
                        ),
                        // تحسين ملمس الورق
                        image: DecorationImage(
                          image: AssetImage(
                            isDarkMode
                                ? 'assets/images/paper_texture_dark.png'
                                : 'assets/images/paper_texture_light.png',
                          ),
                          fit: BoxFit.cover,
                          opacity:
                              isDarkMode ? 0.08 : 0.12, // زيادة طفيفة في الوضوح
                          // تكرار الملمس لتفاصيل أكثر
                          repeat: ImageRepeat.repeat,
                        ),
                      ),
                    ),

                    // خط الطية في المنتصف - محسن ليكون أكثر واقعية
                    Center(
                      child: Container(
                        width: 3, // زيادة عرض الخط قليلاً
                        height: double.infinity,
                        decoration: BoxDecoration(
                          // تدرج لوني أكثر تعقيدًا للطية
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.transparent,
                              isDarkMode
                                  ? ManhalColors.gold500.withValues(alpha: 0.2)
                                  : ManhalColors.primary.withValues(alpha: 0.1),
                              isDarkMode
                                  ? ManhalColors.gold500.withValues(alpha: 0.6)
                                  : ManhalColors.primary.withValues(alpha: 0.4),
                              isDarkMode
                                  ? ManhalColors.gold500.withValues(alpha: 0.6)
                                  : ManhalColors.primary.withValues(alpha: 0.4),
                              isDarkMode
                                  ? ManhalColors.gold500.withValues(alpha: 0.2)
                                  : ManhalColors.primary.withValues(alpha: 0.1),
                              Colors.transparent,
                            ],
                            stops: const [
                              0.0,
                              0.15,
                              0.5,
                              0.85,
                              1.0
                            ], // توزيع أكثر دقة للألوان
                          ),
                          // إضافة ظل خفيف للطية
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 3,
                              spreadRadius: 1,
                              offset: const Offset(0, 0),
                            ),
                          ],
                        ),
                      ),
                    ),

                    // ظل الطية - محسن ليكون أكثر واقعية وعمقًا
                    Center(
                      child: Container(
                        width: 40, // زيادة عرض منطقة الظل
                        height: double.infinity,
                        decoration: BoxDecoration(
                          // تدرج لوني أكثر تعقيدًا للظل
                          gradient: LinearGradient(
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                            colors: [
                              // الجانب الأيسر من الظل
                              isDarkMode
                                  ? Colors.black.withValues(alpha: 0.25)
                                  : Colors.black.withValues(alpha: 0.15),
                              Colors.transparent,
                              // خط الطية
                              isDarkMode
                                  ? Colors.black.withValues(alpha: 0.05)
                                  : Colors.black.withValues(alpha: 0.03),
                              Colors.transparent,
                              // الجانب الأيمن من الظل
                              isDarkMode
                                  ? Colors.black.withValues(alpha: 0.25)
                                  : Colors.black.withValues(alpha: 0.15),
                            ],
                            // توزيع أكثر دقة للألوان
                            stops: const [0.0, 0.3, 0.5, 0.7, 1.0],
                          ),
                        ),
                      ),
                    ),

                    // إضافة تأثير انعكاس الضوء على الطية
                    Center(
                      child: Container(
                        width: 6, // عرض ضيق للانعكاس
                        height: double.infinity,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.transparent,
                              isDarkMode
                                  ? Colors.white.withValues(alpha: 0.05)
                                  : Colors.white.withValues(alpha: 0.2),
                              isDarkMode
                                  ? Colors.white.withValues(alpha: 0.1)
                                  : Colors.white.withValues(alpha: 0.4),
                              isDarkMode
                                  ? Colors.white.withValues(alpha: 0.05)
                                  : Colors.white.withValues(alpha: 0.2),
                              Colors.transparent,
                            ],
                            stops: const [0.0, 0.3, 0.5, 0.7, 1.0],
                          ),
                        ),
                      ),
                    ),

                    // صفحات الكتاب
                    PageView.builder(
                      controller: _pageController,
                      physics:
                          const NeverScrollableScrollPhysics(), // نعطل التمرير الافتراضي ونستخدم التمرير المخصص فقط
                      onPageChanged: (pageIndex) {
                        // تحديث مؤشر الصفحة الحالية فقط عند تغيير الصفحة يدويًا
                        setState(() {
                          _currentIndex = pageIndex;
                        });

                        // لا نقوم بتحديث البيت المحدد تلقائيًا عند تغيير الصفحة
                        // لمنع التنقل التلقائي غير المرغوب فيه

                        // تشغيل تأثير تقليب الصفحة
                        _animationController.reset();
                        _animationController.forward();
                      },
                      itemCount: totalPages,
                      itemBuilder: (context, pageIndex) {
                        // حساب نطاق الأبيات في هذه الصفحة
                        final startIndex = pageIndex * _versesPerPage;
                        final endIndex =
                            (startIndex + _versesPerPage) < widget.verses.length
                                ? startIndex + _versesPerPage
                                : widget.verses.length;

                        return _buildBookPage(
                          verses: widget.verses.sublist(startIndex, endIndex),
                          pageIndex: pageIndex,
                          startIndex: startIndex,
                          isDarkMode: isDarkMode,
                        );
                      },
                    ),

                    // تأثير تقليب الصفحة
                    if (_isDragging) _buildPageFlipEffect(isDarkMode),

                    // زر إغلاق وضع ملء الشاشة - تصميم فاخر ومتناسق لا يتداخل مع المحتوى - يظهر فقط إذا لم يتم طلب إخفاء عناصر التحكم
                    if (widget.onFullScreenToggle != null &&
                        _isButtonVisible &&
                        !widget.hideControls)
                      Positioned(
                        bottom:
                            16, // وضع الزر في الأسفل بدلاً من الأعلى لتجنب تغطية البيت الأول
                        left:
                            16, // وضع الزر على اليسار (في اللغة العربية يكون على يسار الشاشة)
                        child: AnimatedBuilder(
                          animation: _buttonOpacityAnimation,
                          builder: (context, child) {
                            return Opacity(
                              opacity: _buttonOpacityAnimation.value,
                              child: child,
                            );
                          },
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: isDarkMode
                                  ? Colors.black.withValues(
                                      alpha:
                                          0.5) // تنسيق مع fullscreen_verse_screen.dart
                                  : Colors.white.withValues(alpha: 0.5),
                              shape: BoxShape.circle, // شكل دائري أكثر أناقة
                              boxShadow: [
                                BoxShadow(
                                  color: isDarkMode
                                      ? Colors.black.withValues(alpha: 0.3)
                                      : Colors.grey.withValues(alpha: 0.2),
                                  blurRadius: 4,
                                  spreadRadius: 1,
                                  offset: const Offset(0, 1),
                                ),
                              ],
                              border: Border.all(
                                color: isDarkMode
                                    ? Colors.white.withValues(
                                        alpha:
                                            0.7) // حدود بيضاء أكثر وضوحًا في الوضع المظلم
                                    : ManhalColors.gold300,
                                width: isDarkMode
                                    ? 1.2
                                    : 0.8, // زيادة سمك الحدود في الوضع المظلم
                              ),
                            ),
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                borderRadius: BorderRadius.circular(20),
                                splashColor: isDarkMode
                                    ? ManhalColors.gold500
                                        .withValues(alpha: 0.2)
                                    : ManhalColors.primary
                                        .withValues(alpha: 0.2),
                                onTap: widget.onFullScreenToggle,
                                child: Padding(
                                  padding: const EdgeInsets.all(6.0),
                                  child: Icon(
                                    Icons.fullscreen_exit,
                                    color: isDarkMode
                                        ? Colors
                                            .white // استخدام اللون الأبيض للأيقونة في الوضع المظلم لزيادة الوضوح
                                        : ManhalColors.primary,
                                    size: 20,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),

            // أزرار التنقل في وضع ملء الشاشة - تصميم فاخر ومتناسق - تظهر فقط إذا لم يتم طلب إخفاء عناصر التحكم
            if (!widget.hideControls)
              Padding(
                padding: const EdgeInsets.only(bottom: 16, top: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  textDirection: TextDirection
                      .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                  children: [
                    // زر الصفحة السابقة (في اللغة العربية يكون على اليسار)
                    _buildFullscreenNavigationButton(
                      icon: Icons.arrow_back_ios,
                      isDarkMode: isDarkMode,
                      onPressed: _currentIndex > 0
                          ? () {
                              _pageController.previousPage(
                                duration: const Duration(milliseconds: 500),
                                curve: Curves.easeInOut,
                              );
                            }
                          : null,
                    ),

                    const SizedBox(width: 24),

                    // مؤشر الصفحة الحالية - تصميم فاخر
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 10),
                      decoration: BoxDecoration(
                        color: isDarkMode
                            ? ManhalColors.blue800.withValues(alpha: 0.7)
                            : ManhalColors.gold100.withValues(alpha: 0.7),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: isDarkMode
                              ? ManhalColors.gold500.withValues(alpha: 0.3)
                              : ManhalColors.gold300,
                          width: 1,
                        ),
                        // إضافة ظل خفيف
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 5,
                            spreadRadius: 1,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Text(
                        'صفحة ${(_currentIndex * 2) + 2}-${(_currentIndex * 2) + 1} من ${totalPages * 2}', // تم إعادة أرقام الصفحات
                        style: IslamicTypography.luxuryCaption(
                          isDark: isDarkMode,
                          fontSize: 16,
                        ),
                        textDirection: TextDirection
                            .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                      ),
                    ),

                    const SizedBox(width: 24),

                    // زر الصفحة التالية (في اللغة العربية يكون على اليمين)
                    _buildFullscreenNavigationButton(
                      icon: Icons.arrow_forward_ios,
                      isDarkMode: isDarkMode,
                      onPressed: _currentIndex < totalPages - 1
                          ? () {
                              _pageController.nextPage(
                                duration: const Duration(milliseconds: 500),
                                curve: Curves.easeInOut,
                              );
                            }
                          : null,
                    ),
                  ],
                ),
              ),
          ],
        ),
      );
    }

    // الوضع العادي (غير ملء الشاشة)
    return LayoutBuilder(builder: (context, constraints) {
      // حساب الارتفاع المتاح للعرض
      final availableHeight = constraints.maxHeight;

      // تحديد ارتفاع الكتاب بناءً على المساحة المتاحة
      final bookHeight = availableHeight * 0.75; // 75% من الارتفاع المتاح

      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // عنوان الكتاب
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // زخرفة إسلامية
                CustomPaint(
                  painter: IslamicPatterns.getFloralPattern(
                    isDark: isDarkMode,
                    opacity: 0.3,
                    complexity: 1,
                  ),
                  size: const Size(24, 24),
                ),

                const SizedBox(width: 8),

                // عنوان الكتاب
                Text(
                  'أبيات القصيدة',
                  style: IslamicTypography.luxurySubtitle(
                    isDark: isDarkMode,
                    fontSize: 20,
                  ),
                ),

                const SizedBox(width: 8),

                // زخرفة إسلامية
                CustomPaint(
                  painter: IslamicPatterns.getFloralPattern(
                    isDark: isDarkMode,
                    opacity: 0.3,
                    complexity: 1,
                  ),
                  size: const Size(24, 24),
                ),
              ],
            ),
          ),

          // عرض الكتاب المفتوح
          Flexible(
            child: GestureDetector(
              onHorizontalDragStart: (details) {
                setState(() {
                  _isDragging = true;
                  _dragStartX = details.localPosition.dx;
                  _dragPosition = 0;
                });
                // إظهار الزر عند بدء السحب
                _showButton();
              },
              // إضافة معالج النقر لإظهار الزر
              onTap: () {
                _showButton();
              },
              onHorizontalDragUpdate: (details) {
                if (_isDragging) {
                  setState(() {
                    _dragPosition = details.localPosition.dx - _dragStartX;
                  });
                }
              },
              onHorizontalDragEnd: (details) {
                if (_isDragging) {
                  final velocity = details.primaryVelocity ?? 0;
                  final pageWidth = MediaQuery.of(context).size.width;

                  // تحسين شروط التنقل بين الصفحات لجعلها أكثر تحكمًا
                  // زيادة عتبة المسافة والسرعة لتجنب التنقل العرضي
                  if (_dragPosition.abs() >
                          pageWidth / 3 || // زيادة العتبة من 1/4 إلى 1/3
                      velocity.abs() > 300) {
                    // زيادة عتبة السرعة من 200 إلى 300

                    // تأكد من أن المستخدم يسحب بوضوح في اتجاه معين
                    // في اللغة العربية، السحب لليسار يعني الصفحة السابقة والسحب لليمين يعني الصفحة التالية
                    final isLeftSwipe =
                        _dragPosition < 0 && velocity < -50; // سحب لليسار
                    final isRightSwipe =
                        _dragPosition > 0 && velocity > 50; // سحب لليمين

                    if (isLeftSwipe) {
                      // سحب لليسار - الصفحة السابقة (في اللغة العربية)
                      if (_currentIndex > 0) {
                        _pageController.previousPage(
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeOut,
                        );
                      }
                    } else if (isRightSwipe) {
                      // سحب لليمين - الصفحة التالية (في اللغة العربية)
                      if (_currentIndex < totalPages - 1) {
                        _pageController.nextPage(
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeOut,
                        );
                      }
                    }
                  }

                  setState(() {
                    _isDragging = false;
                    _dragPosition = 0;
                  });
                }
              },
              child: Container(
                // استخدام ارتفاع مرن بدلاً من ثابت
                height: bookHeight,
                margin: const EdgeInsets.symmetric(horizontal: 16),
                decoration: BoxDecoration(
                  color: Colors.transparent,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: isDarkMode
                          ? Colors.black.withValues(alpha: 0.3)
                          : Colors.black.withValues(alpha: 0.1),
                      blurRadius: 15,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: Stack(
                  children: [
                    // خلفية الكتاب
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        gradient: LinearGradient(
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                          colors: isDarkMode
                              ? [
                                  ManhalColors.blue900,
                                  ManhalColors.blue800,
                                  ManhalColors.blue800,
                                  ManhalColors.blue900,
                                ]
                              : [
                                  ManhalColors.gold100.withValues(alpha: 0.7),
                                  Colors.white,
                                  Colors.white,
                                  ManhalColors.gold100.withValues(alpha: 0.7),
                                ],
                        ),
                        image: DecorationImage(
                          image: AssetImage(
                            isDarkMode
                                ? 'assets/images/paper_texture_dark.png'
                                : 'assets/images/paper_texture_light.png',
                          ),
                          fit: BoxFit.cover,
                          opacity: 0.1,
                        ),
                      ),
                    ),

                    // خط الطية في المنتصف
                    Center(
                      child: Container(
                        width: 2,
                        height: double.infinity,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.transparent,
                              isDarkMode
                                  ? ManhalColors.gold500.withValues(alpha: 0.5)
                                  : ManhalColors.primary.withValues(alpha: 0.3),
                              isDarkMode
                                  ? ManhalColors.gold500.withValues(alpha: 0.5)
                                  : ManhalColors.primary.withValues(alpha: 0.3),
                              Colors.transparent,
                            ],
                          ),
                        ),
                      ),
                    ),

                    // ظل الطية
                    Center(
                      child: Container(
                        width: 20,
                        height: double.infinity,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                            colors: [
                              Colors.black.withValues(alpha: 0.1),
                              Colors.transparent,
                              Colors.transparent,
                              Colors.black.withValues(alpha: 0.1),
                            ],
                          ),
                        ),
                      ),
                    ),

                    // صفحات الكتاب
                    PageView.builder(
                      controller: _pageController,
                      physics:
                          const NeverScrollableScrollPhysics(), // نعطل التمرير الافتراضي ونستخدم التمرير المخصص فقط
                      onPageChanged: (pageIndex) {
                        // تحديث مؤشر الصفحة الحالية فقط عند تغيير الصفحة يدويًا
                        setState(() {
                          _currentIndex = pageIndex;
                        });

                        // لا نقوم بتحديث البيت المحدد تلقائيًا عند تغيير الصفحة
                        // لمنع التنقل التلقائي غير المرغوب فيه

                        // تشغيل تأثير تقليب الصفحة
                        _animationController.reset();
                        _animationController.forward();
                      },
                      itemCount: totalPages,
                      itemBuilder: (context, pageIndex) {
                        // حساب نطاق الأبيات في هذه الصفحة بترتيب تصاعدي
                        final startIndex = pageIndex * _versesPerPage;
                        final endIndex =
                            (startIndex + _versesPerPage) < widget.verses.length
                                ? startIndex + _versesPerPage
                                : widget.verses.length;

                        return _buildBookPage(
                          verses: widget.verses.sublist(startIndex, endIndex),
                          pageIndex: pageIndex,
                          startIndex: startIndex,
                          isDarkMode: isDarkMode,
                        );
                      },
                    ),

                    // تأثير تقليب الصفحة
                    if (_isDragging) _buildPageFlipEffect(isDarkMode),

                    // أزرار التحكم في وضع ملء الشاشة - تصميم فاخر ومتناسق مع تحريك - يظهر فقط إذا لم يتم طلب إخفاء عناصر التحكم
                    if (widget.onFullScreenToggle != null &&
                        _isButtonVisible &&
                        !widget.hideControls)
                      Positioned(
                        bottom:
                            16, // وضع الزر في الأسفل بدلاً من الأعلى لتجنب تغطية البيت الأول
                        left:
                            16, // وضع الزر على اليسار (في اللغة العربية يكون على يسار الشاشة)
                        child: AnimatedBuilder(
                          animation: _buttonOpacityAnimation,
                          builder: (context, child) {
                            return Opacity(
                              opacity: _buttonOpacityAnimation.value,
                              child: child,
                            );
                          },
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: isDarkMode
                                  ? Colors.black.withValues(
                                      alpha:
                                          0.5) // تنسيق مع fullscreen_verse_screen.dart
                                  : Colors.white.withValues(alpha: 0.5),
                              shape: BoxShape.circle, // شكل دائري أكثر أناقة
                              boxShadow: [
                                BoxShadow(
                                  color: isDarkMode
                                      ? Colors.black.withValues(alpha: 0.3)
                                      : Colors.grey.withValues(alpha: 0.2),
                                  blurRadius: 4,
                                  spreadRadius: 1,
                                  offset: const Offset(0, 1),
                                ),
                              ],
                              border: Border.all(
                                color: isDarkMode
                                    ? Colors.white.withValues(
                                        alpha:
                                            0.7) // حدود بيضاء أكثر وضوحًا في الوضع المظلم
                                    : ManhalColors.gold300,
                                width: isDarkMode
                                    ? 1.2
                                    : 0.8, // زيادة سمك الحدود في الوضع المظلم
                              ),
                            ),
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                borderRadius: BorderRadius.circular(20),
                                splashColor: isDarkMode
                                    ? ManhalColors.gold500
                                        .withValues(alpha: 0.2)
                                    : ManhalColors.primary
                                        .withValues(alpha: 0.2),
                                onTap: widget.onFullScreenToggle,
                                child: Padding(
                                  padding: const EdgeInsets.all(6.0),
                                  child: Icon(
                                    widget.isFullScreen
                                        ? Icons.fullscreen_exit
                                        : Icons.fullscreen,
                                    color: isDarkMode
                                        ? Colors
                                            .white // استخدام اللون الأبيض للأيقونة في الوضع المظلم لزيادة الوضوح
                                        : ManhalColors.primary,
                                    size: 20,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),

          // أزرار التنقل (تظهر فقط في الوضع العادي)
          if (!widget.isFullScreen) ...[
            const SizedBox(height: 8),

            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              textDirection:
                  TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
              children: [
                // زر الصفحة السابقة (في اللغة العربية يكون على اليسار)
                _buildNavigationButton(
                  icon: Icons.arrow_back_ios,
                  isDarkMode: isDarkMode,
                  onPressed: _currentIndex > 0
                      ? () {
                          _pageController.previousPage(
                            duration: const Duration(milliseconds: 500),
                            curve: Curves.easeInOut,
                          );
                        }
                      : null,
                ),

                const SizedBox(width: 16),

                // مؤشر الصفحة الحالية
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? ManhalColors.blue800.withValues(alpha: 0.7)
                        : ManhalColors.gold100.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: isDarkMode
                          ? ManhalColors.gold500.withValues(alpha: 0.3)
                          : ManhalColors.gold300,
                      width: 1,
                    ),
                  ),
                  child: Text(
                    'صفحة ${(_currentIndex * 2) + 2}-${(_currentIndex * 2) + 1} من ${totalPages * 2}', // تم إعادة أرقام الصفحات
                    style: IslamicTypography.luxuryCaption(
                      isDark: isDarkMode,
                      fontSize: 14,
                    ),
                    textDirection: TextDirection
                        .rtl, // تحديد اتجاه النص من اليمين إلى اليسار
                  ),
                ),

                const SizedBox(width: 16),

                // زر الصفحة التالية (في اللغة العربية يكون على اليمين)
                _buildNavigationButton(
                  icon: Icons.arrow_forward_ios,
                  isDarkMode: isDarkMode,
                  onPressed: _currentIndex < totalPages - 1
                      ? () {
                          _pageController.nextPage(
                            duration: const Duration(milliseconds: 500),
                            curve: Curves.easeInOut,
                          );
                        }
                      : null,
                ),
              ],
            ),

            const SizedBox(height: 8),

            // مؤشرات الصفحات
            if (totalPages > 1)
              SizedBox(
                height: 8,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  shrinkWrap: true,
                  itemCount: totalPages,
                  itemBuilder: (context, index) {
                    return _buildPageIndicator(index, isDarkMode);
                  },
                ),
              ),
          ],
        ],
      );
    });
  }

  Widget _buildBookPage({
    required List<Verse> verses,
    required int pageIndex,
    required int startIndex,
    required bool isDarkMode,
  }) {
    // جدولة مزامنة ارتفاعات الأبيات بعد رسم الإطار
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _synchronizeVerseHeights();
    });

    // استخدام SingleChildScrollView واحد للصفحتين معًا
    return GestureDetector(
      // إيقاف انتشار أحداث السحب للعناصر الأبناء
      onHorizontalDragStart: null,
      onHorizontalDragUpdate: null,
      onHorizontalDragEnd: null,
      child: SingleChildScrollView(
        controller: _sharedScrollController,
        physics: const BouncingScrollPhysics(),
        child: ConstrainedBox(
          constraints: BoxConstraints(
            // تحديد ارتفاع أدنى للمحتوى لتجنب مشكلة تجاوز الحجم
            minHeight: widget.isFullScreen
                ? MediaQuery.of(context).size.height * 0.8
                : MediaQuery.of(context).size.height * 0.5,
          ),
          child: Column(
            children: [
              // عنوان الصفحة
              _buildPageHeader(isDarkMode),

              // الأبيات المتزامنة
              ...verses.asMap().entries.map((entry) {
                final int verseIndex = startIndex + entry.key;
                final Verse verse = entry.value;
                final bool isSelected = verseIndex == widget.selectedIndex;

                // إنشاء زوج أبيات جديد لكل بيت في الصفحة الحالية
                // هذا يضمن أن كل بيت يحصل على مفاتيح فريدة
                final SynchronizedVersePair versePair = SynchronizedVersePair(
                  index: verseIndex,
                  verse: verse,
                );

                return _buildSynchronizedVersePair(
                  versePair: versePair,
                  verseIndex: verseIndex,
                  isSelected: isSelected,
                  isDarkMode: isDarkMode,
                );
              }),

              // زخرفة أسفل الصفحة
              _buildPageFooter(isDarkMode, pageIndex),
            ],
          ),
        ),
      ),
    );
  }

  // بناء عنوان الصفحة بتصميم فاخر ومتناسق
  Widget _buildPageHeader(bool isDarkMode) {
    // تحديد حجم الشاشة لتكييف العناصر
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;
    final isLargeScreen = screenWidth > 600;

    // تعديل الأحجام بناءً على حجم الشاشة
    final patternSize = isSmallScreen ? 14.0 : (isLargeScreen ? 20.0 : 16.0);
    final lineWidth = isSmallScreen ? 0.7 : (isLargeScreen ? 1.2 : 0.9);
    final verticalPadding = isSmallScreen ? 4.0 : (isLargeScreen ? 8.0 : 6.0);
    final horizontalPadding =
        isSmallScreen ? 12.0 : (isLargeScreen ? 20.0 : 16.0);

    // تقليل حجم الخط للعنوان
    final titleFontSize = isSmallScreen ? 12.0 : (isLargeScreen ? 16.0 : 14.0);

    return Padding(
      padding: EdgeInsets.only(
        top: 2, // تقليل المسافة العلوية
        bottom: 6,
        left: horizontalPadding,
        right: horizontalPadding,
      ),
      child: Container(
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: isDarkMode
                  ? ManhalColors.gold500.withValues(alpha: 0.3)
                  : ManhalColors.gold300,
              width: lineWidth,
            ),
          ),
        ),
        child: Row(
          textDirection:
              TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // زخرفة يمين - تم تصغيرها
            CustomPaint(
              painter: IslamicPatterns.getFloralPattern(
                isDark: isDarkMode,
                opacity: 0.3,
                complexity: 1,
              ),
              size: Size(patternSize, patternSize),
            ),

            // خط زخرفي
            Expanded(
              child: Container(
                height: lineWidth,
                margin: EdgeInsets.symmetric(
                  horizontal: 6, // تقليل المسافة الأفقية
                  vertical: verticalPadding,
                ),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.centerRight,
                    end: Alignment.centerLeft,
                    colors: [
                      isDarkMode
                          ? ManhalColors.gold500.withValues(alpha: 0.1)
                          : ManhalColors.gold300.withValues(alpha: 0.1),
                      isDarkMode
                          ? ManhalColors.gold500.withValues(alpha: 0.5)
                          : ManhalColors.gold300,
                      isDarkMode
                          ? ManhalColors.gold500.withValues(alpha: 0.5)
                          : ManhalColors.gold300,
                      isDarkMode
                          ? ManhalColors.gold500.withValues(alpha: 0.1)
                          : ManhalColors.gold300.withValues(alpha: 0.1),
                    ],
                  ),
                ),
              ),
            ),

            // عنوان مركزي فاخر - تم تصغيره
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 6),
              child: Text(
                'أبيات القصيدة',
                style: IslamicTypography.luxuryTitle(
                  isDark: isDarkMode,
                  fontSize: titleFontSize,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            // خط زخرفي
            Expanded(
              child: Container(
                height: lineWidth,
                margin: EdgeInsets.symmetric(
                  horizontal: 6, // تقليل المسافة الأفقية
                  vertical: verticalPadding,
                ),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.centerRight,
                    end: Alignment.centerLeft,
                    colors: [
                      isDarkMode
                          ? ManhalColors.gold500.withValues(alpha: 0.1)
                          : ManhalColors.gold300.withValues(alpha: 0.1),
                      isDarkMode
                          ? ManhalColors.gold500.withValues(alpha: 0.5)
                          : ManhalColors.gold300,
                      isDarkMode
                          ? ManhalColors.gold500.withValues(alpha: 0.5)
                          : ManhalColors.gold300,
                      isDarkMode
                          ? ManhalColors.gold500.withValues(alpha: 0.1)
                          : ManhalColors.gold300.withValues(alpha: 0.1),
                    ],
                  ),
                ),
              ),
            ),

            // زخرفة يسار - تم تصغيرها
            CustomPaint(
              painter: IslamicPatterns.getFloralPattern(
                isDark: isDarkMode,
                opacity: 0.3,
                complexity: 1,
              ),
              size: Size(patternSize, patternSize),
            ),
          ],
        ),
      ),
    );
  }

  // بناء تذييل الصفحة
  Widget _buildPageFooter(bool isDarkMode, int pageIndex) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        children: [
          // زخرفة
          Center(
            child: CustomPaint(
              painter: IslamicPatterns.getFloralPattern(
                isDark: isDarkMode,
                opacity: 0.2,
                complexity: 1,
              ),
              size: const Size(30, 30),
            ),
          ),

          // تم إزالة أرقام الصفحات واستبدالها بزخرفة إضافية
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: CustomPaint(
              painter: IslamicPatterns.getGeometricPattern(
                isDark: isDarkMode,
                opacity: 0.2,
                complexity: 1,
              ),
              size: const Size(20, 10),
            ),
          ),
        ],
      ),
    );
  }

  // بناء زوج الأبيات المتزامن
  Widget _buildSynchronizedVersePair({
    required SynchronizedVersePair versePair,
    required int verseIndex,
    required bool isSelected,
    required bool isDarkMode,
  }) {
    // تحديد حجم الشاشة لتكييف العناصر
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;
    final isLargeScreen = screenWidth > 600;

    // تقليل المسافات لجعل الأبيات أكثر تقاربًا وواقعية مثل الكتاب
    final verticalPadding =
        isSmallScreen ? 4.0 : (isLargeScreen ? 6.0 : 5.0); // تم تقليل المسافة
    final horizontalPadding =
        isSmallScreen ? 4.0 : (isLargeScreen ? 8.0 : 6.0); // تم تقليل المسافة
    final bottomMargin =
        isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0); // تم تقليل المسافة

    // تعديل حجم الخط بناءً على حجم الشاشة
    final baseFontSize = _provider.fontSize;
    final adjustedFontSize = isSmallScreen
        ? baseFontSize * 0.9
        : (isLargeScreen ? baseFontSize * 1.1 : baseFontSize);

    return GestureDetector(
      // منع انتشار الحدث للعناصر الأب
      behavior: HitTestBehavior.opaque,
      onTap: widget.enableSelection
          ? () {
              if (widget.onVerseSelected != null) {
                // تعطيل التنقل التلقائي لهذا التغيير في المؤشر
                _allowPageNavigation = false;

                // فقط تحديد البيت المنقور عليه دون أي تأثير على تنقل الصفحات
                widget.onVerseSelected!(verseIndex);

                // تحديث حالة العرض
                setState(() {
                  // تأكد من أن السحب غير نشط
                  _isDragging = false;
                  _dragPosition = 0;
                });
              }
            }
          : null,
      child: Container(
        margin: EdgeInsets.only(bottom: bottomMargin),
        child: Row(
          textDirection:
              TextDirection.rtl, // تحديد اتجاه النص من اليمين إلى اليسار
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // الشطر الأول (يمين)
            Expanded(
              child: Container(
                padding: EdgeInsets.symmetric(
                  vertical: verticalPadding,
                  horizontal: horizontalPadding,
                ),
                // تحسين مظهر الأبيات لتبدو أكثر واقعية مثل الكتاب
                decoration: BoxDecoration(
                  color: isSelected
                      ? (isDarkMode
                          ? ManhalColors.blue800.withValues(alpha: 0.2)
                          : ManhalColors.gold100.withValues(alpha: 0.2))
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(
                      isSmallScreen ? 4.0 : (isLargeScreen ? 6.0 : 5.0)),
                  border: Border(
                    bottom: BorderSide(
                      color: isDarkMode
                          ? (isSelected
                              ? ManhalColors.gold500.withValues(alpha: 0.2)
                              : ManhalColors.gold500.withValues(alpha: 0.05))
                          : (isSelected
                              ? ManhalColors.primary.withValues(alpha: 0.2)
                              : ManhalColors.gold300.withValues(alpha: 0.05)),
                      width: isSmallScreen ? 0.3 : (isLargeScreen ? 0.7 : 0.5),
                    ),
                  ),
                ),
                child: widget.showAnimation && isSelected
                    ? IslamicAnimations.arabicTextTyping(
                        text: versePair.verse.first,
                        style: _getTextStyle(
                          fontFamily: _provider.fontFamily,
                          fontSize: adjustedFontSize,
                          color: isDarkMode ? Colors.white : Colors.black87,
                          fontWeight:
                              isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                        durationMs: 800,
                        textAlign: TextAlign.center,
                      )
                    : Text(
                        versePair.verse.first,
                        style: _getTextStyle(
                          fontFamily: _provider.fontFamily,
                          fontSize: adjustedFontSize,
                          color: isDarkMode ? Colors.white : Colors.black87,
                          fontWeight:
                              isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                        textAlign: TextAlign.center,
                        textDirection: TextDirection.rtl,
                      ),
              ),
            ),

            // فاصل
            const SizedBox(width: 8),

            // الشطر الثاني (يسار)
            Expanded(
              child: Container(
                padding: EdgeInsets.symmetric(
                  vertical: verticalPadding,
                  horizontal: horizontalPadding,
                ),
                // تحسين مظهر الأبيات لتبدو أكثر واقعية مثل الكتاب
                decoration: BoxDecoration(
                  color: isSelected
                      ? (isDarkMode
                          ? ManhalColors.blue800.withValues(alpha: 0.2)
                          : ManhalColors.gold100.withValues(alpha: 0.2))
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(
                      isSmallScreen ? 4.0 : (isLargeScreen ? 6.0 : 5.0)),
                  border: Border(
                    bottom: BorderSide(
                      color: isDarkMode
                          ? (isSelected
                              ? ManhalColors.gold500.withValues(alpha: 0.2)
                              : ManhalColors.gold500.withValues(alpha: 0.05))
                          : (isSelected
                              ? ManhalColors.primary.withValues(alpha: 0.2)
                              : ManhalColors.gold300.withValues(alpha: 0.05)),
                      width: isSmallScreen ? 0.3 : (isLargeScreen ? 0.7 : 0.5),
                    ),
                  ),
                ),
                child: widget.showAnimation && isSelected
                    ? IslamicAnimations.arabicTextTyping(
                        text: versePair.verse.second,
                        style: _getTextStyle(
                          fontFamily: _provider.fontFamily,
                          fontSize: adjustedFontSize,
                          color: isDarkMode ? Colors.white : Colors.black87,
                          fontWeight:
                              isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                        durationMs: 800,
                        delayMs: 400,
                        textAlign: TextAlign.center,
                      )
                    : Text(
                        versePair.verse.second,
                        style: _getTextStyle(
                          fontFamily: _provider.fontFamily,
                          fontSize: adjustedFontSize,
                          color: isDarkMode ? Colors.white : Colors.black87,
                          fontWeight:
                              isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                        textAlign: TextAlign.center,
                        textDirection: TextDirection.rtl,
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPageFlipEffect(bool isDarkMode) {
    final width = MediaQuery.of(context).size.width;
    final dragPercentage = (_dragPosition / (width / 2)).clamp(-1.0, 1.0);

    return Positioned(
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      child: CustomPaint(
        painter: PageFlipPainter(
          progress: dragPercentage,
          isDarkMode: isDarkMode,
        ),
      ),
    );
  }

  // زر التنقل في الوضع العادي
  Widget _buildNavigationButton({
    required IconData icon,
    required bool isDarkMode,
    required VoidCallback? onPressed,
  }) {
    // تحديد حجم الشاشة لتكييف العناصر
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;
    final isLargeScreen = screenWidth > 600;

    // تعديل الأحجام بناءً على حجم الشاشة
    final iconSize = isSmallScreen ? 14.0 : (isLargeScreen ? 20.0 : 16.0);
    final buttonPadding = isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0);
    final borderWidth = isSmallScreen ? 0.5 : (isLargeScreen ? 1.5 : 1.0);

    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: onPressed != null
            ? (isDarkMode
                ? ManhalColors.blue800.withValues(alpha: 0.7)
                : ManhalColors.gold100.withValues(alpha: 0.7))
            : (isDarkMode
                ? Colors.grey.withValues(alpha: 0.3)
                : Colors.grey.withValues(alpha: 0.2)),
        border: Border.all(
          color: onPressed != null
              ? (isDarkMode
                  ? ManhalColors.gold500.withValues(alpha: 0.3)
                  : ManhalColors.gold300)
              : Colors.grey,
          width: borderWidth,
        ),
      ),
      child: IconButton(
        icon: Icon(
          icon,
          color: onPressed != null
              ? (isDarkMode ? ManhalColors.gold500 : ManhalColors.primary)
              : Colors.grey,
          size: iconSize,
        ),
        onPressed: onPressed,
        padding: EdgeInsets.all(buttonPadding),
        constraints: BoxConstraints(
          minWidth: isSmallScreen ? 32.0 : (isLargeScreen ? 48.0 : 40.0),
          minHeight: isSmallScreen ? 32.0 : (isLargeScreen ? 48.0 : 40.0),
        ),
      ),
    );
  }

  // زر التنقل في وضع ملء الشاشة - تصميم فاخر ومتناسق
  Widget _buildFullscreenNavigationButton({
    required IconData icon,
    required bool isDarkMode,
    required VoidCallback? onPressed,
  }) {
    // تحديد حجم الشاشة لتكييف العناصر
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;
    final isLargeScreen = screenWidth > 600;

    // تعديل الأحجام بناءً على حجم الشاشة - أزرار أكبر في وضع ملء الشاشة
    final iconSize = isSmallScreen ? 18.0 : (isLargeScreen ? 24.0 : 20.0);
    final buttonPadding = isSmallScreen ? 8.0 : (isLargeScreen ? 12.0 : 10.0);
    final borderWidth = isSmallScreen ? 1.0 : (isLargeScreen ? 2.0 : 1.5);

    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: onPressed != null
            ? (isDarkMode
                ? ManhalColors.blue800.withValues(alpha: 0.8)
                : ManhalColors.gold100.withValues(alpha: 0.8))
            : (isDarkMode
                ? Colors.grey.withValues(alpha: 0.3)
                : Colors.grey.withValues(alpha: 0.2)),
        border: Border.all(
          color: onPressed != null
              ? (isDarkMode
                  ? ManhalColors.gold500.withValues(alpha: 0.5)
                  : ManhalColors.gold300)
              : Colors.grey,
          width: borderWidth,
        ),
        // إضافة ظل للزر
        boxShadow: onPressed != null
            ? [
                BoxShadow(
                  color: isDarkMode
                      ? Colors.black.withValues(alpha: 0.3)
                      : Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  spreadRadius: 1,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(30),
          splashColor: isDarkMode
              ? ManhalColors.gold500.withValues(alpha: 0.2)
              : ManhalColors.primary.withValues(alpha: 0.2),
          onTap: onPressed,
          child: Padding(
            padding: EdgeInsets.all(buttonPadding),
            child: Icon(
              icon,
              color: onPressed != null
                  ? (isDarkMode ? Colors.white : ManhalColors.primary)
                  : Colors.grey,
              size: iconSize,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPageIndicator(int index, bool isDarkMode) {
    final isActive = index == _currentIndex;

    // تحديد حجم الشاشة لتكييف العناصر
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;
    final isLargeScreen = screenWidth > 600;

    // تعديل الأحجام بناءً على حجم الشاشة
    final indicatorHeight = isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0);
    final activeWidth = isSmallScreen ? 18.0 : (isLargeScreen ? 30.0 : 24.0);
    final inactiveWidth = isSmallScreen ? 6.0 : (isLargeScreen ? 10.0 : 8.0);
    final horizontalMargin = isSmallScreen ? 2.0 : (isLargeScreen ? 6.0 : 4.0);
    final borderRadius = isSmallScreen ? 3.0 : (isLargeScreen ? 5.0 : 4.0);
    final blurRadius = isSmallScreen ? 3.0 : (isLargeScreen ? 5.0 : 4.0);
    final spreadRadius = isSmallScreen ? 0.5 : (isLargeScreen ? 1.5 : 1.0);

    return GestureDetector(
      onTap: () {
        _pageController.animateToPage(
          index,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        margin: EdgeInsets.symmetric(horizontal: horizontalMargin),
        height: indicatorHeight,
        width: isActive ? activeWidth : inactiveWidth,
        decoration: BoxDecoration(
          color: isActive
              ? (isDarkMode ? ManhalColors.gold500 : ManhalColors.primary)
              : (isDarkMode ? ManhalColors.blue700 : ManhalColors.gold200),
          borderRadius: BorderRadius.circular(borderRadius),
          boxShadow: isActive
              ? [
                  BoxShadow(
                    color: isDarkMode
                        ? ManhalColors.gold500.withValues(alpha: 0.3)
                        : ManhalColors.primary.withValues(alpha: 0.3),
                    blurRadius: blurRadius,
                    spreadRadius: spreadRadius,
                  ),
                ]
              : null,
        ),
      ),
    );
  }

  // دالة مساعدة للحصول على نمط الخط المناسب باستخدام الخطوط المحلية
  TextStyle _getTextStyle({
    required String fontFamily,
    required double fontSize,
    required Color color,
    FontWeight fontWeight = FontWeight.normal,
  }) {
    // تقليل ارتفاع النص لجعل الأبيات أكثر تقاربًا وواقعية مثل الكتاب
    const double textHeight = 1.3; // قيمة منخفضة لجعل النص أكثر تقاربًا

    switch (fontFamily) {
      case 'Amiri':
        return TextStyle(
          fontFamily: 'Amiri',
          fontSize: fontSize,
          color: color,
          fontWeight: fontWeight,
          height: textHeight, // إضافة ارتفاع النص المخفض
          letterSpacing: -0.2, // تقليل المسافة بين الحروف قليلاً
        );
      case 'Cairo':
        return TextStyle(
          fontFamily: 'Cairo',
          fontSize: fontSize,
          color: color,
          fontWeight: fontWeight,
          height: textHeight, // إضافة ارتفاع النص المخفض
          letterSpacing: -0.2, // تقليل المسافة بين الحروف قليلاً
        );
      case 'Markazi':
        return TextStyle(
          fontFamily: 'Markazi',
          fontSize: fontSize,
          color: color,
          fontWeight: fontWeight,
          height: textHeight, // إضافة ارتفاع النص المخفض
          letterSpacing: -0.2, // تقليل المسافة بين الحروف قليلاً
        );
      case 'Raqq':
        return TextStyle(
          fontFamily: 'Scheherazade',
          fontSize: fontSize,
          color: color,
          fontWeight: fontWeight,
          height: textHeight, // إضافة ارتفاع النص المخفض
          letterSpacing: -0.2, // تقليل المسافة بين الحروف قليلاً
        );
      case 'Naskh':
        return TextStyle(
          fontFamily: 'Naskh',
          fontSize: fontSize,
          color: color,
          fontWeight: fontWeight,
          height: textHeight, // إضافة ارتفاع النص المخفض
          letterSpacing: -0.2, // تقليل المسافة بين الحروف قليلاً
        );
      default:
        return TextStyle(
          fontFamily: 'Amiri',
          fontSize: fontSize,
          color: color,
          fontWeight: fontWeight,
          height: textHeight, // إضافة ارتفاع النص المخفض
          letterSpacing: -0.2, // تقليل المسافة بين الحروف قليلاً
        );
    }
  }
}

/// رسام تأثير تقليب الصفحة - محسن ليكون أكثر واقعية وفخامة
class PageFlipPainter extends CustomPainter {
  final double progress; // -1 إلى 1
  final bool isDarkMode;

  PageFlipPainter({
    required this.progress,
    required this.isDarkMode,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;
    final centerX = width / 2;

    // لا نرسم شيئًا إذا كان التقدم صفرًا
    if (progress == 0) return;

    // تحديد اتجاه التقليب
    final isRightToLeft = progress > 0;
    final absProgress = progress.abs();

    // حساب نقطة انحناء الصفحة
    final curvePoint = centerX + (width / 2) * progress;

    // رسم خلفية الصفحة المقلوبة
    final pagePaint = Paint()
      ..color = isDarkMode
          ? ManhalColors.blue800.withValues(alpha: 0.9)
          : Colors.white.withValues(alpha: 0.95)
      ..style = PaintingStyle.fill;

    // رسم ظل الصفحة المقلوبة - ظل أكثر واقعية
    final shadowPaint = Paint()
      ..color = Colors.black.withValues(alpha: 0.4) // ظل أكثر كثافة
      ..style = PaintingStyle.fill
      ..maskFilter = MaskFilter.blur(
          BlurStyle.normal, 12 * absProgress); // ظل متغير حسب التقدم

    // رسم تدرج الظل على الصفحة المقلوبة
    final gradientPaint = Paint()
      ..shader = LinearGradient(
        begin: isRightToLeft ? Alignment.centerRight : Alignment.centerLeft,
        end: Alignment.center,
        colors: [
          Colors.black.withValues(alpha: 0.3 * absProgress),
          Colors.transparent,
        ],
      ).createShader(Rect.fromLTWH(
        isRightToLeft ? centerX : curvePoint,
        0,
        (width / 2) * absProgress,
        height,
      ))
      ..style = PaintingStyle.fill;

    // رسم انعكاس الضوء على حافة الصفحة
    final highlightPaint = Paint()
      ..color = isDarkMode
          ? Colors.white.withValues(alpha: 0.2 * absProgress)
          : Colors.white.withValues(alpha: 0.5 * absProgress)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.5
      ..strokeCap = StrokeCap.round;

    // رسم حافة الصفحة المقلوبة
    final edgePaint = Paint()
      ..color = isDarkMode
          ? ManhalColors.gold500.withValues(alpha: 0.7)
          : ManhalColors.primary.withValues(alpha: 0.5)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;

    // إنشاء مسار الصفحة المقلوبة
    final pagePath = Path();

    // إنشاء مسار الظل
    final shadowPath = Path();

    // إنشاء مسار حافة الصفحة
    final edgePath = Path();

    // إنشاء مسار انعكاس الضوء
    final highlightPath = Path();

    // تحديد نقاط المسار حسب اتجاه التقليب
    if (isRightToLeft) {
      // تقليب من اليمين إلى اليسار

      // مسار الصفحة
      pagePath.moveTo(centerX, 0);
      // إضافة منحنى بيزيه لجعل الصفحة تبدو أكثر واقعية
      pagePath.cubicTo(
        centerX + (width / 4) * absProgress,
        height * 0.1,
        curvePoint - (width / 8) * absProgress,
        height * 0.4,
        curvePoint,
        height * 0.5,
      );
      pagePath.cubicTo(
        curvePoint - (width / 8) * absProgress,
        height * 0.6,
        centerX + (width / 4) * absProgress,
        height * 0.9,
        centerX,
        height,
      );
      pagePath.close();

      // مسار الظل (أوسع قليلاً من مسار الصفحة)
      shadowPath.moveTo(centerX, 0);
      shadowPath.cubicTo(
        centerX + (width / 4) * absProgress,
        height * 0.1,
        curvePoint - (width / 8) * absProgress,
        height * 0.4,
        curvePoint + 5,
        height * 0.5,
      );
      shadowPath.cubicTo(
        curvePoint - (width / 8) * absProgress,
        height * 0.6,
        centerX + (width / 4) * absProgress,
        height * 0.9,
        centerX,
        height,
      );
      shadowPath.close();

      // مسار حافة الصفحة
      edgePath.moveTo(centerX, 0);
      edgePath.cubicTo(
        centerX + (width / 4) * absProgress,
        height * 0.1,
        curvePoint - (width / 8) * absProgress,
        height * 0.4,
        curvePoint,
        height * 0.5,
      );
      edgePath.cubicTo(
        curvePoint - (width / 8) * absProgress,
        height * 0.6,
        centerX + (width / 4) * absProgress,
        height * 0.9,
        centerX,
        height,
      );

      // مسار انعكاس الضوء (على طول حافة الصفحة)
      highlightPath.moveTo(centerX, 0);
      highlightPath.cubicTo(
        centerX + (width / 4) * absProgress,
        height * 0.1,
        curvePoint - (width / 8) * absProgress,
        height * 0.4,
        curvePoint,
        height * 0.5,
      );
    } else {
      // تقليب من اليسار إلى اليمين

      // مسار الصفحة
      pagePath.moveTo(centerX, 0);
      // إضافة منحنى بيزيه لجعل الصفحة تبدو أكثر واقعية
      pagePath.cubicTo(
        centerX + (width / 4) * progress,
        height * 0.1,
        curvePoint - (width / 8) * progress,
        height * 0.4,
        curvePoint,
        height * 0.5,
      );
      pagePath.cubicTo(
        curvePoint - (width / 8) * progress,
        height * 0.6,
        centerX + (width / 4) * progress,
        height * 0.9,
        centerX,
        height,
      );
      pagePath.close();

      // مسار الظل (أوسع قليلاً من مسار الصفحة)
      shadowPath.moveTo(centerX, 0);
      shadowPath.cubicTo(
        centerX + (width / 4) * progress,
        height * 0.1,
        curvePoint - (width / 8) * progress,
        height * 0.4,
        curvePoint - 5,
        height * 0.5,
      );
      shadowPath.cubicTo(
        curvePoint - (width / 8) * progress,
        height * 0.6,
        centerX + (width / 4) * progress,
        height * 0.9,
        centerX,
        height,
      );
      shadowPath.close();

      // مسار حافة الصفحة
      edgePath.moveTo(centerX, 0);
      edgePath.cubicTo(
        centerX + (width / 4) * progress,
        height * 0.1,
        curvePoint - (width / 8) * progress,
        height * 0.4,
        curvePoint,
        height * 0.5,
      );
      edgePath.cubicTo(
        curvePoint - (width / 8) * progress,
        height * 0.6,
        centerX + (width / 4) * progress,
        height * 0.9,
        centerX,
        height,
      );

      // مسار انعكاس الضوء (على طول حافة الصفحة)
      highlightPath.moveTo(centerX, 0);
      highlightPath.cubicTo(
        centerX + (width / 4) * progress,
        height * 0.1,
        curvePoint - (width / 8) * progress,
        height * 0.4,
        curvePoint,
        height * 0.5,
      );
    }

    // رسم الظل أولاً
    canvas.drawPath(shadowPath, shadowPaint);

    // ثم رسم الصفحة
    canvas.drawPath(pagePath, pagePaint);

    // رسم تدرج الظل على الصفحة
    canvas.drawPath(pagePath, gradientPaint);

    // رسم حافة الصفحة
    canvas.drawPath(edgePath, edgePaint);

    // رسم انعكاس الضوء
    canvas.drawPath(highlightPath, highlightPaint);

    // إضافة ملمس الورق إلى الصفحة المقلوبة
    if (absProgress > 0.2) {
      // رسم نقاط صغيرة عشوائية لمحاكاة ملمس الورق
      final random = Random(42); // استخدام بذرة ثابتة للحصول على نمط متسق
      final texturePaint = Paint()
        ..color = isDarkMode
            ? Colors.white.withValues(alpha: 0.03)
            : Colors.black.withValues(alpha: 0.02)
        ..style = PaintingStyle.fill;

      for (int i = 0; i < 100; i++) {
        final x = centerX + random.nextDouble() * (width / 2) * progress;
        final y = random.nextDouble() * height;

        // التحقق مما إذا كانت النقطة داخل مسار الصفحة
        if (pagePath.contains(Offset(x, y))) {
          canvas.drawCircle(Offset(x, y), 0.5, texturePaint);
        }
      }
    }
  }

  @override
  bool shouldRepaint(covariant PageFlipPainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.isDarkMode != isDarkMode;
  }
}
