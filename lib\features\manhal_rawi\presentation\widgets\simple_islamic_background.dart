import 'package:flutter/material.dart';
import '../../../../utils/theme/manhal_colors.dart';
import '../../../../utils/theme/islamic_patterns.dart';

/// خلفية إسلامية بسيطة للتطبيق
class SimpleIslamicBackground extends StatelessWidget {
  final Widget child;
  final bool isDark;
  final bool showPatterns;

  const SimpleIslamicBackground({
    super.key,
    required this.child,
    required this.isDark,
    this.showPatterns = true,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // الخلفية المتدرجة
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: isDark
                  ? [
                      ManhalColors.blue900,
                      ManhalColors.blue800,
                    ]
                  : [
                      Colors.white,
                      ManhalColors.gold100.withValues(alpha: 0.3),
                    ],
            ),
          ),
        ),

        // الزخارف الإسلامية
        if (showPatterns)
          Opacity(
            opacity: 0.15,
            child: CustomPaint(
              painter: _SimplePatternPainter(
                isDark: isDark,
              ),
              size: Size.infinite,
            ),
          ),

        // الهلال الإسلامي في الزاوية العلوية
        Positioned(
          top: 20,
          right: 20,
          child: Opacity(
            opacity: 0.2,
            child: IslamicPatterns.getCrescentDecoration(
              isDark: isDark,
              size: 60,
              opacity: 0.5,
            ),
          ),
        ),

        // الهلال الإسلامي في الزاوية السفلية
        Positioned(
          bottom: 20,
          left: 20,
          child: Opacity(
            opacity: 0.2,
            child: IslamicPatterns.getCrescentDecoration(
              isDark: isDark,
              size: 40,
              opacity: 0.5,
            ),
          ),
        ),

        // المحتوى الرئيسي
        child,
      ],
    );
  }
}

/// رسام الزخارف الإسلامية البسيطة
class _SimplePatternPainter extends CustomPainter {
  final bool isDark;

  _SimplePatternPainter({
    required this.isDark,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final baseColor = isDark ? ManhalColors.gold500 : ManhalColors.primary;
    final paint = Paint()
      ..color = baseColor.withValues(alpha: 0.1)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    final width = size.width;
    final height = size.height;

    // رسم الزخارف في الزوايا
    _drawCornerPattern(canvas, paint, Offset(0, 0), size);
    _drawCornerPattern(canvas, paint, Offset(width, 0), size);
    _drawCornerPattern(canvas, paint, Offset(0, height), size);
    _drawCornerPattern(canvas, paint, Offset(width, height), size);

    // رسم الزخارف في المنتصف
    _drawCenterPattern(canvas, paint, Offset(width / 2, height / 2), size);
  }

  void _drawCornerPattern(
      Canvas canvas, Paint paint, Offset corner, Size size) {
    final patternSize =
        size.width < size.height ? size.width / 3 : size.height / 3;

    // No necesitamos crear un Path aquí
    final isTopLeft = corner.dx == 0 && corner.dy == 0;
    final isTopRight = corner.dx == size.width && corner.dy == 0;
    final isBottomLeft = corner.dx == 0 && corner.dy == size.height;

    // تحديد نقطة البداية حسب الزاوية
    Offset start;
    if (isTopLeft) {
      start = Offset(corner.dx + 20, corner.dy + 20);
    } else if (isTopRight) {
      start = Offset(corner.dx - 20, corner.dy + 20);
    } else if (isBottomLeft) {
      start = Offset(corner.dx + 20, corner.dy - 20);
    } else {
      start = Offset(corner.dx - 20, corner.dy - 20);
    }

    // رسم الزخارف الدائرية
    for (int i = 0; i < 3; i++) {
      final radius = patternSize * 0.3 * (i + 1);

      if (isTopLeft) {
        canvas.drawArc(
          Rect.fromCircle(center: start, radius: radius),
          3.14159 * 1.5,
          3.14159 / 2,
          false,
          paint,
        );
      } else if (isTopRight) {
        canvas.drawArc(
          Rect.fromCircle(center: start, radius: radius),
          3.14159,
          3.14159 / 2,
          false,
          paint,
        );
      } else if (isBottomLeft) {
        canvas.drawArc(
          Rect.fromCircle(center: start, radius: radius),
          0,
          3.14159 / 2,
          false,
          paint,
        );
      } else {
        canvas.drawArc(
          Rect.fromCircle(center: start, radius: radius),
          3.14159 / 2,
          3.14159 / 2,
          false,
          paint,
        );
      }
    }
  }

  void _drawCenterPattern(
      Canvas canvas, Paint paint, Offset center, Size size) {
    final radius = size.width < size.height ? size.width / 6 : size.height / 6;

    // رسم دائرة في المنتصف
    canvas.drawCircle(center, radius, paint);
    canvas.drawCircle(center, radius * 0.7, paint);
    canvas.drawCircle(center, radius * 0.4, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
